extern "C" {
    #include "avcodec.h"
    #include "codec_internal.h"
    #include "libavcodec/internal.h"
    #include "libavutil/avutil.h"
    #include "libavutil/internal.h"
    #include "libavutil/pixdesc.h"
    #include "decode.h"
}

#include "jpeg2000_kakadu.hpp"

#include <cinttypes>
#include <array>


av_cold int jpeg2000_kakadu_decode_init(AVCodecContext *avctx)
{
    Jpeg2000KakaduDecoderContext *ctx = static_cast<Jpeg2000KakaduDecoderContext*>(avctx->priv_data);

    ctx->thread_env       = new kdu_core::kdu_thread_env();
    ctx->packet_interface = new bbright::AVPacketKakaduInterface();
    ctx->codestream       = new kdu_core::kdu_codestream();
    ctx->decompressor     = new kdu_supp::kdu_stripe_decompressor();

    ctx->thread_env->create();
    if( avctx->thread_count > 0 ){
        for(int nt=1; nt < avctx->thread_count; ++nt){
            if( !ctx->thread_env->add_thread() ){
                avctx->thread_count = nt;  // Unable to create all the requested threads
                av_log(avctx, AV_LOG_WARNING, "can't create more than %d decoding threads", nt);
                break;
            }
        }
    }

    return 0;
}

int jpeg2000_kakadu_receive_frame(AVCodecContext *avctx, AVFrame *frame)
{
    Jpeg2000KakaduDecoderContext *ctx = static_cast<Jpeg2000KakaduDecoderContext*>(avctx->priv_data);

    if( frame == nullptr ){
        frame = av_frame_alloc();
    }

    AVPacket pkt = { 0 };
    int err = ff_decode_get_packet(avctx, &pkt);
    if( (err < 0)  &&  (err != AVERROR_EOF) ){
        return err;
    }

    if( err == AVERROR_EOF ){
        av_packet_unref(&pkt);
        return AVERROR_EOF;
    }

    if( avctx->extradata == nullptr ){
        av_log(avctx, AV_LOG_ERROR, "missing informations from mpegts PMT");
        av_packet_unref(&pkt);
        return AVERROR_INVALIDDATA;
    }else{
        // TODO: use extract_extradata() ??
        const bool interlaced            = (*avctx->extradata >> 2) & 0x01;
        const bool extended_capabilities = (*avctx->extradata >> 1) & 0x01;
        const bool stripped              = (*avctx->extradata >> 0) & 0x01;

        if( ctx->packet_interface->use_packet(pkt, interlaced, extended_capabilities, stripped) == true ){
            av_log(avctx, AV_LOG_ERROR, "error while parsing j2k_elsm header");
            av_packet_unref(&pkt);
            return AVERROR_INVALIDDATA;
        }

        ctx->codestream->create(ctx->packet_interface, ctx->thread_env);
        ctx->codestream->set_resilient(true);  // TODO
        ctx->codestream->apply_input_restrictions(0, 3, 0, 0, nullptr, kdu_core::KDU_WANT_CODESTREAM_COMPONENTS, ctx->thread_env);  // 3 components max = ignore alpha channels


        // get j2k values
        const int num_components = ctx->codestream->get_num_components();
        if( num_components != 3 ){
            av_log(avctx, AV_LOG_ERROR, "invalid number of components (%d), ignore this packet", num_components);
            av_packet_unref(&pkt);
            return AVERROR_INVALIDDATA;
        }

        std::array<int,                3> bit_depth;
        std::array<kdu_core::kdu_dims, 3> dim;
        using subsampling_t = std::pair<decltype(kdu_core::kdu_coords::x), decltype(kdu_core::kdu_coords::y)>;
        std::array<subsampling_t,      3> subsampling_log2_factor;
        for(int i=0; i<num_components; ++i){
            bit_depth[i] = ctx->codestream->get_bit_depth(i);
            ctx->codestream->get_dims(i, dim[i]);

            kdu_core::kdu_coords coords;
            ctx->codestream->get_subsampling(i, coords);
            subsampling_log2_factor[i] = {coords.x, coords.y};
        }

        // check components coherency
        if( (bit_depth[0] != bit_depth[1])  || (bit_depth[0] != bit_depth[2]) ){
            av_log(avctx, AV_LOG_ERROR, "components bit depths are differents (y=%d,u=%d,v=%d)", bit_depth[0], bit_depth[1], bit_depth[2]);
            av_packet_unref(&pkt);
            return AVERROR_INVALIDDATA;
        }
        if( (subsampling_log2_factor[1].first != subsampling_log2_factor[2].first)  ||  (subsampling_log2_factor[1].second != subsampling_log2_factor[2].second) ){
            av_log(avctx, AV_LOG_ERROR, "sumbsamplings are differents (u=(%d,%d),v=(%d,%d)", 
                    subsampling_log2_factor[1].first, subsampling_log2_factor[1].second,
                    subsampling_log2_factor[2].first, subsampling_log2_factor[2].second);
            av_packet_unref(&pkt);
            return AVERROR_INVALIDDATA;
        }

        // guess the pixel format
        AVPixelFormat pixel_format = AV_PIX_FMT_NONE;
        switch( bit_depth[0] ){
        case  8:
                 if( (subsampling_log2_factor[1].first == 1)  &&  (subsampling_log2_factor[1].second == 1) ) pixel_format = AV_PIX_FMT_YUV444P;
            else if( (subsampling_log2_factor[1].first == 2)  &&  (subsampling_log2_factor[1].second == 1) ) pixel_format = AV_PIX_FMT_YUV422P;
            else if( (subsampling_log2_factor[1].first == 2)  &&  (subsampling_log2_factor[1].second == 2) ) pixel_format = AV_PIX_FMT_YUV420P;
                break;
        case 10:
                 if( (subsampling_log2_factor[1].first == 1)  &&  (subsampling_log2_factor[1].second == 1) ) pixel_format = AV_PIX_FMT_YUV444P10LE;
            else if( (subsampling_log2_factor[1].first == 2)  &&  (subsampling_log2_factor[1].second == 1) ) pixel_format = AV_PIX_FMT_YUV422P10LE;
            else if( (subsampling_log2_factor[1].first == 2)  &&  (subsampling_log2_factor[1].second == 2) ) pixel_format = AV_PIX_FMT_YUV420P10LE;
                break;
        case 12:
                 if( (subsampling_log2_factor[1].first == 1)  &&  (subsampling_log2_factor[1].second == 1) ) pixel_format = AV_PIX_FMT_YUV444P12LE;
            else if( (subsampling_log2_factor[1].first == 2)  &&  (subsampling_log2_factor[1].second == 1) ) pixel_format = AV_PIX_FMT_YUV422P12LE;
            else if( (subsampling_log2_factor[1].first == 2)  &&  (subsampling_log2_factor[1].second == 2) ) pixel_format = AV_PIX_FMT_YUV420P12LE;
                break;
        case 16:
                 if( (subsampling_log2_factor[1].first == 1)  &&  (subsampling_log2_factor[1].second == 1) ) pixel_format = AV_PIX_FMT_YUV444P16LE;
            else if( (subsampling_log2_factor[1].first == 2)  &&  (subsampling_log2_factor[1].second == 1) ) pixel_format = AV_PIX_FMT_YUV422P16LE;
            else if( (subsampling_log2_factor[1].first == 2)  &&  (subsampling_log2_factor[1].second == 2) ) pixel_format = AV_PIX_FMT_YUV420P16LE;
                break;
        }

        if( pixel_format == AV_PIX_FMT_NONE ){
            const char *fmt_str = av_get_pix_fmt_name(pixel_format);
            if( fmt_str != nullptr ){
                av_log(avctx, AV_LOG_ERROR, "pixel format %s not supported", fmt_str);
            }else{
                av_log(avctx, AV_LOG_ERROR, "invalid pixel format");
            }
            av_packet_unref(&pkt);
            return AVERROR_INVALIDDATA;
        }

        // update avctx
        avctx->pix_fmt = pixel_format;
        avctx->width   = dim[0].size.get_x();
        avctx->height  = dim[0].size.get_y() * (interlaced?2:1);

        frame->format = static_cast<int>(pixel_format);
        if (interlaced) {
            frame->flags |= AV_FRAME_FLAG_INTERLACED;
            frame->flags |= AV_FRAME_FLAG_TOP_FIELD_FIRST;
        }
        frame->pict_type = AV_PICTURE_TYPE_I;
        frame->flags |= AV_FRAME_FLAG_KEY;
        frame->pts = pkt.pts;

        // allocate avframe's buffers
        if ( (err = ff_get_buffer(avctx, frame, 0)) < 0 ) {
            av_packet_unref(&pkt);
            return err;
        }

        std::array<int,3>  sample_gap = {1, 1, 1};
        std::array<int,3>  row_gap    = {frame->linesize[0], frame->linesize[1], frame->linesize[2]};
        std::array<int,3>  precision  = {ctx->codestream->get_bit_depth(0), ctx->codestream->get_bit_depth(1), ctx->codestream->get_bit_depth(2)};
        std::array<bool,3> is_signed  = {ctx->codestream->get_signed(0),    ctx->codestream->get_signed(1),    ctx->codestream->get_signed(2)   };

        uint8_t pass = 0;
        int     line = pass;
        bool    frame_complete = false;
        do{
            ctx->decompressor->start(*ctx->codestream, false, true, ctx->thread_env);

            line = interlaced? pass : line;

            std::array<int,3> stripe_height     = {1, 1, 1};
            std::array<int,3> max_stripe_height = {1, 1, 1};
            ctx->decompressor->get_recommended_stripe_heights(1, dim[0].size.y, stripe_height.data(), max_stripe_height.data());  // TODO: get_recommended_stripe_heights(dims.size.y, 1024 ... ?

            bool continue_pull_stripe = true;
            while( continue_pull_stripe ){

                if( line >= frame->height ){  // security
                    frame_complete = true;
                    break;
                }
                
                std::array<kdu_core::kdu_int16*,3> res = {
                    reinterpret_cast<kdu_core::kdu_int16*>(&frame->data[0][line*frame->linesize[0]]),
                    reinterpret_cast<kdu_core::kdu_int16*>(&frame->data[1][line*frame->linesize[1]]),
                    reinterpret_cast<kdu_core::kdu_int16*>(&frame->data[2][line*frame->linesize[2]])
                };
                continue_pull_stripe = ctx->decompressor->pull_stripe(res.data(), stripe_height.data(), sample_gap.data(), row_gap.data(), precision.data(), is_signed.data());

                line += (interlaced?2:1)*stripe_height[0];
            }

            ctx->decompressor->finish();
            ctx->codestream->destroy();

            if( ctx->packet_interface->next_stripe() != false ){
                frame_complete = true;
            }else{
                ctx->codestream->create(ctx->packet_interface, ctx->thread_env);
            }

            ++pass;

        }while( !frame_complete );
    }

    
    av_packet_unref(&pkt);

    return 0;
}

av_cold int jpeg2000_kakadu_close(AVCodecContext *avctx)
{
    Jpeg2000KakaduDecoderContext *ctx = static_cast<Jpeg2000KakaduDecoderContext*>(avctx->priv_data);

    int ret = ctx->thread_env->destroy();
    if( ret == false ){
        av_log(avctx, AV_LOG_ERROR, "failed to destroy thread_env");
    }

    delete ctx->thread_env;
    delete ctx->packet_interface;
    delete ctx->codestream;
    delete ctx->decompressor;

    return 0;
}

// #define OFFSET(x) offsetof(Jpeg2000KakaduDecoderContext, x)
// #define VD AV_OPT_FLAG_VIDEO_PARAM | AV_OPT_FLAG_DECODING_PARAM

// static const AVOption options[] = {
//     { "lowres",  "Lower the decoding resolution by a power of two",
//         OFFSET(reduction_factor), AV_OPT_TYPE_INT, { .i64 = 0 }, 0, 34 - 1, VD },
//     { NULL },
// };

static const AVClass jpeg2000_kakadu_class = {
    .class_name = "jpeg2000_kakadu",
    .item_name  = av_default_item_name,
    // .option     = options,
    .version    = LIBAVUTIL_VERSION_INT,
};


// C++ compilation needs to declare this structure as extern to export the associated symbol while standard C compilation doesn't need to, I don't know why
extern const FFCodec ff_jpeg2000_kakadu_decoder = {
    .p = {
        .name         = "jpeg2000_kakadu",
        .long_name    = NULL_IF_CONFIG_SMALL("JPEG 2000 Kakadu"),
        .type         = AVMEDIA_TYPE_VIDEO,
        .id           = AV_CODEC_ID_JPEG2000,
        .capabilities = AV_CODEC_CAP_DR1 | AV_CODEC_CAP_OTHER_THREADS,
        .priv_class   = &jpeg2000_kakadu_class,
    },

    .caps_internal    = FF_CODEC_CAP_AUTO_THREADS,
    .cb_type = FF_CODEC_CB_TYPE_RECEIVE_FRAME,  // can't use the macro FF_CODEC_RECEIVE_FRAME_CB() in C++, need to declare the callback manually
    .priv_data_size = sizeof(Jpeg2000KakaduDecoderContext),
    .init = jpeg2000_kakadu_decode_init,
    .cb = {
        .receive_frame = jpeg2000_kakadu_receive_frame,
    },
    .close = jpeg2000_kakadu_close,
};
