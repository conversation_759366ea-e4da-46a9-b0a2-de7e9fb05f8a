=encoding utf8

=head1 NAME

ffmpeg-scaler - FFmpeg video scaling and pixel format converter

=head1 DESCRIPTION


The FFmpeg rescaler provides a high-level interface to the libswscale
library image conversion utilities. In particular it allows one to perform
image rescaling and pixel format conversion.




=head1 SCALER OPTIONS


The video scaler supports the following named options.

Options may be set by specifying -I<option> I<value> in the
FFmpeg tools, with a few API-only exceptions noted below.
For programmatic use, they can be set explicitly in the
C<SwsContext> options or through the F<libavutil/opt.h> API.


=over 4




=item B<sws_flags>

Set the scaler flags. This is also used to set the scaling
algorithm. Only a single algorithm should be selected. Default
value is B<bicubic>.

It accepts the following values:

=over 4


=item B<fast_bilinear>

Select fast bilinear scaling algorithm.


=item B<bilinear>

Select bilinear scaling algorithm.


=item B<bicubic>

Select bicubic scaling algorithm.


=item B<experimental>

Select experimental scaling algorithm.


=item B<neighbor>

Select nearest neighbor rescaling algorithm.


=item B<area>

Select averaging area rescaling algorithm.


=item B<bicublin>

Select bicubic scaling algorithm for the luma component, bilinear for
chroma components.


=item B<gauss>

Select Gaussian rescaling algorithm.


=item B<sinc>

Select sinc rescaling algorithm.


=item B<lanczos>

Select Lanczos rescaling algorithm. The default width (alpha) is 3 and can be
changed by setting C<param0>.


=item B<spline>

Select natural bicubic spline rescaling algorithm.


=item B<print_info>

Enable printing/debug logging.


=item B<accurate_rnd>

Enable accurate rounding.


=item B<full_chroma_int>

Enable full chroma interpolation.


=item B<full_chroma_inp>

Select full chroma input.


=item B<bitexact>

Enable bitexact output.

=back



=item B<srcw> I<(API only)>

Set source width.


=item B<srch> I<(API only)>

Set source height.


=item B<dstw> I<(API only)>

Set destination width.


=item B<dsth> I<(API only)>

Set destination height.


=item B<src_format> I<(API only)>

Set source pixel format (must be expressed as an integer).


=item B<dst_format> I<(API only)>

Set destination pixel format (must be expressed as an integer).


=item B<src_range> I<(boolean)>

If value is set to C<1>, indicates source is full range. Default value is
C<0>, which indicates source is limited range.


=item B<dst_range> I<(boolean)>

If value is set to C<1>, enable full range for destination. Default value
is C<0>, which enables limited range.


=item B<gamma> I<(boolean)>

If value is set to C<1>, enable gamma correct scaling. Default value is C<0>.



=item B<param0, param1>

Set scaling algorithm parameters. The specified values are specific of
some scaling algorithms and ignored by others. The specified values
are floating point number values.


=item B<sws_dither>

Set the dithering algorithm. Accepts one of the following
values. Default value is B<auto>.


=over 4


=item B<auto>

automatic choice


=item B<none>

no dithering


=item B<bayer>

bayer dither


=item B<ed>

error diffusion dither


=item B<a_dither>

arithmetic dither, based using addition


=item B<x_dither>

arithmetic dither, based using xor (more random/less apparent patterning that
a_dither).


=back



=item B<alphablend>

Set the alpha blending to use when the input has alpha but the output does not.
Default value is B<none>.


=over 4


=item B<uniform_color>

Blend onto a uniform background color


=item B<checkerboard>

Blend onto a checkerboard


=item B<none>

No blending


=back



=back




=head1 SEE ALSO



ffmpeg(1), ffplay(1), ffprobe(1), libswscale(3)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



