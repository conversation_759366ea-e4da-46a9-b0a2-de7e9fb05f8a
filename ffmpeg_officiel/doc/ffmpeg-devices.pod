=encoding utf8

=head1 NAME

ffmpeg-devices - FFmpeg devices

=head1 DESCRIPTION


This document describes the input and output devices provided by the
libavdevice library.



=head1 DEVICE OPTIONS


The libavdevice library provides the same interface as
libavformat. Namely, an input device is considered like a demuxer, and
an output device like a muxer, and the interface and generic device
options are the same provided by libavformat (see the ffmpeg-formats
manual).

In addition each input or output device may support so-called private
options, which are specific for that component.

Options may be set by specifying -I<option> I<value> in the
FFmpeg tools, or by setting the value explicitly in the device
C<AVFormatContext> options or using the F<libavutil/opt.h> API
for programmatic use.



=head1 INPUT DEVICES


Input devices are configured elements in FFmpeg which enable accessing
the data coming from a multimedia device attached to your system.

When you configure your FFmpeg build, all the supported input devices
are enabled by default. You can list all available ones using the
configure option "--list-indevs".

You can disable all the input devices using the configure option
"--disable-indevs", and selectively enable an input device using the
option "--enable-indev=I<INDEV>", or you can disable a particular
input device using the option "--disable-indev=I<INDEV>".

The option "-devices" of the ff* tools will display the list of
supported input devices.

A description of the currently available input devices follows.


=head2 alsa


ALSA (Advanced Linux Sound Architecture) input device.

To enable this input device during configuration you need libasound
installed on your system.

This device allows capturing from an ALSA device. The name of the
device to capture has to be an ALSA card identifier.

An ALSA identifier has the syntax:
	
	hw:<CARD>[,<DEV>[,<SUBDEV>]]


where the I<DEV> and I<SUBDEV> components are optional.

The three arguments (in order: I<CARD>,I<DEV>,I<SUBDEV>)
specify card number or identifier, device number and subdevice number
(-1 means any).

To see the list of cards currently recognized by your system check the
files F</proc/asound/cards> and F</proc/asound/devices>.

For example to capture with B<ffmpeg> from an ALSA device with
card id 0, you may run the command:
	
	ffmpeg -f alsa -i hw:0 alsaout.wav


For more information see:
E<lt>B<http://www.alsa-project.org/alsa-doc/alsa-lib/pcm.html>E<gt>


=head3 Options



=over 4



=item B<sample_rate>

Set the sample rate in Hz. Default is 48000.


=item B<channels>

Set the number of channels. Default is 2.


=back



=head2 android_camera


Android camera input device.

This input devices uses the Android Camera2 NDK API which is
available on devices with API level 24+. The availability of
android_camera is autodetected during configuration.

This device allows capturing from all cameras on an Android device,
which are integrated into the Camera2 NDK API.

The available cameras are enumerated internally and can be selected
with the I<camera_index> parameter. The input file string is
discarded.

Generally the back facing camera has index 0 while the front facing
camera has index 1.


=head3 Options



=over 4



=item B<video_size>

Set the video size given as a string such as 640x480 or hd720.
Falls back to the first available configuration reported by
Android if requested video size is not available or by default.


=item B<framerate>

Set the video framerate.
Falls back to the first available configuration reported by
Android if requested framerate is not available or by default (-1).


=item B<camera_index>

Set the index of the camera to use. Default is 0.


=item B<input_queue_size>

Set the maximum number of frames to buffer. Default is 5.


=back



=head2 avfoundation


AVFoundation input device.

AVFoundation is the currently recommended framework by Apple for streamgrabbing on OSX E<gt>= 10.7 as well as on iOS.

The input filename has to be given in the following syntax:
	
	-i "[[VIDEO]:[AUDIO]]"

The first entry selects the video input while the latter selects the audio input.
The stream has to be specified by the device name or the device index as shown by the device list.
Alternatively, the video and/or audio input device can be chosen by index using the

    B<-video_device_index E<lt>INDEXE<gt>>

and/or

    B<-audio_device_index E<lt>INDEXE<gt>>

, overriding any
device name or index given in the input filename.

All available devices can be enumerated by using B<-list_devices true>, listing
all device names and corresponding indices.

There are two device name aliases:

=over 4



=item C<default>

Select the AVFoundation default device of the corresponding type.


=item C<none>

Do not record the corresponding media type.
This is equivalent to specifying an empty device name or index.


=back



=head3 Options


AVFoundation supports the following options:


=over 4



=item B<-list_devices E<lt>TRUE|FALSEE<gt>>

If set to true, a list of all available input devices is given showing all
device names and indices.


=item B<-video_device_index E<lt>INDEXE<gt>>

Specify the video device by its index. Overrides anything given in the input filename.


=item B<-audio_device_index E<lt>INDEXE<gt>>

Specify the audio device by its index. Overrides anything given in the input filename.


=item B<-pixel_format E<lt>FORMATE<gt>>

Request the video device to use a specific pixel format.
If the specified format is not supported, a list of available formats is given
and the first one in this list is used instead. Available pixel formats are:
C<monob, rgb555be, rgb555le, rgb565be, rgb565le, rgb24, bgr24, 0rgb, bgr0, 0bgr, rgb0,
 bgr48be, uyvy422, yuva444p, yuva444p16le, yuv444p, yuv422p16, yuv422p10, yuv444p10,
 yuv420p, nv12, yuyv422, gray>


=item B<-framerate>

Set the grabbing frame rate. Default is C<ntsc>, corresponding to a
frame rate of C<30000/1001>.


=item B<-video_size>

Set the video frame size.


=item B<-capture_cursor>

Capture the mouse pointer. Default is 0.


=item B<-capture_mouse_clicks>

Capture the screen mouse clicks. Default is 0.


=item B<-capture_raw_data>

Capture the raw device data. Default is 0.
Using this option may result in receiving the underlying data delivered to the AVFoundation framework. E.g. for muxed devices that sends raw DV data to the framework (like tape-based camcorders), setting this option to false results in extracted video frames captured in the designated pixel format only. Setting this option to true results in receiving the raw DV stream untouched.

=back



=head3 Examples



=over 4



=item *

Print the list of AVFoundation supported devices and exit:
	
	$ ffmpeg -f avfoundation -list_devices true -i ""



=item *

Record video from video device 0 and audio from audio device 0 into out.avi:
	
	$ ffmpeg -f avfoundation -i "0:0" out.avi



=item *

Record video from video device 2 and audio from audio device 1 into out.avi:
	
	$ ffmpeg -f avfoundation -video_device_index 2 -i ":1" out.avi



=item *

Record video from the system default video device using the pixel format bgr0 and do not record any audio into out.avi:
	
	$ ffmpeg -f avfoundation -pixel_format bgr0 -i "default:none" out.avi



=item *

Record raw DV data from a suitable input device and write the output into out.dv:
	
	$ ffmpeg -f avfoundation -capture_raw_data true -i "zr100:none" out.dv




=back



=head2 decklink


The decklink input device provides capture capabilities for Blackmagic
DeckLink devices.

To enable this input device, you need the Blackmagic DeckLink SDK and you
need to configure with the appropriate C<--extra-cflags>
and C<--extra-ldflags>.
On Windows, you need to run the IDL files through B<widl>.

DeckLink is very picky about the formats it supports. Pixel format of the
input can be set with B<raw_format>.
Framerate and video size must be determined for your device with
B<-list_formats 1>. Audio sample rate is always 48 kHz and the number
of channels can be 2, 8 or 16. Note that all audio channels are bundled in one single
audio track.


=head3 Options



=over 4



=item B<list_devices>

If set to B<true>, print a list of devices and exit.
Defaults to B<false>. This option is deprecated, please use the
C<-sources> option of ffmpeg to list the available input devices.


=item B<list_formats>

If set to B<true>, print a list of supported formats and exit.
Defaults to B<false>.


=item B<format_code E<lt>FourCCE<gt>>

This sets the input video format to the format given by the FourCC. To see
the supported values of your device(s) use B<list_formats>.
Note that there is a FourCC B<'pal '> that can also be used
as B<pal> (3 letters).
Default behavior is autodetection of the input video format, if the hardware
supports it.


=item B<raw_format>

Set the pixel format of the captured video.
Available values are:

=over 4


=item B<auto>


This is the default which means 8-bit YUV 422 or 8-bit ARGB if format
autodetection is used, 8-bit YUV 422 otherwise.


=item B<uyvy422>


8-bit YUV 422.


=item B<yuv422p10>


10-bit YUV 422.


=item B<argb>


8-bit RGB.


=item B<bgra>


8-bit RGB.


=item B<rgb10>


10-bit RGB.


=back



=item B<teletext_lines>

If set to nonzero, an additional teletext stream will be captured from the
vertical ancillary data. Both SD PAL (576i) and HD (1080i or 1080p)
sources are supported. In case of HD sources, OP47 packets are decoded.

This option is a bitmask of the SD PAL VBI lines captured, specifically lines 6
to 22, and lines 318 to 335. Line 6 is the LSB in the mask. Selected lines
which do not contain teletext information will be ignored. You can use the
special B<all> constant to select all possible lines, or
B<standard> to skip lines 6, 318 and 319, which are not compatible with
all receivers.

For SD sources, ffmpeg needs to be compiled with C<--enable-libzvbi>. For
HD sources, on older (pre-4K) DeckLink card models you have to capture in 10
bit mode.


=item B<channels>

Defines number of audio channels to capture. Must be B<2>, B<8> or B<16>.
Defaults to B<2>.


=item B<duplex_mode>

Sets the decklink device duplex/profile mode. Must be B<unset>, B<half>, B<full>,
B<one_sub_device_full>, B<one_sub_device_half>, B<two_sub_device_full>,
B<four_sub_device_half>
Defaults to B<unset>.

Note: DeckLink SDK 11.0 have replaced the duplex property by a profile property.
For the DeckLink Duo 2 and DeckLink Quad 2, a profile is shared between any 2
sub-devices that utilize the same connectors. For the DeckLink 8K Pro, a profile
is shared between all 4 sub-devices. So DeckLink 8K Pro support four profiles.

Valid profile modes for DeckLink 8K Pro(with DeckLink SDK E<gt>= 11.0):
B<one_sub_device_full>, B<one_sub_device_half>, B<two_sub_device_full>,
B<four_sub_device_half>

Valid profile modes for DeckLink Quad 2 and DeckLink Duo 2:
B<half>, B<full>


=item B<timecode_format>

Timecode type to include in the frame and video stream metadata. Must be
B<none>, B<rp188vitc>, B<rp188vitc2>, B<rp188ltc>,
B<rp188hfr>, B<rp188any>, B<vitc>, B<vitc2>, or B<serial>.
Defaults to B<none> (not included).

In order to properly support 50/60 fps timecodes, the ordering of the queried
timecode types for B<rp188any> is HFR, VITC1, VITC2 and LTC for E<gt>30 fps
content. Note that this is slightly different to the ordering used by the
DeckLink API, which is HFR, VITC1, LTC, VITC2.


=item B<video_input>

Sets the video input source. Must be B<unset>, B<sdi>, B<hdmi>,
B<optical_sdi>, B<component>, B<composite> or B<s_video>.
Defaults to B<unset>.


=item B<audio_input>

Sets the audio input source. Must be B<unset>, B<embedded>,
B<aes_ebu>, B<analog>, B<analog_xlr>, B<analog_rca> or
B<microphone>. Defaults to B<unset>.


=item B<video_pts>

Sets the video packet timestamp source. Must be B<video>, B<audio>,
B<reference>, B<wallclock> or B<abs_wallclock>.
Defaults to B<video>.


=item B<audio_pts>

Sets the audio packet timestamp source. Must be B<video>, B<audio>,
B<reference>, B<wallclock> or B<abs_wallclock>.
Defaults to B<audio>.


=item B<draw_bars>

If set to B<true>, color bars are drawn in the event of a signal loss.
Defaults to B<true>.
This option is deprecated, please use the C<signal_loss_action> option.


=item B<signal_loss_action>

Sets the action to take in the event of a signal loss. Accepts one of the
following values:


=over 4


=item B<1, none>

Do nothing on signal loss. This usually results in black frames.

=item B<2, bars>

Draw color bars on signal loss. Only supported for 8-bit input signals.

=item B<3, repeat>

Repeat the last video frame on signal loss.

=back


Defaults to B<bars>.


=item B<queue_size>

Sets maximum input buffer size in bytes. If the buffering reaches this value,
incoming frames will be dropped.
Defaults to B<1073741824>.


=item B<audio_depth>

Sets the audio sample bit depth. Must be B<16> or B<32>.
Defaults to B<16>.


=item B<decklink_copyts>

If set to B<true>, timestamps are forwarded as they are without removing
the initial offset.
Defaults to B<false>.


=item B<timestamp_align>

Capture start time alignment in seconds. If set to nonzero, input frames are
dropped till the system timestamp aligns with configured value.
Alignment difference of up to one frame duration is tolerated.
This is useful for maintaining input synchronization across N different
hardware devices deployed for 'N-way' redundancy. The system time of different
hardware devices should be synchronized with protocols such as NTP or PTP,
before using this option.
Note that this method is not foolproof. In some border cases input
synchronization may not happen due to thread scheduling jitters in the OS.
Either sync could go wrong by 1 frame or in a rarer case
B<timestamp_align> seconds.
Defaults to B<0>.


=item B<wait_for_tc (>I<bool>B<)>

Drop frames till a frame with timecode is received. Sometimes serial timecode
isn't received with the first input frame. If that happens, the stored stream
timecode will be inaccurate. If this option is set to B<true>, input frames
are dropped till a frame with timecode is received.
Option I<timecode_format> must be specified.
Defaults to B<false>.


=item B<enable_klv(>I<bool>B<)>

If set to B<true>, extracts KLV data from VANC and outputs KLV packets.
KLV VANC packets are joined based on MID and PSC fields and aggregated into
one KLV packet.
Defaults to B<false>.


=back



=head3 Examples



=over 4



=item *

List input devices:
	
	ffmpeg -sources decklink



=item *

List supported formats:
	
	ffmpeg -f decklink -list_formats 1 -i 'Intensity Pro'



=item *

Capture video clip at 1080i50:
	
	ffmpeg -format_code Hi50 -f decklink -i 'Intensity Pro' -c:a copy -c:v copy output.avi



=item *

Capture video clip at 1080i50 10 bit:
	
	ffmpeg -raw_format yuv422p10 -format_code Hi50 -f decklink -i 'UltraStudio Mini Recorder' -c:a copy -c:v copy output.avi



=item *

Capture video clip at 1080i50 with 16 audio channels:
	
	ffmpeg -channels 16 -format_code Hi50 -f decklink -i 'UltraStudio Mini Recorder' -c:a copy -c:v copy output.avi



=back



=head2 dshow


Windows DirectShow input device.

DirectShow support is enabled when FFmpeg is built with the mingw-w64 project.
Currently only audio and video devices are supported.

Multiple devices may be opened as separate inputs, but they may also be
opened on the same input, which should improve synchronism between them.

The input name should be in the format:

	
	<TYPE>=<NAME>[:<TYPE>=<NAME>]


where I<TYPE> can be either I<audio> or I<video>,
and I<NAME> is the device's name or alternative name..


=head3 Options


If no options are specified, the device's defaults are used.
If the device does not support the requested options, it will
fail to open.


=over 4



=item B<video_size>

Set the video size in the captured video.


=item B<framerate>

Set the frame rate in the captured video.


=item B<sample_rate>

Set the sample rate (in Hz) of the captured audio.


=item B<sample_size>

Set the sample size (in bits) of the captured audio.


=item B<channels>

Set the number of channels in the captured audio.


=item B<list_devices>

If set to B<true>, print a list of devices and exit.


=item B<list_options>

If set to B<true>, print a list of selected device's options
and exit.


=item B<video_device_number>

Set video device number for devices with the same name (starts at 0,
defaults to 0).


=item B<audio_device_number>

Set audio device number for devices with the same name (starts at 0,
defaults to 0).


=item B<pixel_format>

Select pixel format to be used by DirectShow. This may only be set when
the video codec is not set or set to rawvideo.


=item B<audio_buffer_size>

Set audio device buffer size in milliseconds (which can directly
impact latency, depending on the device).
Defaults to using the audio device's
default buffer size (typically some multiple of 500ms).
Setting this value too low can degrade performance.
See also
E<lt>B<http://msdn.microsoft.com/en-us/library/windows/desktop/dd377582(v=vs.85).aspx>E<gt>


=item B<video_pin_name>

Select video capture pin to use by name or alternative name.


=item B<audio_pin_name>

Select audio capture pin to use by name or alternative name.


=item B<crossbar_video_input_pin_number>

Select video input pin number for crossbar device. This will be
routed to the crossbar device's Video Decoder output pin.
Note that changing this value can affect future invocations
(sets a new default) until system reboot occurs.


=item B<crossbar_audio_input_pin_number>

Select audio input pin number for crossbar device. This will be
routed to the crossbar device's Audio Decoder output pin.
Note that changing this value can affect future invocations
(sets a new default) until system reboot occurs.


=item B<show_video_device_dialog>

If set to B<true>, before capture starts, popup a display dialog
to the end user, allowing them to change video filter properties
and configurations manually.
Note that for crossbar devices, adjusting values in this dialog
may be needed at times to toggle between PAL (25 fps) and NTSC (29.97)
input frame rates, sizes, interlacing, etc.  Changing these values can
enable different scan rates/frame rates and avoiding green bars at
the bottom, flickering scan lines, etc.
Note that with some devices, changing these properties can also affect future
invocations (sets new defaults) until system reboot occurs.


=item B<show_audio_device_dialog>

If set to B<true>, before capture starts, popup a display dialog
to the end user, allowing them to change audio filter properties
and configurations manually.


=item B<show_video_crossbar_connection_dialog>

If set to B<true>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify crossbar pin routings, when it opens a video device.


=item B<show_audio_crossbar_connection_dialog>

If set to B<true>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify crossbar pin routings, when it opens an audio device.


=item B<show_analog_tv_tuner_dialog>

If set to B<true>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify TV channels and frequencies.


=item B<show_analog_tv_tuner_audio_dialog>

If set to B<true>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify TV audio (like mono vs. stereo, Language A,B or C).


=item B<audio_device_load>

Load an audio capture filter device from file instead of searching
it by name. It may load additional parameters too, if the filter
supports the serialization of its properties to.
To use this an audio capture source has to be specified, but it can
be anything even fake one.


=item B<audio_device_save>

Save the currently used audio capture filter device and its
parameters (if the filter supports it) to a file.
If a file with the same name exists it will be overwritten.


=item B<video_device_load>

Load a video capture filter device from file instead of searching
it by name. It may load additional parameters too, if the filter
supports the serialization of its properties to.
To use this a video capture source has to be specified, but it can
be anything even fake one.


=item B<video_device_save>

Save the currently used video capture filter device and its
parameters (if the filter supports it) to a file.
If a file with the same name exists it will be overwritten.


=item B<use_video_device_timestamps>

If set to B<false>, the timestamp for video frames will be
derived from the wallclock instead of the timestamp provided by
the capture device. This allows working around devices that
provide unreliable timestamps.


=back



=head3 Examples



=over 4



=item *

Print the list of DirectShow supported devices and exit:
	
	$ ffmpeg -list_devices true -f dshow -i dummy



=item *

Open video device I<Camera>:
	
	$ ffmpeg -f dshow -i video="Camera"



=item *

Open second video device with name I<Camera>:
	
	$ ffmpeg -f dshow -video_device_number 1 -i video="Camera"



=item *

Open video device I<Camera> and audio device I<Microphone>:
	
	$ ffmpeg -f dshow -i video="Camera":audio="Microphone"



=item *

Print the list of supported options in selected device and exit:
	
	$ ffmpeg -list_options true -f dshow -i video="Camera"



=item *

Specify pin names to capture by name or alternative name, specify alternative device name:
	
	$ ffmpeg -f dshow -audio_pin_name "Audio Out" -video_pin_name 2 -i video=video="@device_pnp_\\?\pci#ven_1a0a&dev_6200&subsys_62021461&rev_01#4&e2c7dd6&0&00e1#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\{ca465100-deb0-4d59-818f-8c477184adf6}":audio="Microphone"



=item *

Configure a crossbar device, specifying crossbar pins, allow user to adjust video capture properties at startup:
	
	$ ffmpeg -f dshow -show_video_device_dialog true -crossbar_video_input_pin_number 0
	     -crossbar_audio_input_pin_number 3 -i video="AVerMedia BDA Analog Capture":audio="AVerMedia BDA Analog Capture"



=back



=head2 fbdev


Linux framebuffer input device.

The Linux framebuffer is a graphic hardware-independent abstraction
layer to show graphics on a computer monitor, typically on the
console. It is accessed through a file device node, usually
F</dev/fb0>.

For more detailed information read the file
Documentation/fb/framebuffer.txt included in the Linux source tree.

See also E<lt>B<http://linux-fbdev.sourceforge.net/>E<gt>, and fbset(1).

To record from the framebuffer device F</dev/fb0> with
B<ffmpeg>:
	
	ffmpeg -f fbdev -framerate 10 -i /dev/fb0 out.avi


You can take a single screenshot image with the command:
	
	ffmpeg -f fbdev -framerate 1 -i /dev/fb0 -frames:v 1 screenshot.jpeg



=head3 Options



=over 4



=item B<framerate>

Set the frame rate. Default is 25.


=back



=head2 gdigrab


Win32 GDI-based screen capture device.

This device allows you to capture a region of the display on Windows.

Amongst options for the input filenames are such elements as:
	
	desktop

or
	
	title=<window_title>

or
	
	hwnd=<window_hwnd>


The first option will capture the entire desktop, or a fixed region of the
desktop. The second and third options will instead capture the contents of a single
window, regardless of its position on the screen.

For example, to grab the entire desktop using B<ffmpeg>:
	
	ffmpeg -f gdigrab -framerate 6 -i desktop out.mpg


Grab a 640x480 region at position C<10,20>:
	
	ffmpeg -f gdigrab -framerate 6 -offset_x 10 -offset_y 20 -video_size vga -i desktop out.mpg


Grab the contents of the window named "Calculator"
	
	ffmpeg -f gdigrab -framerate 6 -i title=Calculator out.mpg



=head3 Options



=over 4


=item B<draw_mouse>

Specify whether to draw the mouse pointer. Use the value C<0> to
not draw the pointer. Default value is C<1>.


=item B<framerate>

Set the grabbing frame rate. Default value is C<ntsc>,
corresponding to a frame rate of C<30000/1001>.


=item B<show_region>

Show grabbed region on screen.

If I<show_region> is specified with C<1>, then the grabbing
region will be indicated on screen. With this option, it is easy to
know what is being grabbed if only a portion of the screen is grabbed.

Note that I<show_region> is incompatible with grabbing the contents
of a single window.

For example:
	
	ffmpeg -f gdigrab -show_region 1 -framerate 6 -video_size cif -offset_x 10 -offset_y 20 -i desktop out.mpg



=item B<video_size>

Set the video frame size. The default is to capture the full screen if F<desktop> is selected, or the full window size if F<title=I<window_title>> is selected.


=item B<offset_x>

When capturing a region with I<video_size>, set the distance from the left edge of the screen or desktop.

Note that the offset calculation is from the top left corner of the primary monitor on Windows. If you have a monitor positioned to the left of your primary monitor, you will need to use a negative I<offset_x> value to move the region to that monitor.


=item B<offset_y>

When capturing a region with I<video_size>, set the distance from the top edge of the screen or desktop.

Note that the offset calculation is from the top left corner of the primary monitor on Windows. If you have a monitor positioned above your primary monitor, you will need to use a negative I<offset_y> value to move the region to that monitor.


=back



=head2 iec61883


FireWire DV/HDV input device using libiec61883.

To enable this input device, you need libiec61883, libraw1394 and
libavc1394 installed on your system. Use the configure option
C<--enable-libiec61883> to compile with the device enabled.

The iec61883 capture device supports capturing from a video device
connected via IEEE1394 (FireWire), using libiec61883 and the new Linux
FireWire stack (juju). This is the default DV/HDV input method in Linux
Kernel 2.6.37 and later, since the old FireWire stack was removed.

Specify the FireWire port to be used as input file, or "auto"
to choose the first port connected.


=head3 Options



=over 4



=item B<dvtype>

Override autodetection of DV/HDV. This should only be used if auto
detection does not work, or if usage of a different device type
should be prohibited. Treating a DV device as HDV (or vice versa) will
not work and result in undefined behavior.
The values B<auto>, B<dv> and B<hdv> are supported.


=item B<dvbuffer>

Set maximum size of buffer for incoming data, in frames. For DV, this
is an exact value. For HDV, it is not frame exact, since HDV does
not have a fixed frame size.


=item B<dvguid>

Select the capture device by specifying its GUID. Capturing will only
be performed from the specified device and fails if no device with the
given GUID is found. This is useful to select the input if multiple
devices are connected at the same time.
Look at /sys/bus/firewire/devices to find out the GUIDs.


=back



=head3 Examples



=over 4



=item *

Grab and show the input of a FireWire DV/HDV device.
	
	ffplay -f iec61883 -i auto



=item *

Grab and record the input of a FireWire DV/HDV device,
using a packet buffer of 100000 packets if the source is HDV.
	
	ffmpeg -f iec61883 -i auto -dvbuffer 100000 out.mpg



=back



=head2 jack


JACK input device.

To enable this input device during configuration you need libjack
installed on your system.

A JACK input device creates one or more JACK writable clients, one for
each audio channel, with name I<client_name>:input_I<N>, where
I<client_name> is the name provided by the application, and I<N>
is a number which identifies the channel.
Each writable client will send the acquired data to the FFmpeg input
device.

Once you have created one or more JACK readable clients, you need to
connect them to one or more JACK writable clients.

To connect or disconnect JACK clients you can use the B<jack_connect>
and B<jack_disconnect> programs, or do it through a graphical interface,
for example with B<qjackctl>.

To list the JACK clients and their properties you can invoke the command
B<jack_lsp>.

Follows an example which shows how to capture a JACK readable client
with B<ffmpeg>.
	
	# Create a JACK writable client with name "ffmpeg".
	$ ffmpeg -f jack -i ffmpeg -y out.wav
	
	# Start the sample jack_metro readable client.
	$ jack_metro -b 120 -d 0.2 -f 4000
	
	# List the current JACK clients.
	$ jack_lsp -c
	system:capture_1
	system:capture_2
	system:playback_1
	system:playback_2
	ffmpeg:input_1
	metro:120_bpm
	
	# Connect metro to the ffmpeg writable client.
	$ jack_connect metro:120_bpm ffmpeg:input_1


For more information read:
E<lt>B<http://jackaudio.org/>E<gt>


=head3 Options



=over 4



=item B<channels>

Set the number of channels. Default is 2.


=back



=head2 kmsgrab


KMS video input device.

Captures the KMS scanout framebuffer associated with a specified CRTC or plane as a
DRM object that can be passed to other hardware functions.

Requires either DRM master or CAP_SYS_ADMIN to run.

If you don't understand what all of that means, you probably don't want this.  Look at
B<x11grab> instead.


=head3 Options



=over 4



=item B<device>

DRM device to capture on.  Defaults to B</dev/dri/card0>.


=item B<format>

Pixel format of the framebuffer.  This can be autodetected if you are running Linux 5.7
or later, but needs to be provided for earlier versions.  Defaults to B<bgr0>,
which is the most common format used by the Linux console and Xorg X server.


=item B<format_modifier>

Format modifier to signal on output frames.  This is necessary to import correctly into
some APIs.  It can be autodetected if you are running Linux 5.7 or later, but will need
to be provided explicitly when needed in earlier versions.  See the libdrm documentation
for possible values.


=item B<crtc_id>

KMS CRTC ID to define the capture source.  The first active plane on the given CRTC
will be used.


=item B<plane_id>

KMS plane ID to define the capture source.  Defaults to the first active plane found if
neither B<crtc_id> nor B<plane_id> are specified.


=item B<framerate>

Framerate to capture at.  This is not synchronised to any page flipping or framebuffer
changes - it just defines the interval at which the framebuffer is sampled.  Sampling
faster than the framebuffer update rate will generate independent frames with the same
content.  Defaults to C<30>.


=back



=head3 Examples



=over 4



=item *

Capture from the first active plane, download the result to normal frames and encode.
This will only work if the framebuffer is both linear and mappable - if not, the result
may be scrambled or fail to download.
	
	ffmpeg -f kmsgrab -i - -vf 'hwdownload,format=bgr0' output.mp4



=item *

Capture from CRTC ID 42 at 60fps, map the result to VAAPI, convert to NV12 and encode as H.264.
	
	ffmpeg -crtc_id 42 -framerate 60 -f kmsgrab -i - -vf 'hwmap=derive_device=vaapi,scale_vaapi=w=1920:h=1080:format=nv12' -c:v h264_vaapi output.mp4



=item *

To capture only part of a plane the output can be cropped - this can be used to capture
a single window, as long as it has a known absolute position and size.  For example, to
capture and encode the middle quarter of a 1920x1080 plane:
	
	ffmpeg -f kmsgrab -i - -vf 'hwmap=derive_device=vaapi,crop=960:540:480:270,scale_vaapi=960:540:nv12' -c:v h264_vaapi output.mp4



=back



=head2 lavfi


Libavfilter input virtual device.

This input device reads data from the open output pads of a libavfilter
filtergraph.

For each filtergraph open output, the input device will create a
corresponding stream which is mapped to the generated output.
The filtergraph is specified through the option B<graph>.


=head3 Options



=over 4



=item B<graph>

Specify the filtergraph to use as input. Each video open output must be
labelled by a unique string of the form "outI<N>", where I<N> is a
number starting from 0 corresponding to the mapped input stream
generated by the device.
The first unlabelled output is automatically assigned to the "out0"
label, but all the others need to be specified explicitly.

The suffix "+subcc" can be appended to the output label to create an extra
stream with the closed captions packets attached to that output
(experimental; only for EIA-608 / CEA-708 for now).
The subcc streams are created after all the normal streams, in the order of
the corresponding stream.
For example, if there is "out19+subcc", "out7+subcc" and up to "out42", the
stream #43 is subcc for stream #7 and stream #44 is subcc for stream #19.

If not specified defaults to the filename specified for the input
device.


=item B<graph_file>

Set the filename of the filtergraph to be read and sent to the other
filters. Syntax of the filtergraph is the same as the one specified by
the option I<graph>.


=item B<dumpgraph>

Dump graph to stderr.


=back



=head3 Examples



=over 4


=item *

Create a color video stream and play it back with B<ffplay>:
	
	ffplay -f lavfi -graph "color=c=pink [out0]" dummy



=item *

As the previous example, but use filename for specifying the graph
description, and omit the "out0" label:
	
	ffplay -f lavfi color=c=pink



=item *

Create three different video test filtered sources and play them:
	
	ffplay -f lavfi -graph "testsrc [out0]; testsrc,hflip [out1]; testsrc,negate [out2]" test3



=item *

Read an audio stream from a file using the amovie source and play it
back with B<ffplay>:
	
	ffplay -f lavfi "amovie=test.wav"



=item *

Read an audio stream and a video stream and play it back with
B<ffplay>:
	
	ffplay -f lavfi "movie=test.avi[out0];amovie=test.wav[out1]"



=item *

Dump decoded frames to images and Closed Captions to an RCWT backup:
	
	ffmpeg -f lavfi -i "movie=test.ts[out0+subcc]" -map v frame%08d.png -map s -c copy -f rcwt subcc.bin



=back



=head2 libcdio


Audio-CD input device based on libcdio.

To enable this input device during configuration you need libcdio
installed on your system. It requires the configure option
C<--enable-libcdio>.

This device allows playing and grabbing from an Audio-CD.

For example to copy with B<ffmpeg> the entire Audio-CD in F</dev/sr0>,
you may run the command:
	
	ffmpeg -f libcdio -i /dev/sr0 cd.wav



=head3 Options


=over 4


=item B<speed>

Set drive reading speed. Default value is 0.

The speed is specified CD-ROM speed units. The speed is set through
the libcdio C<cdio_cddap_speed_set> function. On many CD-ROM
drives, specifying a value too large will result in using the fastest
speed.


=item B<paranoia_mode>

Set paranoia recovery mode flags. It accepts one of the following values:


=over 4


=item B<disable>


=item B<verify>


=item B<overlap>


=item B<neverskip>


=item B<full>


=back


Default value is B<disable>.

For more information about the available recovery modes, consult the
paranoia project documentation.

=back



=head2 libdc1394


IIDC1394 input device, based on libdc1394 and libraw1394.

Requires the configure option C<--enable-libdc1394>.


=head3 Options


=over 4



=item B<framerate>

Set the frame rate. Default is C<ntsc>, corresponding to a frame
rate of C<30000/1001>.


=item B<pixel_format>

Select the pixel format. Default is C<uyvy422>.


=item B<video_size>

Set the video size given as a string such as C<640x480> or C<hd720>.
Default is C<qvga>.

=back



=head2 openal


The OpenAL input device provides audio capture on all systems with a
working OpenAL 1.1 implementation.

To enable this input device during configuration, you need OpenAL
headers and libraries installed on your system, and need to configure
FFmpeg with C<--enable-openal>.

OpenAL headers and libraries should be provided as part of your OpenAL
implementation, or as an additional download (an SDK). Depending on your
installation you may need to specify additional flags via the
C<--extra-cflags> and C<--extra-ldflags> for allowing the build
system to locate the OpenAL headers and libraries.

An incomplete list of OpenAL implementations follows:


=over 4


=item B<Creative>

The official Windows implementation, providing hardware acceleration
with supported devices and software fallback.
See E<lt>B<http://openal.org/>E<gt>.

=item B<OpenAL Soft>

Portable, open source (LGPL) software implementation. Includes
backends for the most common sound APIs on the Windows, Linux,
Solaris, and BSD operating systems.
See E<lt>B<http://kcat.strangesoft.net/openal.html>E<gt>.

=item B<Apple>

OpenAL is part of Core Audio, the official Mac OS X Audio interface.
See E<lt>B<http://developer.apple.com/technologies/mac/audio-and-video.html>E<gt>

=back


This device allows one to capture from an audio input device handled
through OpenAL.

You need to specify the name of the device to capture in the provided
filename. If the empty string is provided, the device will
automatically select the default device. You can get the list of the
supported devices by using the option I<list_devices>.


=head3 Options



=over 4



=item B<channels>

Set the number of channels in the captured audio. Only the values
B<1> (monaural) and B<2> (stereo) are currently supported.
Defaults to B<2>.


=item B<sample_size>

Set the sample size (in bits) of the captured audio. Only the values
B<8> and B<16> are currently supported. Defaults to
B<16>.


=item B<sample_rate>

Set the sample rate (in Hz) of the captured audio.
Defaults to B<44.1k>.


=item B<list_devices>

If set to B<true>, print a list of devices and exit.
Defaults to B<false>.


=back



=head3 Examples


Print the list of OpenAL supported devices and exit:
	
	$ ffmpeg -list_devices true -f openal -i dummy out.ogg


Capture from the OpenAL device F<DR-BT101 via PulseAudio>:
	
	$ ffmpeg -f openal -i 'DR-BT101 via PulseAudio' out.ogg


Capture from the default device (note the empty string '' as filename):
	
	$ ffmpeg -f openal -i '' out.ogg


Capture from two devices simultaneously, writing to two different files,
within the same B<ffmpeg> command:
	
	$ ffmpeg -f openal -i 'DR-BT101 via PulseAudio' out1.ogg -f openal -i 'ALSA Default' out2.ogg

Note: not all OpenAL implementations support multiple simultaneous capture -
try the latest OpenAL Soft if the above does not work.


=head2 oss


Open Sound System input device.

The filename to provide to the input device is the device node
representing the OSS input device, and is usually set to
F</dev/dsp>.

For example to grab from F</dev/dsp> using B<ffmpeg> use the
command:
	
	ffmpeg -f oss -i /dev/dsp /tmp/oss.wav


For more information about OSS see:
E<lt>B<http://manuals.opensound.com/usersguide/dsp.html>E<gt>


=head3 Options



=over 4



=item B<sample_rate>

Set the sample rate in Hz. Default is 48000.


=item B<channels>

Set the number of channels. Default is 2.


=back



=head2 pulse


PulseAudio input device.

To enable this output device you need to configure FFmpeg with C<--enable-libpulse>.

The filename to provide to the input device is a source device or the
string "default"

To list the PulseAudio source devices and their properties you can invoke
the command B<pactl list sources>.

More information about PulseAudio can be found on E<lt>B<http://www.pulseaudio.org>E<gt>.


=head3 Options


=over 4


=item B<server>

Connect to a specific PulseAudio server, specified by an IP address.
Default server is used when not provided.


=item B<name>

Specify the application name PulseAudio will use when showing active clients,
by default it is the C<LIBAVFORMAT_IDENT> string.


=item B<stream_name>

Specify the stream name PulseAudio will use when showing active streams,
by default it is "record".


=item B<sample_rate>

Specify the samplerate in Hz, by default 48kHz is used.


=item B<channels>

Specify the channels in use, by default 2 (stereo) is set.


=item B<frame_size>

This option does nothing and is deprecated.


=item B<fragment_size>

Specify the size in bytes of the minimal buffering fragment in PulseAudio, it
will affect the audio latency. By default it is set to 50 ms amount of data.


=item B<wallclock>

Set the initial PTS using the current time. Default is 1.


=back



=head3 Examples

Record a stream from default device:
	
	ffmpeg -f pulse -i default /tmp/pulse.wav



=head2 sndio


sndio input device.

To enable this input device during configuration you need libsndio
installed on your system.

The filename to provide to the input device is the device node
representing the sndio input device, and is usually set to
F</dev/audio0>.

For example to grab from F</dev/audio0> using B<ffmpeg> use the
command:
	
	ffmpeg -f sndio -i /dev/audio0 /tmp/oss.wav



=head3 Options



=over 4



=item B<sample_rate>

Set the sample rate in Hz. Default is 48000.


=item B<channels>

Set the number of channels. Default is 2.


=back



=head2 video4linux2, v4l2


Video4Linux2 input video device.

"v4l2" can be used as alias for "video4linux2".

If FFmpeg is built with v4l-utils support (by using the
C<--enable-libv4l2> configure option), it is possible to use it with the
C<-use_libv4l2> input device option.

The name of the device to grab is a file device node, usually Linux
systems tend to automatically create such nodes when the device
(e.g. an USB webcam) is plugged into the system, and has a name of the
kind F</dev/videoI<N>>, where I<N> is a number associated to
the device.

Video4Linux2 devices usually support a limited set of
I<width>xI<height> sizes and frame rates. You can check which are
supported using B<-list_formats all> for Video4Linux2 devices.
Some devices, like TV cards, support one or more standards. It is possible
to list all the supported standards using B<-list_standards all>.

The time base for the timestamps is 1 microsecond. Depending on the kernel
version and configuration, the timestamps may be derived from the real time
clock (origin at the Unix Epoch) or the monotonic clock (origin usually at
boot time, unaffected by NTP or manual changes to the clock). The
B<-timestamps abs> or B<-ts abs> option can be used to force
conversion into the real time clock.

Some usage examples of the video4linux2 device with B<ffmpeg>
and B<ffplay>:

=over 4


=item *

List supported formats for a video4linux2 device:
	
	ffplay -f video4linux2 -list_formats all /dev/video0



=item *

Grab and show the input of a video4linux2 device:
	
	ffplay -f video4linux2 -framerate 30 -video_size hd720 /dev/video0



=item *

Grab and record the input of a video4linux2 device, leave the
frame rate and size as previously set:
	
	ffmpeg -f video4linux2 -input_format mjpeg -i /dev/video0 out.mpeg


=back


For more information about Video4Linux, check E<lt>B<http://linuxtv.org/>E<gt>.


=head3 Options



=over 4


=item B<standard>

Set the standard. Must be the name of a supported standard. To get a
list of the supported standards, use the B<list_standards>
option.


=item B<channel>

Set the input channel number. Default to -1, which means using the
previously selected channel.


=item B<video_size>

Set the video frame size. The argument must be a string in the form
I<WIDTH>xI<HEIGHT> or a valid size abbreviation.


=item B<pixel_format>

Select the pixel format (only valid for raw video input).


=item B<input_format>

Set the preferred pixel format (for raw video) or a codec name.
This option allows one to select the input format, when several are
available.


=item B<framerate>

Set the preferred video frame rate.


=item B<list_formats>

List available formats (supported pixel formats, codecs, and frame
sizes) and exit.

Available values are:

=over 4


=item B<all>

Show all available (compressed and non-compressed) formats.


=item B<raw>

Show only raw video (non-compressed) formats.


=item B<compressed>

Show only compressed formats.

=back



=item B<list_standards>

List supported standards and exit.

Available values are:

=over 4


=item B<all>

Show all supported standards.

=back



=item B<timestamps, ts>

Set type of timestamps for grabbed frames.

Available values are:

=over 4


=item B<default>

Use timestamps from the kernel.


=item B<abs>

Use absolute timestamps (wall clock).


=item B<mono2abs>

Force conversion from monotonic to absolute timestamps.

=back


Default value is C<default>.


=item B<use_libv4l2>

Use libv4l2 (v4l-utils) conversion functions. Default is 0.


=back



=head2 vfwcap


VfW (Video for Windows) capture input device.

The filename passed as input is the capture driver number, ranging from
0 to 9. You may use "list" as filename to print a list of drivers. Any
other filename will be interpreted as device number 0.


=head3 Options



=over 4



=item B<video_size>

Set the video frame size.


=item B<framerate>

Set the grabbing frame rate. Default value is C<ntsc>,
corresponding to a frame rate of C<30000/1001>.


=back



=head2 x11grab


X11 video input device.

To enable this input device during configuration you need libxcb
installed on your system. It will be automatically detected during
configuration.

This device allows one to capture a region of an X11 display.

The filename passed as input has the syntax:
	
	[<hostname>]:<display_number>.<screen_number>[+<x_offset>,<y_offset>]


I<hostname>:I<display_number>.I<screen_number> specifies the
X11 display name of the screen to grab from. I<hostname> can be
omitted, and defaults to "localhost". The environment variable
B<DISPLAY> contains the default display name.

I<x_offset> and I<y_offset> specify the offsets of the grabbed
area with respect to the top-left border of the X11 screen. They
default to 0.

Check the X11 documentation (e.g. B<man X>) for more detailed
information.

Use the B<xdpyinfo> program for getting basic information about
the properties of your X11 display (e.g. grep for "name" or
"dimensions").

For example to grab from F<:0.0> using B<ffmpeg>:
	
	ffmpeg -f x11grab -framerate 25 -video_size cif -i :0.0 out.mpg


Grab at position C<10,20>:
	
	ffmpeg -f x11grab -framerate 25 -video_size cif -i :0.0+10,20 out.mpg



=head3 Options



=over 4


=item B<select_region>

Specify whether to select the grabbing area graphically using the pointer.
A value of C<1> prompts the user to select the grabbing area graphically
by clicking and dragging. A single click with no dragging will select the
whole screen. A region with zero width or height will also select the whole
screen. This option overwrites the I<video_size>, I<grab_x>, and
I<grab_y> options. Default value is C<0>.


=item B<draw_mouse>

Specify whether to draw the mouse pointer. A value of C<0> specifies
not to draw the pointer. Default value is C<1>.


=item B<follow_mouse>

Make the grabbed area follow the mouse. The argument can be
C<centered> or a number of pixels I<PIXELS>.

When it is specified with "centered", the grabbing region follows the mouse
pointer and keeps the pointer at the center of region; otherwise, the region
follows only when the mouse pointer reaches within I<PIXELS> (greater than
zero) to the edge of region.

For example:
	
	ffmpeg -f x11grab -follow_mouse centered -framerate 25 -video_size cif -i :0.0 out.mpg


To follow only when the mouse pointer reaches within 100 pixels to edge:
	
	ffmpeg -f x11grab -follow_mouse 100 -framerate 25 -video_size cif -i :0.0 out.mpg



=item B<framerate>

Set the grabbing frame rate. Default value is C<ntsc>,
corresponding to a frame rate of C<30000/1001>.


=item B<show_region>

Show grabbed region on screen.

If I<show_region> is specified with C<1>, then the grabbing
region will be indicated on screen. With this option, it is easy to
know what is being grabbed if only a portion of the screen is grabbed.


=item B<region_border>

Set the region border thickness if B<-show_region 1> is used.
Range is 1 to 128 and default is 3 (XCB-based x11grab only).

For example:
	
	ffmpeg -f x11grab -show_region 1 -framerate 25 -video_size cif -i :0.0+10,20 out.mpg


With I<follow_mouse>:
	
	ffmpeg -f x11grab -follow_mouse centered -show_region 1 -framerate 25 -video_size cif -i :0.0 out.mpg



=item B<window_id>

Grab this window, instead of the whole screen. Default value is 0, which maps to
the whole screen (root window).

The id of a window can be found using the B<xwininfo> program, possibly with options -tree and
-root.

If the window is later enlarged, the new area is not recorded. Video ends when
the window is closed, unmapped (i.e., iconified) or shrunk beyond the video
size (which defaults to the initial window size).

This option disables options B<follow_mouse> and B<select_region>.


=item B<video_size>

Set the video frame size. Default is the full desktop or window.


=item B<grab_x>


=item B<grab_y>

Set the grabbing region coordinates. They are expressed as offset from
the top left corner of the X11 window and correspond to the
I<x_offset> and I<y_offset> parameters in the device name. The
default value for both options is 0.

=back



=head1 OUTPUT DEVICES


Output devices are configured elements in FFmpeg that can write
multimedia data to an output device attached to your system.

When you configure your FFmpeg build, all the supported output devices
are enabled by default. You can list all available ones using the
configure option "--list-outdevs".

You can disable all the output devices using the configure option
"--disable-outdevs", and selectively enable an output device using the
option "--enable-outdev=I<OUTDEV>", or you can disable a particular
input device using the option "--disable-outdev=I<OUTDEV>".

The option "-devices" of the ff* tools will display the list of
enabled output devices.

A description of the currently available output devices follows.


=head2 alsa


ALSA (Advanced Linux Sound Architecture) output device.


=head3 Examples



=over 4


=item *

Play a file on default ALSA device:
	
	ffmpeg -i INPUT -f alsa default



=item *

Play a file on soundcard 1, audio device 7:
	
	ffmpeg -i INPUT -f alsa hw:1,7


=back



=head2 AudioToolbox


AudioToolbox output device.

Allows native output to CoreAudio devices on OSX.

The output filename can be empty (or C<->) to refer to the default system output device or a number that refers to the device index as shown using: C<-list_devices true>.

Alternatively, the audio input device can be chosen by index using the

    B<-audio_device_index E<lt>INDEXE<gt>>

, overriding any device name or index given in the input filename.

All available devices can be enumerated by using B<-list_devices true>, listing
all device names, UIDs and corresponding indices.


=head3 Options


AudioToolbox supports the following options:


=over 4



=item B<-audio_device_index E<lt>INDEXE<gt>>

Specify the audio device by its index. Overrides anything given in the output filename.


=back



=head3 Examples



=over 4



=item *

Print the list of supported devices and output a sine wave to the default device:
	
	$ ffmpeg -f lavfi -i sine=r=44100 -f audiotoolbox -list_devices true -



=item *

Output a sine wave to the device with the index 2, overriding any output filename:
	
	$ ffmpeg -f lavfi -i sine=r=44100 -f audiotoolbox -audio_device_index 2 -



=back



=head2 caca


CACA output device.

This output device allows one to show a video stream in CACA window.
Only one CACA window is allowed per application, so you can
have only one instance of this output device in an application.

To enable this output device you need to configure FFmpeg with
C<--enable-libcaca>.
libcaca is a graphics library that outputs text instead of pixels.

For more information about libcaca, check:
E<lt>B<http://caca.zoy.org/wiki/libcaca>E<gt>


=head3 Options



=over 4



=item B<window_title>

Set the CACA window title, if not specified default to the filename
specified for the output device.


=item B<window_size>

Set the CACA window size, can be a string of the form
I<width>xI<height> or a video size abbreviation.
If not specified it defaults to the size of the input video.


=item B<driver>

Set display driver.


=item B<algorithm>

Set dithering algorithm. Dithering is necessary
because the picture being rendered has usually far more colours than
the available palette.
The accepted values are listed with C<-list_dither algorithms>.


=item B<antialias>

Set antialias method. Antialiasing smoothens the rendered
image and avoids the commonly seen staircase effect.
The accepted values are listed with C<-list_dither antialiases>.


=item B<charset>

Set which characters are going to be used when rendering text.
The accepted values are listed with C<-list_dither charsets>.


=item B<color>

Set color to be used when rendering text.
The accepted values are listed with C<-list_dither colors>.


=item B<list_drivers>

If set to B<true>, print a list of available drivers and exit.


=item B<list_dither>

List available dither options related to the argument.
The argument must be one of C<algorithms>, C<antialiases>,
C<charsets>, C<colors>.

=back



=head3 Examples



=over 4


=item *

The following command shows the B<ffmpeg> output is an
CACA window, forcing its size to 80x25:
	
	ffmpeg -i INPUT -c:v rawvideo -pix_fmt rgb24 -window_size 80x25 -f caca -



=item *

Show the list of available drivers and exit:
	
	ffmpeg -i INPUT -pix_fmt rgb24 -f caca -list_drivers true -



=item *

Show the list of available dither colors and exit:
	
	ffmpeg -i INPUT -pix_fmt rgb24 -f caca -list_dither colors -


=back



=head2 decklink


The decklink output device provides playback capabilities for Blackmagic
DeckLink devices.

To enable this output device, you need the Blackmagic DeckLink SDK and you
need to configure with the appropriate C<--extra-cflags>
and C<--extra-ldflags>.
On Windows, you need to run the IDL files through B<widl>.

DeckLink is very picky about the formats it supports. Pixel format is always
uyvy422, framerate, field order and video size must be determined for your
device with B<-list_formats 1>. Audio sample rate is always 48 kHz.


=head3 Options



=over 4



=item B<list_devices>

If set to B<true>, print a list of devices and exit.
Defaults to B<false>. This option is deprecated, please use the
C<-sinks> option of ffmpeg to list the available output devices.


=item B<list_formats>

If set to B<true>, print a list of supported formats and exit.
Defaults to B<false>.


=item B<preroll>

Amount of time to preroll video in seconds.
Defaults to B<0.5>.


=item B<duplex_mode>

Sets the decklink device duplex/profile mode. Must be B<unset>, B<half>, B<full>,
B<one_sub_device_full>, B<one_sub_device_half>, B<two_sub_device_full>,
B<four_sub_device_half>
Defaults to B<unset>.

Note: DeckLink SDK 11.0 have replaced the duplex property by a profile property.
For the DeckLink Duo 2 and DeckLink Quad 2, a profile is shared between any 2
sub-devices that utilize the same connectors. For the DeckLink 8K Pro, a profile
is shared between all 4 sub-devices. So DeckLink 8K Pro support four profiles.

Valid profile modes for DeckLink 8K Pro(with DeckLink SDK E<gt>= 11.0):
B<one_sub_device_full>, B<one_sub_device_half>, B<two_sub_device_full>,
B<four_sub_device_half>

Valid profile modes for DeckLink Quad 2 and DeckLink Duo 2:
B<half>, B<full>


=item B<timing_offset>

Sets the genlock timing pixel offset on the used output.
Defaults to B<unset>.


=item B<link>

Sets the SDI video link configuration on the used output. Must be
B<unset>, B<single> link SDI, B<dual> link SDI or B<quad> link
SDI.
Defaults to B<unset>.


=item B<sqd>

Enable Square Division Quad Split mode for Quad-link SDI output.
Must be B<unset>, B<true> or B<false>.
Defaults to B<unset>.


=item B<level_a>

Enable SMPTE Level A mode on the used output.
Must be B<unset>, B<true> or B<false>.
Defaults to B<unset>.


=item B<vanc_queue_size>

Sets maximum output buffer size in bytes for VANC data. If the buffering reaches this value,
outgoing VANC data will be dropped.
Defaults to B<1048576>.


=back



=head3 Examples



=over 4



=item *

List output devices:
	
	ffmpeg -sinks decklink



=item *

List supported formats:
	
	ffmpeg -i test.avi -f decklink -list_formats 1 'DeckLink Mini Monitor'



=item *

Play video clip:
	
	ffmpeg -i test.avi -f decklink -pix_fmt uyvy422 'DeckLink Mini Monitor'



=item *

Play video clip with non-standard framerate or video size:
	
	ffmpeg -i test.avi -f decklink -pix_fmt uyvy422 -s 720x486 -r 24000/1001 'DeckLink Mini Monitor'



=back



=head2 fbdev


Linux framebuffer output device.

The Linux framebuffer is a graphic hardware-independent abstraction
layer to show graphics on a computer monitor, typically on the
console. It is accessed through a file device node, usually
F</dev/fb0>.

For more detailed information read the file
F<Documentation/fb/framebuffer.txt> included in the Linux source tree.


=head3 Options


=over 4



=item B<xoffset>


=item B<yoffset>

Set x/y coordinate of top left corner. Default is 0.

=back



=head3 Examples

Play a file on framebuffer device F</dev/fb0>.
Required pixel format depends on current framebuffer settings.
	
	ffmpeg -re -i INPUT -c:v rawvideo -pix_fmt bgra -f fbdev /dev/fb0


See also E<lt>B<http://linux-fbdev.sourceforge.net/>E<gt>, and fbset(1).


=head2 oss


OSS (Open Sound System) output device.


=head2 pulse


PulseAudio output device.

To enable this output device you need to configure FFmpeg with C<--enable-libpulse>.

More information about PulseAudio can be found on E<lt>B<http://www.pulseaudio.org>E<gt>


=head3 Options


=over 4



=item B<server>

Connect to a specific PulseAudio server, specified by an IP address.
Default server is used when not provided.


=item B<name>

Specify the application name PulseAudio will use when showing active clients,
by default it is the C<LIBAVFORMAT_IDENT> string.


=item B<stream_name>

Specify the stream name PulseAudio will use when showing active streams,
by default it is set to the specified output name.


=item B<device>

Specify the device to use. Default device is used when not provided.
List of output devices can be obtained with command B<pactl list sinks>.


=item B<buffer_size>


=item B<buffer_duration>

Control the size and duration of the PulseAudio buffer. A small buffer
gives more control, but requires more frequent updates.

B<buffer_size> specifies size in bytes while
B<buffer_duration> specifies duration in milliseconds.

When both options are provided then the highest value is used
(duration is recalculated to bytes using stream parameters). If they
are set to 0 (which is default), the device will use the default
PulseAudio duration value. By default PulseAudio set buffer duration
to around 2 seconds.


=item B<prebuf>

Specify pre-buffering size in bytes. The server does not start with
playback before at least B<prebuf> bytes are available in the
buffer. By default this option is initialized to the same value as
B<buffer_size> or B<buffer_duration> (whichever is bigger).


=item B<minreq>

Specify minimum request size in bytes. The server does not request less
than B<minreq> bytes from the client, instead waits until the buffer
is free enough to request more bytes at once. It is recommended to not set
this option, which will initialize this to a value that is deemed sensible
by the server.


=back



=head3 Examples

Play a file on default device on default server:
	
	ffmpeg  -i INPUT -f pulse "stream name"



=head2 sndio


sndio audio output device.


=head2 v4l2


Video4Linux2 output device.


=head2 xv


XV (XVideo) output device.

This output device allows one to show a video stream in a X Window System
window.


=head3 Options



=over 4


=item B<display_name>

Specify the hardware display name, which determines the display and
communications domain to be used.

The display name or DISPLAY environment variable can be a string in
the format I<hostname>[:I<number>[.I<screen_number>]].

I<hostname> specifies the name of the host machine on which the
display is physically attached. I<number> specifies the number of
the display server on that host machine. I<screen_number> specifies
the screen to be used on that server.

If unspecified, it defaults to the value of the DISPLAY environment
variable.

For example, C<dual-headed:0.1> would specify screen 1 of display
0 on the machine named ``dual-headed''.

Check the X11 specification for more detailed information about the
display name format.


=item B<window_id>

When set to non-zero value then device doesn't create new window,
but uses existing one with provided I<window_id>. By default
this options is set to zero and device creates its own window.


=item B<window_size>

Set the created window size, can be a string of the form
I<width>xI<height> or a video size abbreviation. If not
specified it defaults to the size of the input video.
Ignored when I<window_id> is set.


=item B<window_x>


=item B<window_y>

Set the X and Y window offsets for the created window. They are both
set to 0 by default. The values may be ignored by the window manager.
Ignored when I<window_id> is set.


=item B<window_title>

Set the window title, if not specified default to the filename
specified for the output device. Ignored when I<window_id> is set.

=back


For more information about XVideo see E<lt>B<http://www.x.org/>E<gt>.


=head3 Examples



=over 4


=item *

Decode, display and encode video input with B<ffmpeg> at the
same time:
	
	ffmpeg -i INPUT OUTPUT -f xv display



=item *

Decode and display the input video to multiple X11 windows:
	
	ffmpeg -i INPUT -f xv normal -vf negate -f xv negated


=back




=head1 SEE ALSO



ffmpeg(1), ffplay(1), ffprobe(1), libavdevice(3)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



