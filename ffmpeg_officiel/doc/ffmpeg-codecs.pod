=encoding utf8

=head1 NAME

ffmpeg-codecs - FFmpeg codecs

=head1 DESCRIPTION


This document describes the codecs (decoders and encoders) provided by
the libavcodec library.




=head1 CODEC OPTIONS


libavcodec provides some generic global options, which can be set on
all the encoders and decoders. In addition, each codec may support
so-called private options, which are specific for a given codec.

Sometimes, a global option may only affect a specific kind of codec,
and may be nonsensical or ignored by another, so you need to be aware
of the meaning of the specified options. Also some options are
meant only for decoding or encoding.

Options may be set by specifying -I<option> I<value> in the
FFmpeg tools, or by setting the value explicitly in the
C<AVCodecContext> options or using the F<libavutil/opt.h> API
for programmatic use.

The list of supported options follow:


=over 4


=item B<b> I<integer> B<(>I<encoding,audio,video>B<)>

Set bitrate in bits/s. Default value is 200K.


=item B<ab> I<integer> B<(>I<encoding,audio>B<)>

Set audio bitrate (in bits/s). Default value is 128K.


=item B<bt> I<integer> B<(>I<encoding,video>B<)>

Set video bitrate tolerance (in bits/s). In 1-pass mode, bitrate
tolerance specifies how far ratecontrol is willing to deviate from the
target average bitrate value. This is not related to min/max
bitrate. Lowering tolerance too much has an adverse effect on quality.


=item B<flags> I<flags> B<(>I<decoding/encoding,audio,video,subtitles>B<)>

Set generic flags.

Possible values:

=over 4


=item B<mv4>

Use four motion vector by macroblock (mpeg4).

=item B<qpel>

Use 1/4 pel motion compensation.

=item B<loop>

Use loop filter.

=item B<qscale>

Use fixed qscale.

=item B<pass1>

Use internal 2pass ratecontrol in first pass mode.

=item B<pass2>

Use internal 2pass ratecontrol in second pass mode.

=item B<gray>

Only decode/encode grayscale.

=item B<psnr>

Set error[?] variables during encoding.

=item B<truncated>

Input bitstream might be randomly truncated.

=item B<drop_changed>

Don't output frames whose parameters differ from first decoded frame in stream.
Error AVERROR_INPUT_CHANGED is returned when a frame is dropped.


=item B<ildct>

Use interlaced DCT.

=item B<low_delay>

Force low delay.

=item B<global_header>

Place global headers in extradata instead of every keyframe.

=item B<bitexact>

Only write platform-, build- and time-independent data. (except (I)DCT).
This ensures that file and data checksums are reproducible and match between
platforms. Its primary use is for regression testing.

=item B<aic>

Apply H263 advanced intra coding / mpeg4 ac prediction.

=item B<ilme>

Apply interlaced motion estimation.

=item B<cgop>

Use closed gop.

=item B<output_corrupt>

Output even potentially corrupted frames.

=back



=item B<time_base> I<rational number>

Set codec time base.

It is the fundamental unit of time (in seconds) in terms of which
frame timestamps are represented. For fixed-fps content, timebase
should be C<1 / frame_rate> and timestamp increments should be
identically 1.


=item B<g> I<integer> B<(>I<encoding,video>B<)>

Set the group of picture (GOP) size. Default value is 12.


=item B<ar> I<integer> B<(>I<decoding/encoding,audio>B<)>

Set audio sampling rate (in Hz).


=item B<ac> I<integer> B<(>I<decoding/encoding,audio>B<)>

Set number of audio channels.


=item B<cutoff> I<integer> B<(>I<encoding,audio>B<)>

Set cutoff bandwidth. (Supported only by selected encoders, see
their respective documentation sections.)


=item B<frame_size> I<integer> B<(>I<encoding,audio>B<)>

Set audio frame size.

Each submitted frame except the last must contain exactly frame_size
samples per channel. May be 0 when the codec has
CODEC_CAP_VARIABLE_FRAME_SIZE set, in that case the frame size is not
restricted. It is set by some decoders to indicate constant frame
size.


=item B<frame_number> I<integer>

Set the frame number.


=item B<delay> I<integer>



=item B<qcomp> I<float> B<(>I<encoding,video>B<)>

Set video quantizer scale compression (VBR). It is used as a constant
in the ratecontrol equation. Recommended range for default rc_eq:
0.0-1.0.


=item B<qblur> I<float> B<(>I<encoding,video>B<)>

Set video quantizer scale blur (VBR).


=item B<qmin> I<integer> B<(>I<encoding,video>B<)>

Set min video quantizer scale (VBR). Must be included between -1 and
69, default value is 2.


=item B<qmax> I<integer> B<(>I<encoding,video>B<)>

Set max video quantizer scale (VBR). Must be included between -1 and
1024, default value is 31.


=item B<qdiff> I<integer> B<(>I<encoding,video>B<)>

Set max difference between the quantizer scale (VBR).


=item B<bf> I<integer> B<(>I<encoding,video>B<)>

Set max number of B frames between non-B-frames.

Must be an integer between -1 and 16. 0 means that B-frames are
disabled. If a value of -1 is used, it will choose an automatic value
depending on the encoder.

Default value is 0.


=item B<b_qfactor> I<float> B<(>I<encoding,video>B<)>

Set qp factor between P and B frames.


=item B<codec_tag> I<integer>


=item B<bug> I<flags> B<(>I<decoding,video>B<)>

Workaround not auto detected encoder bugs.

Possible values:

=over 4


=item B<autodetect>



=item B<xvid_ilace>

Xvid interlacing bug (autodetected if fourcc==XVIX)

=item B<ump4>

(autodetected if fourcc==UMP4)

=item B<no_padding>

padding bug (autodetected)

=item B<amv>



=item B<qpel_chroma>



=item B<std_qpel>

old standard qpel (autodetected per fourcc/version)

=item B<qpel_chroma2>



=item B<direct_blocksize>

direct-qpel-blocksize bug (autodetected per fourcc/version)

=item B<edge>

edge padding bug (autodetected per fourcc/version)

=item B<hpel_chroma>



=item B<dc_clip>



=item B<ms>

Workaround various bugs in microsoft broken decoders.

=item B<trunc>

trancated frames

=back



=item B<strict> I<integer> B<(>I<decoding/encoding,audio,video>B<)>

Specify how strictly to follow the standards.

Possible values:

=over 4


=item B<very>

strictly conform to an older more strict version of the spec or reference software

=item B<strict>

strictly conform to all the things in the spec no matter what consequences

=item B<normal>



=item B<unofficial>

allow unofficial extensions

=item B<experimental>

allow non standardized experimental things, experimental
(unfinished/work in progress/not well tested) decoders and encoders.
Note: experimental decoders can pose a security risk, do not use this for
decoding untrusted input.

=back



=item B<b_qoffset> I<float> B<(>I<encoding,video>B<)>

Set QP offset between P and B frames.


=item B<err_detect> I<flags> B<(>I<decoding,audio,video>B<)>

Set error detection flags.

Possible values:

=over 4


=item B<crccheck>

verify embedded CRCs

=item B<bitstream>

detect bitstream specification deviations

=item B<buffer>

detect improper bitstream length

=item B<explode>

abort decoding on minor error detection

=item B<ignore_err>

ignore decoding errors, and continue decoding.
This is useful if you want to analyze the content of a video and thus want
everything to be decoded no matter what. This option will not result in a video
that is pleasing to watch in case of errors.

=item B<careful>

consider things that violate the spec and have not been seen in the wild as errors

=item B<compliant>

consider all spec non compliancies as errors

=item B<aggressive>

consider things that a sane encoder should not do as an error

=back



=item B<has_b_frames> I<integer>



=item B<block_align> I<integer>



=item B<rc_override_count> I<integer>



=item B<maxrate> I<integer> B<(>I<encoding,audio,video>B<)>

Set max bitrate tolerance (in bits/s). Requires bufsize to be set.


=item B<minrate> I<integer> B<(>I<encoding,audio,video>B<)>

Set min bitrate tolerance (in bits/s). Most useful in setting up a CBR
encode. It is of little use elsewise.


=item B<bufsize> I<integer> B<(>I<encoding,audio,video>B<)>

Set ratecontrol buffer size (in bits).


=item B<i_qfactor> I<float> B<(>I<encoding,video>B<)>

Set QP factor between P and I frames.


=item B<i_qoffset> I<float> B<(>I<encoding,video>B<)>

Set QP offset between P and I frames.


=item B<dct> I<integer> B<(>I<encoding,video>B<)>

Set DCT algorithm.

Possible values:

=over 4


=item B<auto>

autoselect a good one (default)

=item B<fastint>

fast integer

=item B<int>

accurate integer

=item B<mmx>



=item B<altivec>



=item B<faan>

floating point AAN DCT

=back



=item B<lumi_mask> I<float> B<(>I<encoding,video>B<)>

Compress bright areas stronger than medium ones.


=item B<tcplx_mask> I<float> B<(>I<encoding,video>B<)>

Set temporal complexity masking.


=item B<scplx_mask> I<float> B<(>I<encoding,video>B<)>

Set spatial complexity masking.


=item B<p_mask> I<float> B<(>I<encoding,video>B<)>

Set inter masking.


=item B<dark_mask> I<float> B<(>I<encoding,video>B<)>

Compress dark areas stronger than medium ones.


=item B<idct> I<integer> B<(>I<decoding/encoding,video>B<)>

Select IDCT implementation.

Possible values:

=over 4


=item B<auto>



=item B<int>



=item B<simple>



=item B<simplemmx>



=item B<simpleauto>

Automatically pick a IDCT compatible with the simple one


=item B<arm>



=item B<altivec>



=item B<sh4>



=item B<simplearm>



=item B<simplearmv5te>



=item B<simplearmv6>



=item B<simpleneon>



=item B<xvid>



=item B<faani>

floating point AAN IDCT

=back



=item B<slice_count> I<integer>



=item B<ec> I<flags> B<(>I<decoding,video>B<)>

Set error concealment strategy.

Possible values:

=over 4


=item B<guess_mvs>

iterative motion vector (MV) search (slow)

=item B<deblock>

use strong deblock filter for damaged MBs

=item B<favor_inter>

favor predicting from the previous frame instead of the current

=back



=item B<bits_per_coded_sample> I<integer>



=item B<aspect> I<rational number> B<(>I<encoding,video>B<)>

Set sample aspect ratio.


=item B<sar> I<rational number> B<(>I<encoding,video>B<)>

Set sample aspect ratio. Alias to I<aspect>.


=item B<debug> I<flags> B<(>I<decoding/encoding,audio,video,subtitles>B<)>

Print specific debug info.

Possible values:

=over 4


=item B<pict>

picture info

=item B<rc>

rate control

=item B<bitstream>



=item B<mb_type>

macroblock (MB) type

=item B<qp>

per-block quantization parameter (QP)

=item B<dct_coeff>



=item B<green_metadata>

display complexity metadata for the upcoming frame, GoP or for a given duration.


=item B<skip>



=item B<startcode>



=item B<er>

error recognition

=item B<mmco>

memory management control operations (H.264)

=item B<bugs>



=item B<buffers>

picture buffer allocations

=item B<thread_ops>

threading operations

=item B<nomc>

skip motion compensation

=back



=item B<cmp> I<integer> B<(>I<encoding,video>B<)>

Set full pel me compare function.

Possible values:

=over 4


=item B<sad>

sum of absolute differences, fast (default)

=item B<sse>

sum of squared errors

=item B<satd>

sum of absolute Hadamard transformed differences

=item B<dct>

sum of absolute DCT transformed differences

=item B<psnr>

sum of squared quantization errors (avoid, low quality)

=item B<bit>

number of bits needed for the block

=item B<rd>

rate distortion optimal, slow

=item B<zero>

0

=item B<vsad>

sum of absolute vertical differences

=item B<vsse>

sum of squared vertical differences

=item B<nsse>

noise preserving sum of squared differences

=item B<w53>

5/3 wavelet, only used in snow

=item B<w97>

9/7 wavelet, only used in snow

=item B<dctmax>



=item B<chroma>



=back



=item B<subcmp> I<integer> B<(>I<encoding,video>B<)>

Set sub pel me compare function.

Possible values:

=over 4


=item B<sad>

sum of absolute differences, fast (default)

=item B<sse>

sum of squared errors

=item B<satd>

sum of absolute Hadamard transformed differences

=item B<dct>

sum of absolute DCT transformed differences

=item B<psnr>

sum of squared quantization errors (avoid, low quality)

=item B<bit>

number of bits needed for the block

=item B<rd>

rate distortion optimal, slow

=item B<zero>

0

=item B<vsad>

sum of absolute vertical differences

=item B<vsse>

sum of squared vertical differences

=item B<nsse>

noise preserving sum of squared differences

=item B<w53>

5/3 wavelet, only used in snow

=item B<w97>

9/7 wavelet, only used in snow

=item B<dctmax>



=item B<chroma>



=back



=item B<mbcmp> I<integer> B<(>I<encoding,video>B<)>

Set macroblock compare function.

Possible values:

=over 4


=item B<sad>

sum of absolute differences, fast (default)

=item B<sse>

sum of squared errors

=item B<satd>

sum of absolute Hadamard transformed differences

=item B<dct>

sum of absolute DCT transformed differences

=item B<psnr>

sum of squared quantization errors (avoid, low quality)

=item B<bit>

number of bits needed for the block

=item B<rd>

rate distortion optimal, slow

=item B<zero>

0

=item B<vsad>

sum of absolute vertical differences

=item B<vsse>

sum of squared vertical differences

=item B<nsse>

noise preserving sum of squared differences

=item B<w53>

5/3 wavelet, only used in snow

=item B<w97>

9/7 wavelet, only used in snow

=item B<dctmax>



=item B<chroma>



=back



=item B<ildctcmp> I<integer> B<(>I<encoding,video>B<)>

Set interlaced dct compare function.

Possible values:

=over 4


=item B<sad>

sum of absolute differences, fast (default)

=item B<sse>

sum of squared errors

=item B<satd>

sum of absolute Hadamard transformed differences

=item B<dct>

sum of absolute DCT transformed differences

=item B<psnr>

sum of squared quantization errors (avoid, low quality)

=item B<bit>

number of bits needed for the block

=item B<rd>

rate distortion optimal, slow

=item B<zero>

0

=item B<vsad>

sum of absolute vertical differences

=item B<vsse>

sum of squared vertical differences

=item B<nsse>

noise preserving sum of squared differences

=item B<w53>

5/3 wavelet, only used in snow

=item B<w97>

9/7 wavelet, only used in snow

=item B<dctmax>



=item B<chroma>



=back



=item B<dia_size> I<integer> B<(>I<encoding,video>B<)>

Set diamond type & size for motion estimation.

=over 4


=item B<(1024, INT_MAX)>

full motion estimation(slowest)

=item B<(768, 1024]>

umh motion estimation

=item B<(512, 768]>

hex motion estimation

=item B<(256, 512]>

l2s diamond motion estimation

=item B<[2,256]>

var diamond motion estimation

=item B<(-1,  2)>

small diamond motion estimation

=item B<-1>

funny diamond motion estimation

=item B<(INT_MIN, -1)>

sab diamond motion estimation

=back



=item B<last_pred> I<integer> B<(>I<encoding,video>B<)>

Set amount of motion predictors from the previous frame.


=item B<precmp> I<integer> B<(>I<encoding,video>B<)>

Set pre motion estimation compare function.

Possible values:

=over 4


=item B<sad>

sum of absolute differences, fast (default)

=item B<sse>

sum of squared errors

=item B<satd>

sum of absolute Hadamard transformed differences

=item B<dct>

sum of absolute DCT transformed differences

=item B<psnr>

sum of squared quantization errors (avoid, low quality)

=item B<bit>

number of bits needed for the block

=item B<rd>

rate distortion optimal, slow

=item B<zero>

0

=item B<vsad>

sum of absolute vertical differences

=item B<vsse>

sum of squared vertical differences

=item B<nsse>

noise preserving sum of squared differences

=item B<w53>

5/3 wavelet, only used in snow

=item B<w97>

9/7 wavelet, only used in snow

=item B<dctmax>



=item B<chroma>



=back



=item B<pre_dia_size> I<integer> B<(>I<encoding,video>B<)>

Set diamond type & size for motion estimation pre-pass.


=item B<subq> I<integer> B<(>I<encoding,video>B<)>

Set sub pel motion estimation quality.


=item B<me_range> I<integer> B<(>I<encoding,video>B<)>

Set limit motion vectors range (1023 for DivX player).


=item B<global_quality> I<integer> B<(>I<encoding,audio,video>B<)>



=item B<slice_flags> I<integer>



=item B<mbd> I<integer> B<(>I<encoding,video>B<)>

Set macroblock decision algorithm (high quality mode).

Possible values:

=over 4


=item B<simple>

use mbcmp (default)

=item B<bits>

use fewest bits

=item B<rd>

use best rate distortion

=back



=item B<rc_init_occupancy> I<integer> B<(>I<encoding,video>B<)>

Set number of bits which should be loaded into the rc buffer before
decoding starts.


=item B<flags2> I<flags> B<(>I<decoding/encoding,audio,video,subtitles>B<)>


Possible values:

=over 4


=item B<fast>

Allow non spec compliant speedup tricks.

=item B<noout>

Skip bitstream encoding.

=item B<ignorecrop>

Ignore cropping information from sps.

=item B<local_header>

Place global headers at every keyframe instead of in extradata.

=item B<chunks>

Frame data might be split into multiple chunks.

=item B<showall>

Show all frames before the first keyframe.

=item B<export_mvs>

Export motion vectors into frame side-data (see C<AV_FRAME_DATA_MOTION_VECTORS>)
for codecs that support it. See also F<doc/examples/export_mvs.c>.

=item B<skip_manual>

Do not skip samples and export skip information as frame side data.

=item B<ass_ro_flush_noop>

Do not reset ASS ReadOrder field on flush.

=item B<icc_profiles>

Generate/parse embedded ICC profiles from/to colorimetry tags.

=back



=item B<export_side_data> I<flags> B<(>I<decoding/encoding,audio,video,subtitles>B<)>


Possible values:

=over 4


=item B<mvs>

Export motion vectors into frame side-data (see C<AV_FRAME_DATA_MOTION_VECTORS>)
for codecs that support it. See also F<doc/examples/export_mvs.c>.

=item B<prft>

Export encoder Producer Reference Time into packet side-data (see C<AV_PKT_DATA_PRFT>)
for codecs that support it.

=item B<venc_params>

Export video encoding parameters through frame side data (see C<AV_FRAME_DATA_VIDEO_ENC_PARAMS>)
for codecs that support it. At present, those are H.264 and VP9.

=item B<film_grain>

Export film grain parameters through frame side data (see C<AV_FRAME_DATA_FILM_GRAIN_PARAMS>).
Supported at present by AV1 decoders.

=item B<enhancements>

Export picture enhancement metadata through frame side data, e.g. LCEVC (see C<AV_FRAME_DATA_LCEVC>).

=back



=item B<threads> I<integer> B<(>I<decoding/encoding,video>B<)>

Set the number of threads to be used, in case the selected codec
implementation supports multi-threading.

Possible values:

=over 4


=item B<auto, 0>

automatically select the number of threads to set

=back


Default value is B<auto>.


=item B<dc> I<integer> B<(>I<encoding,video>B<)>

Set intra_dc_precision.


=item B<nssew> I<integer> B<(>I<encoding,video>B<)>

Set nsse weight.


=item B<skip_top> I<integer> B<(>I<decoding,video>B<)>

Set number of macroblock rows at the top which are skipped.


=item B<skip_bottom> I<integer> B<(>I<decoding,video>B<)>

Set number of macroblock rows at the bottom which are skipped.


=item B<profile> I<integer> B<(>I<encoding,audio,video>B<)>


Set encoder codec profile. Default value is B<unknown>. Encoder specific
profiles are documented in the relevant encoder documentation.


=item B<level> I<integer> B<(>I<encoding,audio,video>B<)>


Set the encoder level. This level depends on the specific codec, and
might correspond to the profile level. It is set by default to
B<unknown>.

Possible values:

=over 4


=item B<unknown>


=back



=item B<lowres> I<integer> B<(>I<decoding,audio,video>B<)>

Decode at 1= 1/2, 2=1/4, 3=1/8 resolutions.


=item B<mblmin> I<integer> B<(>I<encoding,video>B<)>

Set min macroblock lagrange factor (VBR).


=item B<mblmax> I<integer> B<(>I<encoding,video>B<)>

Set max macroblock lagrange factor (VBR).


=item B<skip_loop_filter> I<integer> B<(>I<decoding,video>B<)>


=item B<skip_idct>        I<integer> B<(>I<decoding,video>B<)>


=item B<skip_frame>       I<integer> B<(>I<decoding,video>B<)>


Make decoder discard processing depending on the frame type selected
by the option value.

B<skip_loop_filter> skips frame loop filtering, B<skip_idct>
skips frame IDCT/dequantization, B<skip_frame> skips decoding.

Possible values:

=over 4


=item B<none>

Discard no frame.


=item B<default>

Discard useless frames like 0-sized frames.


=item B<noref>

Discard all non-reference frames.


=item B<bidir>

Discard all bidirectional frames.


=item B<nokey>

Discard all frames excepts keyframes.


=item B<nointra>

Discard all frames except I frames.


=item B<all>

Discard all frames.

=back


Default value is B<default>.


=item B<bidir_refine> I<integer> B<(>I<encoding,video>B<)>

Refine the two motion vectors used in bidirectional macroblocks.


=item B<keyint_min> I<integer> B<(>I<encoding,video>B<)>

Set minimum interval between IDR-frames.


=item B<refs> I<integer> B<(>I<encoding,video>B<)>

Set reference frames to consider for motion compensation.


=item B<trellis> I<integer> B<(>I<encoding,audio,video>B<)>

Set rate-distortion optimal quantization.


=item B<mv0_threshold> I<integer> B<(>I<encoding,video>B<)>


=item B<compression_level> I<integer> B<(>I<encoding,audio,video>B<)>


=item B<bits_per_raw_sample> I<integer>


=item B<channel_layout> I<integer> B<(>I<decoding/encoding,audio>B<)>

See B<the Channel Layout section in the ffmpeg-utils(1) manual>
for the required syntax.


=item B<rc_max_vbv_use> I<float> B<(>I<encoding,video>B<)>


=item B<rc_min_vbv_use> I<float> B<(>I<encoding,video>B<)>



=item B<color_primaries> I<integer> B<(>I<decoding/encoding,video>B<)>

Possible values:

=over 4


=item B<bt709>

BT.709

=item B<bt470m>

BT.470 M

=item B<bt470bg>

BT.470 BG

=item B<smpte170m>

SMPTE 170 M

=item B<smpte240m>

SMPTE 240 M

=item B<film>

Film

=item B<bt2020>

BT.2020

=item B<smpte428>


=item B<smpte428_1>

SMPTE ST 428-1

=item B<smpte431>

SMPTE 431-2

=item B<smpte432>

SMPTE 432-1

=item B<jedec-p22>

JEDEC P22

=back



=item B<color_trc> I<integer> B<(>I<decoding/encoding,video>B<)>

Possible values:

=over 4


=item B<bt709>

BT.709

=item B<gamma22>

BT.470 M

=item B<gamma28>

BT.470 BG

=item B<smpte170m>

SMPTE 170 M

=item B<smpte240m>

SMPTE 240 M

=item B<linear>

Linear

=item B<log>


=item B<log100>

Log

=item B<log_sqrt>


=item B<log316>

Log square root

=item B<iec61966_2_4>


=item B<iec61966-2-4>

IEC 61966-2-4

=item B<bt1361>


=item B<bt1361e>

BT.1361

=item B<iec61966_2_1>


=item B<iec61966-2-1>

IEC 61966-2-1

=item B<bt2020_10>


=item B<bt2020_10bit>

BT.2020 - 10 bit

=item B<bt2020_12>


=item B<bt2020_12bit>

BT.2020 - 12 bit

=item B<smpte2084>

SMPTE ST 2084

=item B<smpte428>


=item B<smpte428_1>

SMPTE ST 428-1

=item B<arib-std-b67>

ARIB STD-B67

=back



=item B<colorspace> I<integer> B<(>I<decoding/encoding,video>B<)>

Possible values:

=over 4


=item B<rgb>

RGB

=item B<bt709>

BT.709

=item B<fcc>

FCC

=item B<bt470bg>

BT.470 BG

=item B<smpte170m>

SMPTE 170 M

=item B<smpte240m>

SMPTE 240 M

=item B<ycocg>

YCOCG

=item B<bt2020nc>


=item B<bt2020_ncl>

BT.2020 NCL

=item B<bt2020c>


=item B<bt2020_cl>

BT.2020 CL

=item B<smpte2085>

SMPTE 2085

=item B<chroma-derived-nc>

Chroma-derived NCL

=item B<chroma-derived-c>

Chroma-derived CL

=item B<ictcp>

ICtCp

=back



=item B<color_range> I<integer> B<(>I<decoding/encoding,video>B<)>

If used as input parameter, it serves as a hint to the decoder, which
color_range the input has.
Possible values:

=over 4


=item B<tv>


=item B<mpeg>


=item B<limited>

MPEG (219*2^(n-8))

=item B<pc>


=item B<jpeg>


=item B<full>

JPEG (2^n-1)

=back



=item B<chroma_sample_location> I<integer> B<(>I<decoding/encoding,video>B<)>

Possible values:

=over 4


=item B<left>



=item B<center>



=item B<topleft>



=item B<top>



=item B<bottomleft>



=item B<bottom>



=back



=item B<log_level_offset> I<integer>

Set the log level offset.


=item B<slices> I<integer> B<(>I<encoding,video>B<)>

Number of slices, used in parallelized encoding.


=item B<thread_type> I<flags> B<(>I<decoding/encoding,video>B<)>

Select which multithreading methods to use.

Use of B<frame> will increase decoding delay by one frame per
thread, so clients which cannot provide future frames should not use
it.

Possible values:

=over 4


=item B<slice>

Decode more than one part of a single frame at once.

Multithreading using slices works only when the video was encoded with
slices.


=item B<frame>

Decode more than one frame at once.

=back


Default value is B<slice+frame>.


=item B<audio_service_type> I<integer> B<(>I<encoding,audio>B<)>

Set audio service type.

Possible values:

=over 4


=item B<ma>

Main Audio Service

=item B<ef>

Effects

=item B<vi>

Visually Impaired

=item B<hi>

Hearing Impaired

=item B<di>

Dialogue

=item B<co>

Commentary

=item B<em>

Emergency

=item B<vo>

Voice Over

=item B<ka>

Karaoke

=back



=item B<request_sample_fmt> I<sample_fmt> B<(>I<decoding,audio>B<)>

Set sample format audio decoders should prefer. Default value is
C<none>.


=item B<pkt_timebase> I<rational number>



=item B<sub_charenc> I<encoding> B<(>I<decoding,subtitles>B<)>

Set the input subtitles character encoding.


=item B<field_order>  I<field_order> B<(>I<video>B<)>

Set/override the field order of the video.
Possible values:

=over 4


=item B<progressive>

Progressive video

=item B<tt>

Interlaced video, top field coded and displayed first

=item B<bb>

Interlaced video, bottom field coded and displayed first

=item B<tb>

Interlaced video, top coded first, bottom displayed first

=item B<bt>

Interlaced video, bottom coded first, top displayed first

=back



=item B<skip_alpha> I<bool> B<(>I<decoding,video>B<)>

Set to 1 to disable processing alpha (transparency). This works like the
B<gray> flag in the B<flags> option which skips chroma information
instead of alpha. Default is 0.


=item B<codec_whitelist> I<list> B<(>I<input>B<)>

"," separated list of allowed decoders. By default all are allowed.


=item B<dump_separator> I<string> B<(>I<input>B<)>

Separator used to separate the fields printed on the command line about the
Stream parameters.
For example, to separate the fields with newlines and indentation:
	
	ffprobe -dump_separator "
	                          "  -i ~/videos/matrixbench_mpeg2.mpg



=item B<max_pixels> I<integer> B<(>I<decoding/encoding,video>B<)>

Maximum number of pixels per image. This value can be used to avoid out of
memory failures due to large images.


=item B<apply_cropping> I<bool> B<(>I<decoding,video>B<)>

Enable cropping if cropping parameters are multiples of the required
alignment for the left and top parameters. If the alignment is not met the
cropping will be partially applied to maintain alignment.
Default is 1 (enabled).
Note: The required alignment depends on if C<AV_CODEC_FLAG_UNALIGNED> is set and the
CPU. C<AV_CODEC_FLAG_UNALIGNED> cannot be changed from the command line. Also hardware
decoders will not apply left/top Cropping.



=back




=head1 DECODERS


Decoders are configured elements in FFmpeg which allow the decoding of
multimedia streams.

When you configure your FFmpeg build, all the supported native decoders
are enabled by default. Decoders requiring an external library must be enabled
manually via the corresponding C<--enable-lib> option. You can list all
available decoders using the configure option C<--list-decoders>.

You can disable all the decoders with the configure option
C<--disable-decoders> and selectively enable / disable single decoders
with the options C<--enable-decoder=I<DECODER>> /
C<--disable-decoder=I<DECODER>>.

The option C<-decoders> of the ff* tools will display the list of
enabled decoders.



=head1 VIDEO DECODERS


A description of some of the currently available video decoders
follows.


=head2 av1


AOMedia Video 1 (AV1) decoder.


=head3 Options



=over 4



=item B<operating_point>

Select an operating point of a scalable AV1 bitstream (0 - 31). Default is 0.


=back



=head2 hevc

HEVC (AKA ITU-T H.265 or ISO/IEC 23008-2) decoder.

The decoder supports MV-HEVC multiview streams with at most two views. Views to
be output are selected by supplying a list of view IDs to the decoder (the
B<view_ids> option). This option may be set either statically before
decoder init, or from the C<get_format()> callback - useful for the case
when the view count or IDs change dynamically during decoding.

Only the base layer is decoded by default.

Note that if you are using the C<ffmpeg> CLI tool, you should be using view
specifiers as documented in its manual, rather than the options documented here.


=head3 Options



=over 4



=item B<view_ids (MV-HEVC)>

Specify a list of view IDs that should be output. This option can also be set to
a single '-1', which will cause all views defined in the VPS to be decoded and
output.


=item B<view_ids_available (MV-HEVC)>

This option may be read by the caller to retrieve an array of view IDs available
in the active VPS. The array is empty for single-layer video.

The value of this option is guaranteed to be accurate when read from the
C<get_format()> callback. It may also be set at other times (e.g. after
opening the decoder), but the value is informational only and may be incorrect
(e.g. when the stream contains multiple distinct VPS NALUs).


=item B<view_pos_available (MV-HEVC)>

This option may be read by the caller to retrieve an array of view positions
(left, right, or unspecified) available in the active VPS, as
C<AVStereo3DView> values. When the array is available, its elements apply to
the corresponding elements of B<view_ids_available>, i.e.
C<view_pos_available[i]> contains the position of view with ID
C<view_ids_available[i]>.

Same validity restrictions as for B<view_ids_available> apply to
this option.


=back



=head2 rawvideo


Raw video decoder.

This decoder decodes rawvideo streams.


=head3 Options



=over 4


=item B<top> I<top_field_first>

Specify the assumed field type of the input video.

=over 4


=item B<-1>

the video is assumed to be progressive (default)

=item B<0>

bottom-field-first is assumed

=item B<1>

top-field-first is assumed

=back



=back



=head2 libdav1d


dav1d AV1 decoder.

libdav1d allows libavcodec to decode the AOMedia Video 1 (AV1) codec.
Requires the presence of the libdav1d headers and library during configuration.
You need to explicitly configure the build with C<--enable-libdav1d>.


=head3 Options


The following options are supported by the libdav1d wrapper.


=over 4



=item B<framethreads>

Set amount of frame threads to use during decoding. The default value is 0 (autodetect).
This option is deprecated for libdav1d E<gt>= 1.0 and will be removed in the future. Use the
option C<max_frame_delay> and the global option C<threads> instead.


=item B<tilethreads>

Set amount of tile threads to use during decoding. The default value is 0 (autodetect).
This option is deprecated for libdav1d E<gt>= 1.0 and will be removed in the future. Use the
global option C<threads> instead.


=item B<max_frame_delay>

Set max amount of frames the decoder may buffer internally. The default value is 0
(autodetect).


=item B<filmgrain>

Apply film grain to the decoded video if present in the bitstream. Defaults to the
internal default of the library.
This option is deprecated and will be removed in the future. See the global option
C<export_side_data> to export Film Grain parameters instead of applying it.


=item B<oppoint>

Select an operating point of a scalable AV1 bitstream (0 - 31). Defaults to the
internal default of the library.


=item B<alllayers>

Output all spatial layers of a scalable AV1 bitstream. The default value is false.


=back



=head2 libdavs2


AVS2-P2/IEEE1857.4 video decoder wrapper.

This decoder allows libavcodec to decode AVS2 streams with davs2 library.



=head2 libuavs3d


AVS3-P2/IEEE1857.10 video decoder.

libuavs3d allows libavcodec to decode AVS3 streams.
Requires the presence of the libuavs3d headers and library during configuration.
You need to explicitly configure the build with C<--enable-libuavs3d>.


=head3 Options


The following option is supported by the libuavs3d wrapper.


=over 4



=item B<frame_threads>

Set amount of frame threads to use during decoding. The default value is 0 (autodetect).


=back



=head2 libxevd


eXtra-fast Essential Video Decoder (XEVD) MPEG-5 EVC decoder wrapper.

This decoder requires the presence of the libxevd headers and library
during configuration. You need to explicitly configure the build with
B<--enable-libxevd>.

The xevd project website is at E<lt>B<https://github.com/mpeg5/xevd>E<gt>.


=head3 Options


The following options are supported by the libxevd wrapper.
The xevd-equivalent options or values are listed in parentheses for easy migration.

To get a more accurate and extensive documentation of the libxevd options,
invoke the command  C<xevd_app --help> or consult the libxevd documentation.


=over 4


=item B<threads (>I<threads>B<)>

Force to use a specific number of threads


=back



=head2 QSV Decoders


The family of Intel QuickSync Video decoders (VC1, MPEG-2, H.264, HEVC,
JPEG/MJPEG, VP8, VP9, AV1, VVC).


=head3 Common Options


The following options are supported by all qsv decoders.


=over 4



=item I<async_depth>

Internal parallelization depth, the higher the value the higher the latency.


=item I<gpu_copy>

A GPU-accelerated copy between video and system memory

=over 4


=item B<default>


=item B<on>


=item B<off>


=back



=back



=head3 HEVC Options

Extra options for hevc_qsv.


=over 4



=item I<load_plugin>

A user plugin to load in an internal session

=over 4


=item B<none>


=item B<hevc_sw>


=item B<hevc_hw>


=back



=item I<load_plugins>

A :-separate list of hexadecimal plugin UIDs to load in an internal session


=back



=head2 v210


Uncompressed 4:2:2 10-bit decoder.


=head3 Options



=over 4



=item B<custom_stride>

Set the line size of the v210 data in bytes. The default value is 0
(autodetect). You can use the special -1 value for a strideless v210 as seen in
BOXX files.


=back




=head1 AUDIO DECODERS


A description of some of the currently available audio decoders
follows.


=head2 ac3


AC-3 audio decoder.

This decoder implements part of ATSC A/52:2010 and ETSI TS 102 366, as well as
the undocumented RealAudio 3 (a.k.a. dnet).


=head3 AC-3 Decoder Options



=over 4



=item B<-drc_scale> I<value>

Dynamic Range Scale Factor. The factor to apply to dynamic range values
from the AC-3 stream. This factor is applied exponentially. The default value is 1.
There are 3 notable scale factor ranges:

=over 4


=item B<drc_scale == 0>

DRC disabled. Produces full range audio.

=item B<0 E<lt> drc_scale E<lt>= 1>

DRC enabled.  Applies a fraction of the stream DRC value.
Audio reproduction is between full range and full compression.

=item B<drc_scale E<gt> 1>

DRC enabled. Applies drc_scale asymmetrically.
Loud sounds are fully compressed.  Soft sounds are enhanced.

=back



=back



=head2 flac


FLAC audio decoder.

This decoder aims to implement the complete FLAC specification from Xiph.


=head3 FLAC Decoder options



=over 4



=item B<-use_buggy_lpc>

The lavc FLAC encoder used to produce buggy streams with high lpc values
(like the default value). This option makes it possible to decode such streams
correctly by using lavc's old buggy lpc logic for decoding.


=back



=head2 ffwavesynth


Internal wave synthesizer.

This decoder generates wave patterns according to predefined sequences. Its
use is purely internal and the format of the data it accepts is not publicly
documented.


=head2 libcelt


libcelt decoder wrapper.

libcelt allows libavcodec to decode the Xiph CELT ultra-low delay audio codec.
Requires the presence of the libcelt headers and library during configuration.
You need to explicitly configure the build with C<--enable-libcelt>.


=head2 libgsm


libgsm decoder wrapper.

libgsm allows libavcodec to decode the GSM full rate audio codec. Requires
the presence of the libgsm headers and library during configuration. You need
to explicitly configure the build with C<--enable-libgsm>.

This decoder supports both the ordinary GSM and the Microsoft variant.


=head2 libilbc


libilbc decoder wrapper.

libilbc allows libavcodec to decode the Internet Low Bitrate Codec (iLBC)
audio codec. Requires the presence of the libilbc headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libilbc>.


=head3 Options


The following option is supported by the libilbc wrapper.


=over 4


=item B<enhance>


Enable the enhancement of the decoded audio when set to 1. The default
value is 0 (disabled).


=back



=head2 libopencore-amrnb


libopencore-amrnb decoder wrapper.

libopencore-amrnb allows libavcodec to decode the Adaptive Multi-Rate
Narrowband audio codec. Using it requires the presence of the
libopencore-amrnb headers and library during configuration. You need to
explicitly configure the build with C<--enable-libopencore-amrnb>.

An FFmpeg native decoder for AMR-NB exists, so users can decode AMR-NB
without this library.


=head2 libopencore-amrwb


libopencore-amrwb decoder wrapper.

libopencore-amrwb allows libavcodec to decode the Adaptive Multi-Rate
Wideband audio codec. Using it requires the presence of the
libopencore-amrwb headers and library during configuration. You need to
explicitly configure the build with C<--enable-libopencore-amrwb>.

An FFmpeg native decoder for AMR-WB exists, so users can decode AMR-WB
without this library.


=head2 libopus


libopus decoder wrapper.

libopus allows libavcodec to decode the Opus Interactive Audio Codec.
Requires the presence of the libopus headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libopus>.

An FFmpeg native decoder for Opus exists, so users can decode Opus
without this library.



=head1 SUBTITLES DECODERS



=head2 libaribb24


ARIB STD-B24 caption decoder.

Implements profiles A and C of the ARIB STD-B24 standard.


=head3 libaribb24 Decoder Options



=over 4



=item B<-aribb24-base-path> I<path>

Sets the base path for the libaribb24 library. This is utilized for reading of
configuration files (for custom unicode conversions), and for dumping of
non-text symbols as images under that location.

Unset by default.


=item B<-aribb24-skip-ruby-text> I<boolean>

Tells the decoder wrapper to skip text blocks that contain half-height ruby
text.

Enabled by default.


=back



=head2 libaribcaption


Yet another ARIB STD-B24 caption decoder using external I<libaribcaption>
library.

Implements profiles A and C of the Japanese ARIB STD-B24 standard,
Brazilian ABNT NBR 15606-1, and Philippines version of ISDB-T.

Requires the presence of the libaribcaption headers and library
(E<lt>B<https://github.com/xqq/libaribcaption>E<gt>) during configuration.
You need to explicitly configure the build with C<--enable-libaribcaption>.
If both I<libaribb24> and I<libaribcaption> are enabled, I<libaribcaption>
decoder precedes.


=head3 libaribcaption Decoder Options



=over 4



=item B<-sub_type> I<subtitle_type>

Specifies the format of the decoded subtitles.


=over 4


=item B<bitmap>

Graphical image.

=item B<ass>

ASS formatted text.

=item B<text>

Simple text based output without formatting.

=back


The default is I<ass> as same as I<libaribb24> decoder.
Some present players (e.g., I<mpv>) expect ASS format for ARIB caption.


=item B<-caption_encoding> I<encoding_scheme>

Specifies the encoding scheme of input subtitle text.


=over 4


=item B<auto>

Automatically detect text encoding (default).

=item B<jis>

8bit-char JIS encoding defined in ARIB STD B24.
This encoding used in Japan for ISDB captions.

=item B<utf8>

UTF-8 encoding defined in ARIB STD B24.
This encoding is used in Philippines for ISDB-T captions.

=item B<latin>

Latin character encoding defined in ABNT NBR 15606-1.
This encoding is used in South America for SBTVD / ISDB-Tb captions.

=back



=item B<-font> I<font_name[,font_name2,...]>

Specify comma-separated list of font family names to be used for I<bitmap>
or I<ass> type subtitle rendering.
Only first font name is used for I<ass> type subtitle.

If not specified, use internally defined default font family.


=item B<-ass_single_rect> I<boolean>

ARIB STD-B24 specifies that some captions may be displayed at different
positions at a time (multi-rectangle subtitle).
Since some players (e.g., old I<mpv>) can't handle multiple ASS rectangles
in a single AVSubtitle, or multiple ASS rectangles of indeterminate duration
with the same start timestamp, this option can change the behavior so that
all the texts are displayed in a single ASS rectangle.

The default is I<false>.

If your player cannot handle AVSubtitles with multiple ASS rectangles properly,
set this option to I<true> or define B<ASS_SINGLE_RECT=1> to change
default behavior at compilation.


=item B<-force_outline_text> I<boolean>

Specify whether always render outline text for all characters regardless of
the indication by character style.

The default is I<false>.


=item B<-outline_width> I<number> B<(0.0 - 3.0)>

Specify width for outline text, in dots (relative).

The default is I<1.5>.


=item B<-ignore_background> I<boolean>

Specify whether to ignore background color rendering.

The default is I<false>.


=item B<-ignore_ruby> I<boolean>

Specify whether to ignore rendering for ruby-like (furigana) characters.

The default is I<false>.


=item B<-replace_drcs> I<boolean>

Specify whether to render replaced DRCS characters as Unicode characters.

The default is I<true>.


=item B<-replace_msz_ascii> I<boolean>

Specify whether to replace MSZ (Middle Size; half width) fullwidth
alphanumerics with halfwidth alphanumerics.

The default is I<true>.


=item B<-replace_msz_japanese> I<boolean>

Specify whether to replace some MSZ (Middle Size; half width) fullwidth
japanese special characters with halfwidth ones.

The default is I<true>.


=item B<-replace_msz_glyph> I<boolean>

Specify whether to replace MSZ (Middle Size; half width) characters
with halfwidth glyphs if the fonts supports it.
This option works under FreeType or DirectWrite renderer
with Adobe-Japan1 compliant fonts.
e.g., IBM Plex Sans JP, Morisawa BIZ UDGothic, Morisawa BIZ UDMincho,
Yu Gothic, Yu Mincho, and Meiryo.

The default is I<true>.


=item B<-canvas_size> I<image_size>

Specify the resolution of the canvas to render subtitles to; usually, this
should be frame size of input video.
This only applies when C<-subtitle_type> is set to I<bitmap>.

The libaribcaption decoder assumes input frame size for bitmap rendering as below:

=over 4


=item 1.

PROFILE_A : 1440 x 1080 with SAR (PAR) 4:3

=item 2.

PROFILE_C : 320 x 180 with SAR (PAR) 1:1

=back


If actual frame size of input video does not match above assumption,
the rendered captions may be distorted.
To make the captions undistorted, add C<-canvas_size> option to specify
actual input video size.

Note that the C<-canvas_size> option is not required for video with
different size but same aspect ratio.
In such cases, the caption will be stretched or shrunk to actual video size
if C<-canvas_size> option is not specified.
If C<-canvas_size> option is specified with different size,
the caption will be stretched or shrunk as specified size with calculated SAR.


=back



=head3 libaribcaption decoder usage examples


Display MPEG-TS file with ARIB subtitle by C<ffplay> tool:
	
	ffplay -sub_type bitmap MPEG.TS


Display MPEG-TS file with input frame size 1920x1080 by C<ffplay> tool:
	
	ffplay -sub_type bitmap -canvas_size 1920x1080 MPEG.TS


Embed ARIB subtitle in transcoded video:
	
	ffmpeg -sub_type bitmap -i src.m2t -filter_complex "[0:v][0:s]overlay" -vcodec h264 dest.mp4



=head2 dvbsub



=head3 Options



=over 4


=item B<compute_clut>


=over 4


=item B<-2>

Compute clut once if no matching CLUT is in the stream.

=item B<-1>

Compute clut if no matching CLUT is in the stream.

=item B<0>

Never compute CLUT

=item B<1>

Always compute CLUT and override the one provided in the stream.

=back


=item B<dvb_substream>

Selects the dvb substream, or all substreams if -1 which is default.


=back



=head2 dvdsub


This codec decodes the bitmap subtitles used in DVDs; the same subtitles can
also be found in VobSub file pairs and in some Matroska files.


=head3 Options



=over 4


=item B<palette>

Specify the global palette used by the bitmaps. When stored in VobSub, the
palette is normally specified in the index file; in Matroska, the palette is
stored in the codec extra-data in the same format as in VobSub. In DVDs, the
palette is stored in the IFO file, and therefore not available when reading
from dumped VOB files.

The format for this option is a string containing 16 24-bits hexadecimal
numbers (without 0x prefix) separated by commas, for example C<0d00ee,
ee450d, 101010, eaeaea, 0ce60b, ec14ed, ebff0b, 0d617a, 7b7b7b, d1d1d1,
7b2a0e, 0d950c, 0f007b, cf0dec, cfa80c, 7c127b>.


=item B<ifo_palette>

Specify the IFO file from which the global palette is obtained.
(experimental)


=item B<forced_subs_only>

Only decode subtitle entries marked as forced. Some titles have forced
and non-forced subtitles in the same track. Setting this flag to C<1>
will only keep the forced subtitles. Default value is C<0>.

=back



=head2 libzvbi-teletext


Libzvbi allows libavcodec to decode DVB teletext pages and DVB teletext
subtitles. Requires the presence of the libzvbi headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libzvbi>.


=head3 Options



=over 4


=item B<txt_page>

List of teletext page numbers to decode. Pages that do not match the specified
list are dropped. You may use the special C<*> string to match all pages,
or C<subtitle> to match all subtitle pages.
Default value is *.

=item B<txt_default_region>

Set default character set used for decoding, a value between 0 and 87 (see
ETS 300 706, Section 15, Table 32). Default value is -1, which does not
override the libzvbi default. This option is needed for some legacy level 1.0
transmissions which cannot signal the proper charset.

=item B<txt_chop_top>

Discards the top teletext line. Default value is 1.

=item B<txt_format>

Specifies the format of the decoded subtitles.

=over 4


=item B<bitmap>

The default format, you should use this for teletext pages, because certain
graphics and colors cannot be expressed in simple text or even ASS.

=item B<text>

Simple text based output without formatting.

=item B<ass>

Formatted ASS output, subtitle pages and teletext pages are returned in
different styles, subtitle pages are stripped down to text, but an effort is
made to keep the text alignment and the formatting.

=back


=item B<txt_left>

X offset of generated bitmaps, default is 0.

=item B<txt_top>

Y offset of generated bitmaps, default is 0.

=item B<txt_chop_spaces>

Chops leading and trailing spaces and removes empty lines from the generated
text. This option is useful for teletext based subtitles where empty spaces may
be present at the start or at the end of the lines or empty lines may be
present between the subtitle lines because of double-sized teletext characters.
Default value is 1.

=item B<txt_duration>

Sets the display duration of the decoded teletext pages or subtitles in
milliseconds. Default value is -1 which means infinity or until the next
subtitle event comes.

=item B<txt_transparent>

Force transparent background of the generated teletext bitmaps. Default value
is 0 which means an opaque background.

=item B<txt_opacity>

Sets the opacity (0-255) of the teletext background. If
B<txt_transparent> is not set, it only affects characters between a start
box and an end box, typically subtitles. Default value is 0 if
B<txt_transparent> is set, 255 otherwise.


=back



=head1 ENCODERS


Encoders are configured elements in FFmpeg which allow the encoding of
multimedia streams.

When you configure your FFmpeg build, all the supported native encoders
are enabled by default. Encoders requiring an external library must be enabled
manually via the corresponding C<--enable-lib> option. You can list all
available encoders using the configure option C<--list-encoders>.

You can disable all the encoders with the configure option
C<--disable-encoders> and selectively enable / disable single encoders
with the options C<--enable-encoder=I<ENCODER>> /
C<--disable-encoder=I<ENCODER>>.

The option C<-encoders> of the ff* tools will display the list of
enabled encoders.



=head1 AUDIO ENCODERS


A description of some of the currently available audio encoders
follows.



=head2 aac


Advanced Audio Coding (AAC) encoder.

This encoder is the default AAC encoder, natively implemented into FFmpeg.


=head3 Options



=over 4


=item B<b>

Set bit rate in bits/s. Setting this automatically activates constant bit rate
(CBR) mode. If this option is unspecified it is set to 128kbps.


=item B<q>

Set quality for variable bit rate (VBR) mode. This option is valid only using
the B<ffmpeg> command-line tool. For library interface users, use
B<global_quality>.


=item B<cutoff>

Set cutoff frequency. If unspecified will allow the encoder to dynamically
adjust the cutoff to improve clarity on low bitrates.


=item B<aac_coder>

Set AAC encoder coding method. Possible values:


=over 4


=item B<twoloop>

Two loop searching (TLS) method. This is the default method.

This method first sets quantizers depending on band thresholds and then tries
to find an optimal combination by adding or subtracting a specific value from
all quantizers and adjusting some individual quantizer a little.  Will tune
itself based on whether B<aac_is>, B<aac_ms> and B<aac_pns>
are enabled.


=item B<anmr>

Average noise to mask ratio (ANMR) trellis-based solution.

This is an experimental coder which currently produces a lower quality, is more
unstable and is slower than the default twoloop coder but has potential.
Currently has no support for the B<aac_is> or B<aac_pns> options.
Not currently recommended.


=item B<fast>

Constant quantizer method.

Uses a cheaper version of twoloop algorithm that doesn't try to do as many
clever adjustments. Worse with low bitrates (less than 64kbps), but is better
and much faster at higher bitrates.


=back



=item B<aac_ms>

Sets mid/side coding mode. The default value of "auto" will automatically use
M/S with bands which will benefit from such coding. Can be forced for all bands
using the value "enable", which is mainly useful for debugging or disabled using
"disable".


=item B<aac_is>

Sets intensity stereo coding tool usage. By default, it's enabled and will
automatically toggle IS for similar pairs of stereo bands if it's beneficial.
Can be disabled for debugging by setting the value to "disable".


=item B<aac_pns>

Uses perceptual noise substitution to replace low entropy high frequency bands
with imperceptible white noise during the decoding process. By default, it's
enabled, but can be disabled for debugging purposes by using "disable".


=item B<aac_tns>

Enables the use of a multitap FIR filter which spans through the high frequency
bands to hide quantization noise during the encoding process and is reverted
by the decoder. As well as decreasing unpleasant artifacts in the high range
this also reduces the entropy in the high bands and allows for more bits to
be used by the mid-low bands. By default it's enabled but can be disabled for
debugging by setting the option to "disable".


=item B<aac_ltp>

Enables the use of the long term prediction extension which increases coding
efficiency in very low bandwidth situations such as encoding of voice or
solo piano music by extending constant harmonic peaks in bands throughout
frames. This option is implied by profile:a aac_low.
Use in conjunction with B<-ar> to decrease the samplerate.


=item B<profile>

Sets the encoding profile, possible values:


=over 4


=item B<aac_low>

The default, AAC "Low-complexity" profile. Is the most compatible and produces
decent quality.


=item B<mpeg2_aac_low>

Equivalent to C<-profile:a aac_low -aac_pns 0>. PNS was introduced with the
MPEG4 specifications.


=item B<aac_ltp>

Long term prediction profile, is enabled by and will enable the B<aac_ltp>
option. Introduced in MPEG4.


=back

If this option is unspecified it is set to B<aac_low>.

=back



=head2 ac3 and ac3_fixed


AC-3 audio encoders.

These encoders implement part of ATSC A/52:2010 and ETSI TS 102 366.

The I<ac3> encoder uses floating-point math, while the I<ac3_fixed>
encoder only uses fixed-point integer math. This does not mean that one is
always faster, just that one or the other may be better suited to a
particular system. The I<ac3_fixed> encoder is not the default codec for
any of the output formats, so it must be specified explicitly using the option
C<-acodec ac3_fixed> in order to use it.


=head3 AC-3 Metadata


The AC-3 metadata options are used to set parameters that describe the audio,
but in most cases do not affect the audio encoding itself. Some of the options
do directly affect or influence the decoding and playback of the resulting
bitstream, while others are just for informational purposes. A few of the
options will add bits to the output stream that could otherwise be used for
audio data, and will thus affect the quality of the output. Those will be
indicated accordingly with a note in the option list below.

These parameters are described in detail in several publicly-available
documents.

=over 4


=item *<E<lt>B<http://www.atsc.org/cms/standards/a_52-2010.pdf>E<gt>>


=item *<E<lt>B<http://www.atsc.org/cms/standards/a_54a_with_corr_1.pdf>E<gt>>


=item *<E<lt>B<http://www.dolby.com/uploadedFiles/zz-_Shared_Assets/English_PDFs/Professional/18_Metadata.Guide.pdf>E<gt>>


=item *<E<lt>B<http://www.dolby.com/uploadedFiles/zz-_Shared_Assets/English_PDFs/Professional/46_DDEncodingGuidelines.pdf>E<gt>>


=back



=head4 Metadata Control Options



=over 4



=item B<-per_frame_metadata> I<boolean>

Allow Per-Frame Metadata. Specifies if the encoder should check for changing
metadata for each frame.

=over 4


=item B<0>

The metadata values set at initialization will be used for every frame in the
stream. (default)

=item B<1>

Metadata values can be changed before encoding each frame.

=back



=back



=head4 Downmix Levels



=over 4



=item B<-center_mixlev> I<level>

Center Mix Level. The amount of gain the decoder should apply to the center
channel when downmixing to stereo. This field will only be written to the
bitstream if a center channel is present. The value is specified as a scale
factor. There are 3 valid values:

=over 4


=item B<0.707>

Apply -3dB gain

=item B<0.595>

Apply -4.5dB gain (default)

=item B<0.500>

Apply -6dB gain

=back



=item B<-surround_mixlev> I<level>

Surround Mix Level. The amount of gain the decoder should apply to the surround
channel(s) when downmixing to stereo. This field will only be written to the
bitstream if one or more surround channels are present. The value is specified
as a scale factor.  There are 3 valid values:

=over 4


=item B<0.707>

Apply -3dB gain

=item B<0.500>

Apply -6dB gain (default)

=item B<0.000>

Silence Surround Channel(s)

=back



=back



=head4 Audio Production Information

Audio Production Information is optional information describing the mixing
environment.  Either none or both of the fields are written to the bitstream.


=over 4



=item B<-mixing_level> I<number>

Mixing Level. Specifies peak sound pressure level (SPL) in the production
environment when the mix was mastered. Valid values are 80 to 111, or -1 for
unknown or not indicated. The default value is -1, but that value cannot be
used if the Audio Production Information is written to the bitstream. Therefore,
if the C<room_type> option is not the default value, the C<mixing_level>
option must not be -1.


=item B<-room_type> I<type>

Room Type. Describes the equalization used during the final mixing session at
the studio or on the dubbing stage. A large room is a dubbing stage with the
industry standard X-curve equalization; a small room has flat equalization.
This field will not be written to the bitstream if both the C<mixing_level>
option and the C<room_type> option have the default values.

=over 4


=item B<0>


=item B<notindicated>

Not Indicated (default)

=item B<1>


=item B<large>

Large Room

=item B<2>


=item B<small>

Small Room

=back



=back



=head4 Other Metadata Options



=over 4



=item B<-copyright> I<boolean>

Copyright Indicator. Specifies whether a copyright exists for this audio.

=over 4


=item B<0>


=item B<off>

No Copyright Exists (default)

=item B<1>


=item B<on>

Copyright Exists

=back



=item B<-dialnorm> I<value>

Dialogue Normalization. Indicates how far the average dialogue level of the
program is below digital 100% full scale (0 dBFS). This parameter determines a
level shift during audio reproduction that sets the average volume of the
dialogue to a preset level. The goal is to match volume level between program
sources. A value of -31dB will result in no volume level change, relative to
the source volume, during audio reproduction. Valid values are whole numbers in
the range -31 to -1, with -31 being the default.


=item B<-dsur_mode> I<mode>

Dolby Surround Mode. Specifies whether the stereo signal uses Dolby Surround
(Pro Logic). This field will only be written to the bitstream if the audio
stream is stereo. Using this option does B<NOT> mean the encoder will actually
apply Dolby Surround processing.

=over 4


=item B<0>


=item B<notindicated>

Not Indicated (default)

=item B<1>


=item B<off>

Not Dolby Surround Encoded

=item B<2>


=item B<on>

Dolby Surround Encoded

=back



=item B<-original> I<boolean>

Original Bit Stream Indicator. Specifies whether this audio is from the
original source and not a copy.

=over 4


=item B<0>


=item B<off>

Not Original Source

=item B<1>


=item B<on>

Original Source (default)

=back



=back



=head3 Extended Bitstream Information

The extended bitstream options are part of the Alternate Bit Stream Syntax as
specified in Annex D of the A/52:2010 standard. It is grouped into 2 parts.
If any one parameter in a group is specified, all values in that group will be
written to the bitstream.  Default values are used for those that are written
but have not been specified.  If the mixing levels are written, the decoder
will use these values instead of the ones specified in the C<center_mixlev>
and C<surround_mixlev> options if it supports the Alternate Bit Stream
Syntax.


=head4 Extended Bitstream Information - Part 1



=over 4



=item B<-dmix_mode> I<mode>

Preferred Stereo Downmix Mode. Allows the user to select either Lt/Rt
(Dolby Surround) or Lo/Ro (normal stereo) as the preferred stereo downmix mode.

=over 4


=item B<0>


=item B<notindicated>

Not Indicated (default)

=item B<1>


=item B<ltrt>

Lt/Rt Downmix Preferred

=item B<2>


=item B<loro>

Lo/Ro Downmix Preferred

=back



=item B<-ltrt_cmixlev> I<level>

Lt/Rt Center Mix Level. The amount of gain the decoder should apply to the
center channel when downmixing to stereo in Lt/Rt mode.

=over 4


=item B<1.414>

Apply +3dB gain

=item B<1.189>

Apply +1.5dB gain

=item B<1.000>

Apply 0dB gain

=item B<0.841>

Apply -1.5dB gain

=item B<0.707>

Apply -3.0dB gain

=item B<0.595>

Apply -4.5dB gain (default)

=item B<0.500>

Apply -6.0dB gain

=item B<0.000>

Silence Center Channel

=back



=item B<-ltrt_surmixlev> I<level>

Lt/Rt Surround Mix Level. The amount of gain the decoder should apply to the
surround channel(s) when downmixing to stereo in Lt/Rt mode.

=over 4


=item B<0.841>

Apply -1.5dB gain

=item B<0.707>

Apply -3.0dB gain

=item B<0.595>

Apply -4.5dB gain

=item B<0.500>

Apply -6.0dB gain (default)

=item B<0.000>

Silence Surround Channel(s)

=back



=item B<-loro_cmixlev> I<level>

Lo/Ro Center Mix Level. The amount of gain the decoder should apply to the
center channel when downmixing to stereo in Lo/Ro mode.

=over 4


=item B<1.414>

Apply +3dB gain

=item B<1.189>

Apply +1.5dB gain

=item B<1.000>

Apply 0dB gain

=item B<0.841>

Apply -1.5dB gain

=item B<0.707>

Apply -3.0dB gain

=item B<0.595>

Apply -4.5dB gain (default)

=item B<0.500>

Apply -6.0dB gain

=item B<0.000>

Silence Center Channel

=back



=item B<-loro_surmixlev> I<level>

Lo/Ro Surround Mix Level. The amount of gain the decoder should apply to the
surround channel(s) when downmixing to stereo in Lo/Ro mode.

=over 4


=item B<0.841>

Apply -1.5dB gain

=item B<0.707>

Apply -3.0dB gain

=item B<0.595>

Apply -4.5dB gain

=item B<0.500>

Apply -6.0dB gain (default)

=item B<0.000>

Silence Surround Channel(s)

=back



=back



=head4 Extended Bitstream Information - Part 2



=over 4



=item B<-dsurex_mode> I<mode>

Dolby Surround EX Mode. Indicates whether the stream uses Dolby Surround EX
(7.1 matrixed to 5.1). Using this option does B<NOT> mean the encoder will actually
apply Dolby Surround EX processing.

=over 4


=item B<0>


=item B<notindicated>

Not Indicated (default)

=item B<1>


=item B<on>

Dolby Surround EX Off

=item B<2>


=item B<off>

Dolby Surround EX On

=back



=item B<-dheadphone_mode> I<mode>

Dolby Headphone Mode. Indicates whether the stream uses Dolby Headphone
encoding (multi-channel matrixed to 2.0 for use with headphones). Using this
option does B<NOT> mean the encoder will actually apply Dolby Headphone
processing.

=over 4


=item B<0>


=item B<notindicated>

Not Indicated (default)

=item B<1>


=item B<on>

Dolby Headphone Off

=item B<2>


=item B<off>

Dolby Headphone On

=back



=item B<-ad_conv_type> I<type>

A/D Converter Type. Indicates whether the audio has passed through HDCD A/D
conversion.

=over 4


=item B<0>


=item B<standard>

Standard A/D Converter (default)

=item B<1>


=item B<hdcd>

HDCD A/D Converter

=back



=back



=head3 Other AC-3 Encoding Options



=over 4



=item B<-stereo_rematrixing> I<boolean>

Stereo Rematrixing. Enables/Disables use of rematrixing for stereo input. This
is an optional AC-3 feature that increases quality by selectively encoding
the left/right channels as mid/side. This option is enabled by default, and it
is highly recommended that it be left as enabled except for testing purposes.


=item B<cutoff> I<frequency>

Set lowpass cutoff frequency. If unspecified, the encoder selects a default
determined by various other encoding parameters.


=back



=head3 Floating-Point-Only AC-3 Encoding Options


These options are only valid for the floating-point encoder and do not exist
for the fixed-point encoder due to the corresponding features not being
implemented in fixed-point.


=over 4



=item B<-channel_coupling> I<boolean>

Enables/Disables use of channel coupling, which is an optional AC-3 feature
that increases quality by combining high frequency information from multiple
channels into a single channel. The per-channel high frequency information is
sent with less accuracy in both the frequency and time domains. This allows
more bits to be used for lower frequencies while preserving enough information
to reconstruct the high frequencies. This option is enabled by default for the
floating-point encoder and should generally be left as enabled except for
testing purposes or to increase encoding speed.

=over 4


=item B<-1>


=item B<auto>

Selected by Encoder (default)

=item B<0>


=item B<off>

Disable Channel Coupling

=item B<1>


=item B<on>

Enable Channel Coupling

=back



=item B<-cpl_start_band> I<number>

Coupling Start Band. Sets the channel coupling start band, from 1 to 15. If a
value higher than the bandwidth is used, it will be reduced to 1 less than the
coupling end band. If I<auto> is used, the start band will be determined by
the encoder based on the bit rate, sample rate, and channel layout. This option
has no effect if channel coupling is disabled.

=over 4


=item B<-1>


=item B<auto>

Selected by Encoder (default)

=back



=back




=head2 flac


FLAC (Free Lossless Audio Codec) Encoder


=head3 Options


The following options are supported by FFmpeg's flac encoder.


=over 4


=item B<compression_level>

Sets the compression level, which chooses defaults for many other options
if they are not set explicitly. Valid values are from 0 to 12, 5 is the
default.


=item B<frame_size>

Sets the size of the frames in samples per channel.


=item B<lpc_coeff_precision>

Sets the LPC coefficient precision, valid values are from 1 to 15, 15 is the
default.


=item B<lpc_type>

Sets the first stage LPC algorithm

=over 4


=item B<none>

LPC is not used


=item B<fixed>

fixed LPC coefficients


=item B<levinson>



=item B<cholesky>


=back



=item B<lpc_passes>

Number of passes to use for Cholesky factorization during LPC analysis


=item B<min_partition_order>

The minimum partition order


=item B<max_partition_order>

The maximum partition order


=item B<prediction_order_method>


=over 4


=item B<estimation>


=item B<2level>


=item B<4level>


=item B<8level>


=item B<search>

Bruteforce search

=item B<log>


=back



=item B<ch_mode>

Channel mode

=over 4


=item B<auto>

The mode is chosen automatically for each frame

=item B<indep>

Channels are independently coded

=item B<left_side>


=item B<right_side>


=item B<mid_side>


=back



=item B<exact_rice_parameters>

Chooses if rice parameters are calculated exactly or approximately.
if set to 1 then they are chosen exactly, which slows the code down slightly and
improves compression slightly.


=item B<multi_dim_quant>

Multi Dimensional Quantization. If set to 1 then a 2nd stage LPC algorithm is
applied after the first stage to finetune the coefficients. This is quite slow
and slightly improves compression.


=back




=head2 opus


Opus encoder.

This is a native FFmpeg encoder for the Opus format. Currently, it's in development and
only implements the CELT part of the codec. Its quality is usually worse and at best
is equal to the libopus encoder.


=head3 Options



=over 4


=item B<b>

Set bit rate in bits/s. If unspecified it uses the number of channels and the layout
to make a good guess.


=item B<opus_delay>

Sets the maximum delay in milliseconds. Lower delays than 20ms will very quickly
decrease quality.

=back




=head2 libfdk_aac


libfdk-aac AAC (Advanced Audio Coding) encoder wrapper.

The libfdk-aac library is based on the Fraunhofer FDK AAC code from
the Android project.

Requires the presence of the libfdk-aac headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libfdk-aac>. The library is also incompatible with GPL,
so if you allow the use of GPL, you should configure with
C<--enable-gpl --enable-nonfree --enable-libfdk-aac>.

This encoder has support for the AAC-HE profiles.

VBR encoding, enabled through the B<vbr> or B<flags
+qscale> options, is experimental and only works with some
combinations of parameters.

Support for encoding 7.1 audio is only available with libfdk-aac 0.1.3 or
higher.

For more information see the fdk-aac project at
E<lt>B<http://sourceforge.net/p/opencore-amr/fdk-aac/>E<gt>.


=head3 Options


The following options are mapped on the shared FFmpeg codec options.


=over 4


=item B<b>

Set bit rate in bits/s. If the bitrate is not explicitly specified, it
is automatically set to a suitable value depending on the selected
profile.

In case VBR mode is enabled the option is ignored.


=item B<ar>

Set audio sampling rate (in Hz).


=item B<channels>

Set the number of audio channels.


=item B<flags +qscale>

Enable fixed quality, VBR (Variable Bit Rate) mode.
Note that VBR is implicitly enabled when the B<vbr> value is
positive.


=item B<cutoff>

Set cutoff frequency. If not specified (or explicitly set to 0) it
will use a value automatically computed by the library. Default value
is 0.


=item B<profile>

Set audio profile.

The following profiles are recognized:

=over 4


=item B<aac_low>

Low Complexity AAC (LC)


=item B<aac_he>

High Efficiency AAC (HE-AAC)


=item B<aac_he_v2>

High Efficiency AAC version 2 (HE-AACv2)


=item B<aac_ld>

Low Delay AAC (LD)


=item B<aac_eld>

Enhanced Low Delay AAC (ELD)

=back


If not specified it is set to B<aac_low>.

=back


The following are private options of the libfdk_aac encoder.


=over 4


=item B<afterburner>

Enable afterburner feature if set to 1, disabled if set to 0. This
improves the quality but also the required processing power.

Default value is 1.


=item B<eld_sbr>

Enable SBR (Spectral Band Replication) for ELD if set to 1, disabled
if set to 0.

Default value is 0.


=item B<eld_v2>

Enable ELDv2 (LD-MPS extension for ELD stereo signals) for ELDv2 if set to 1,
disabled if set to 0.

Note that option is available when fdk-aac version (AACENCODER_LIB_VL0.AACENCODER_LIB_VL1.AACENCODER_LIB_VL2) E<gt> (4.0.0).

Default value is 0.


=item B<signaling>

Set SBR/PS signaling style.

It can assume one of the following values:

=over 4


=item B<default>

choose signaling implicitly (explicit hierarchical by default,
implicit if global header is disabled)


=item B<implicit>

implicit backwards compatible signaling


=item B<explicit_sbr>

explicit SBR, implicit PS signaling


=item B<explicit_hierarchical>

explicit hierarchical signaling

=back


Default value is B<default>.


=item B<latm>

Output LATM/LOAS encapsulated data if set to 1, disabled if set to 0.

Default value is 0.


=item B<header_period>

Set StreamMuxConfig and PCE repetition period (in frames) for sending
in-band configuration buffers within LATM/LOAS transport layer.

Must be a 16-bits non-negative integer.

Default value is 0.


=item B<vbr>

Set VBR mode, from 1 to 5. 1 is lowest quality (though still pretty
good) and 5 is highest quality. A value of 0 will disable VBR, and CBR
(Constant Bit Rate) is enabled.

Currently only the B<aac_low> profile supports VBR encoding.

VBR modes 1-5 correspond to roughly the following average bit rates:


=over 4


=item B<1>

32 kbps/channel

=item B<2>

40 kbps/channel

=item B<3>

48-56 kbps/channel

=item B<4>

64 kbps/channel

=item B<5>

about 80-96 kbps/channel

=back


Default value is 0.


=item B<frame_length>

Set the audio frame length in samples. Default value is the internal
default of the library. Refer to the library's documentation for information
about supported values.

=back



=head3 Examples



=over 4


=item *

Use B<ffmpeg> to convert an audio file to VBR AAC in an M4A (MP4)
container:
	
	ffmpeg -i input.wav -codec:a libfdk_aac -vbr 3 output.m4a



=item *

Use B<ffmpeg> to convert an audio file to CBR 64k kbps AAC, using the
High-Efficiency AAC profile:
	
	ffmpeg -i input.wav -c:a libfdk_aac -profile:a aac_he -b:a 64k output.m4a


=back




=head2 liblc3


liblc3 LC3 (Low Complexity Communication Codec) encoder wrapper.

Requires the presence of the liblc3 headers and library during configuration.
You need to explicitly configure the build with C<--enable-liblc3>.

This encoder has support for the Bluetooth SIG LC3 codec for the LE Audio
protocol, and the following features of LC3plus:

=over 4


=item *

Frame duration of 2.5 and 5ms.

=item *

High-Resolution mode, 48 KHz, and 96 kHz sampling rates.

=back


For more information see the liblc3 project at
E<lt>B<https://github.com/google/liblc3>E<gt>.


=head3 Options


The following options are mapped on the shared FFmpeg codec options.


=over 4


=item B<b> I<bitrate>

Set the bit rate in bits/s. This will determine the fixed size of the encoded
frames, for a selected frame duration.


=item B<ar> I<frequency>

Set the audio sampling rate (in Hz).


=item B<channels>

Set the number of audio channels.


=item B<frame_duration>

Set the audio frame duration in milliseconds. Default value is 10ms.
Allowed frame durations are 2.5ms, 5ms, 7.5ms and 10ms.
LC3 (Bluetooth LE Audio), allows 7.5ms and 10ms; and LC3plus 2.5ms, 5ms
and 10ms.

The 10ms frame duration is available in LC3 and LC3 plus standard.
In this mode, the produced bitstream can be referenced either as LC3 or LC3plus.


=item B<high_resolution> I<boolean>

Enable the high-resolution mode if set to 1. The high-resolution mode is
available with all LC3plus frame durations and for a sampling rate of 48 KHz,
and 96 KHz.

The encoder automatically turns off this mode at lower sampling rates and
activates it at 96 KHz.

This mode should be preferred at high bitrates. In this mode, the audio
bandwidth is always up to the Nyquist frequency, compared to LC3 at 48 KHz,
which limits the bandwidth to 20 KHz.

=back




=head2 libmp3lame


LAME (Lame Ain't an MP3 Encoder) MP3 encoder wrapper.

Requires the presence of the libmp3lame headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libmp3lame>.

See B<libshine> for a fixed-point MP3 encoder, although with a
lower quality.


=head3 Options


The following options are supported by the libmp3lame wrapper. The
B<lame>-equivalent of the options are listed in parentheses.


=over 4


=item B<b (>I<-b>B<)>

Set bitrate expressed in bits/s for CBR or ABR. LAME C<bitrate> is
expressed in kilobits/s.


=item B<q (>I<-V>B<)>

Set constant quality setting for VBR. This option is valid only
using the B<ffmpeg> command-line tool. For library interface
users, use B<global_quality>.


=item B<compression_level (>I<-q>B<)>

Set algorithm quality. Valid arguments are integers in the 0-9 range,
with 0 meaning highest quality but slowest, and 9 meaning fastest
while producing the worst quality.


=item B<cutoff (>I<--lowpass>B<)>

Set lowpass cutoff frequency. If unspecified, the encoder dynamically
adjusts the cutoff.


=item B<reservoir>

Enable use of bit reservoir when set to 1. Default value is 1. LAME
has this enabled by default, but can be overridden by use
B<--nores> option.


=item B<joint_stereo (>I<-m j>B<)>

Enable the encoder to use (on a frame by frame basis) either L/R
stereo or mid/side stereo. Default value is 1.


=item B<abr (>I<--abr>B<)>

Enable the encoder to use ABR when set to 1. The B<lame>
B<--abr> sets the target bitrate, while this options only
tells FFmpeg to use ABR still relies on B<b> to set bitrate.


=item B<copyright (>I<-c>B<)>

Set MPEG audio copyright flag when set to 1. The default value is 0
(disabled).


=item B<original (>I<-o>B<)>

Set MPEG audio original flag when set to 1. The default value is 1
(enabled).

=back



=head2 libopencore-amrnb


OpenCORE Adaptive Multi-Rate Narrowband encoder.

Requires the presence of the libopencore-amrnb headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libopencore-amrnb --enable-version3>.

This is a mono-only encoder. Officially it only supports 8000Hz sample rate,
but you can override it by setting B<strict> to B<unofficial> or
lower.


=head3 Options



=over 4



=item B<b>

Set bitrate in bits per second. Only the following bitrates are supported,
otherwise libavcodec will round to the nearest valid bitrate.


=over 4


=item B<4750>


=item B<5150>


=item B<5900>


=item B<6700>


=item B<7400>


=item B<7950>


=item B<10200>


=item B<12200>


=back



=item B<dtx>

Allow discontinuous transmission (generate comfort noise) when set to 1. The
default value is 0 (disabled).


=back



=head2 libopus


libopus Opus Interactive Audio Codec encoder wrapper.

Requires the presence of the libopus headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libopus>.


=head3 Option Mapping


Most libopus options are modelled after the B<opusenc> utility from
opus-tools. The following is an option mapping chart describing options
supported by the libopus wrapper, and their B<opusenc>-equivalent
in parentheses.


=over 4



=item B<b (>I<bitrate>B<)>

Set the bit rate in bits/s.  FFmpeg's B<b> option is
expressed in bits/s, while B<opusenc>'s B<bitrate> in
kilobits/s.


=item B<vbr (>I<vbr>B<,> I<hard-cbr>B<, and> I<cvbr>B<)>

Set VBR mode. The FFmpeg B<vbr> option has the following
valid arguments, with the B<opusenc> equivalent options
in parentheses:


=over 4


=item B<off (>I<hard-cbr>B<)>

Use constant bit rate encoding.


=item B<on (>I<vbr>B<)>

Use variable bit rate encoding (the default).


=item B<constrained (>I<cvbr>B<)>

Use constrained variable bit rate encoding.

=back



=item B<compression_level (>I<comp>B<)>

Set encoding algorithm complexity. Valid options are integers in
the 0-10 range. 0 gives the fastest encodes but lower quality, while 10
gives the highest quality but slowest encoding. The default is 10.


=item B<frame_duration (>I<framesize>B<)>

Set maximum frame size, or duration of a frame in milliseconds. The
argument must be exactly the following: 2.5, 5, 10, 20, 40, 60. Smaller
frame sizes achieve lower latency but less quality at a given bitrate.
Sizes greater than 20ms are only interesting at fairly low bitrates.
The default is 20ms.


=item B<packet_loss (>I<expect-loss>B<)>

Set expected packet loss percentage. The default is 0.


=item B<fec (>I<n/a>B<)>

Enable inband forward error correction. B<packet_loss> must be non-zero
to take advantage - frequency of FEC 'side-data' is proportional to expected packet loss.
Default is disabled.


=item B<application (N.A.)>

Set intended application type. Valid options are listed below:


=over 4


=item B<voip>

Favor improved speech intelligibility.

=item B<audio>

Favor faithfulness to the input (the default).

=item B<lowdelay>

Restrict to only the lowest delay modes by disabling voice-optimized
modes.

=back



=item B<cutoff (N.A.)>

Set cutoff bandwidth in Hz. The argument must be exactly one of the
following: 4000, 6000, 8000, 12000, or 20000, corresponding to
narrowband, mediumband, wideband, super wideband, and fullband
respectively. The default is 0 (cutoff disabled). Note that libopus
forces a wideband cutoff for bitrates E<lt> 15 kbps, unless CELT-only
(B<application> set to B<lowdelay>) mode is used.


=item B<mapping_family (>I<mapping_family>B<)>

Set channel mapping family to be used by the encoder. The default value of -1
uses mapping family 0 for mono and stereo inputs, and mapping family 1
otherwise. The default also disables the surround masking and LFE bandwidth
optimizations in libopus, and requires that the input contains 8 channels or
fewer.

Other values include 0 for mono and stereo, 1 for surround sound with masking
and LFE bandwidth optimizations, and 255 for independent streams with an
unspecified channel layout.


=item B<apply_phase_inv (N.A.) (requires libopus E<gt>= 1.2)>

If set to 0, disables the use of phase inversion for intensity stereo,
improving the quality of mono downmixes, but slightly reducing normal stereo
quality. The default is 1 (phase inversion enabled).


=back




=head2 libshine


Shine Fixed-Point MP3 encoder wrapper.

Shine is a fixed-point MP3 encoder. It has a far better performance on
platforms without an FPU, e.g. armel CPUs, and some phones and tablets.
However, as it is more targeted on performance than quality, it is not on par
with LAME and other production-grade encoders quality-wise. Also, according to
the project's homepage, this encoder may not be free of bugs as the code was
written a long time ago and the project was dead for at least 5 years.

This encoder only supports stereo and mono input. This is also CBR-only.

The original project (last updated in early 2007) is at
E<lt>B<http://sourceforge.net/projects/libshine-fxp/>E<gt>. We only support the
updated fork by the Savonet/Liquidsoap project at E<lt>B<https://github.com/savonet/shine>E<gt>.

Requires the presence of the libshine headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libshine>.

See also B<libmp3lame>.


=head3 Options


The following options are supported by the libshine wrapper. The
B<shineenc>-equivalent of the options are listed in parentheses.


=over 4


=item B<b (>I<-b>B<)>

Set bitrate expressed in bits/s for CBR. B<shineenc> B<-b> option
is expressed in kilobits/s.


=back



=head2 libtwolame


TwoLAME MP2 encoder wrapper.

Requires the presence of the libtwolame headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libtwolame>.


=head3 Options


The following options are supported by the libtwolame wrapper. The
B<twolame>-equivalent options follow the FFmpeg ones and are in
parentheses.


=over 4


=item B<b (>I<-b>B<)>

Set bitrate expressed in bits/s for CBR. B<twolame> B<b>
option is expressed in kilobits/s. Default value is 128k.


=item B<q (>I<-V>B<)>

Set quality for experimental VBR support. Maximum value range is
from -50 to 50, useful range is from -10 to 10. The higher the
value, the better the quality. This option is valid only using the
B<ffmpeg> command-line tool. For library interface users,
use B<global_quality>.


=item B<mode (>I<--mode>B<)>

Set the mode of the resulting audio. Possible values:


=over 4


=item B<auto>

Choose mode automatically based on the input. This is the default.

=item B<stereo>

Stereo

=item B<joint_stereo>

Joint stereo

=item B<dual_channel>

Dual channel

=item B<mono>

Mono

=back



=item B<psymodel (>I<--psyc-mode>B<)>

Set psychoacoustic model to use in encoding. The argument must be
an integer between -1 and 4, inclusive. The higher the value, the
better the quality. The default value is 3.


=item B<energy_levels (>I<--energy>B<)>

Enable energy levels extensions when set to 1. The default value is
0 (disabled).


=item B<error_protection (>I<--protect>B<)>

Enable CRC error protection when set to 1. The default value is 0
(disabled).


=item B<copyright (>I<--copyright>B<)>

Set MPEG audio copyright flag when set to 1. The default value is 0
(disabled).


=item B<original (>I<--original>B<)>

Set MPEG audio original flag when set to 1. The default value is 0
(disabled).


=back



=head2 libvo-amrwbenc


VisualOn Adaptive Multi-Rate Wideband encoder.

Requires the presence of the libvo-amrwbenc headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libvo-amrwbenc --enable-version3>.

This is a mono-only encoder. Officially it only supports 16000Hz sample
rate, but you can override it by setting B<strict> to
B<unofficial> or lower.


=head3 Options



=over 4



=item B<b>

Set bitrate in bits/s. Only the following bitrates are supported, otherwise
libavcodec will round to the nearest valid bitrate.


=over 4


=item B<6600>


=item B<8850>


=item B<12650>


=item B<14250>


=item B<15850>


=item B<18250>


=item B<19850>


=item B<23050>


=item B<23850>


=back



=item B<dtx>

Allow discontinuous transmission (generate comfort noise) when set to 1. The
default value is 0 (disabled).


=back



=head2 libvorbis


libvorbis encoder wrapper.

Requires the presence of the libvorbisenc headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libvorbis>.


=head3 Options


The following options are supported by the libvorbis wrapper. The
B<oggenc>-equivalent of the options are listed in parentheses.

To get a more accurate and extensive documentation of the libvorbis
options, consult the libvorbisenc's and B<oggenc>'s documentations.
See E<lt>B<http://xiph.org/vorbis/>E<gt>,
E<lt>B<http://wiki.xiph.org/Vorbis-tools>E<gt>, and oggenc(1).


=over 4


=item B<b (>I<-b>B<)>

Set bitrate expressed in bits/s for ABR. B<oggenc> B<-b> is
expressed in kilobits/s.


=item B<q (>I<-q>B<)>

Set constant quality setting for VBR. The value should be a float
number in the range of -1.0 to 10.0. The higher the value, the better
the quality. The default value is B<3.0>.

This option is valid only using the B<ffmpeg> command-line tool.
For library interface users, use B<global_quality>.


=item B<cutoff (>I<--advanced-encode-option lowpass_frequency=N>B<)>

Set cutoff bandwidth in Hz, a value of 0 disables cutoff. B<oggenc>'s
related option is expressed in kHz. The default value is B<0> (cutoff
disabled).


=item B<minrate (>I<-m>B<)>

Set minimum bitrate expressed in bits/s. B<oggenc> B<-m> is
expressed in kilobits/s.


=item B<maxrate (>I<-M>B<)>

Set maximum bitrate expressed in bits/s. B<oggenc> B<-M> is
expressed in kilobits/s. This only has effect on ABR mode.


=item B<iblock (>I<--advanced-encode-option impulse_noisetune=N>B<)>

Set noise floor bias for impulse blocks. The value is a float number from
-15.0 to 0.0. A negative bias instructs the encoder to pay special attention
to the crispness of transients in the encoded audio. The tradeoff for better
transient response is a higher bitrate.


=back




=head2 mjpeg


Motion JPEG encoder.


=head3 Options



=over 4


=item B<huffman>

Set the huffman encoding strategy. Possible values:


=over 4


=item B<default>

Use the default huffman tables. This is the default strategy.


=item B<optimal>

Compute and use optimal huffman tables.


=back


=back




=head2 wavpack


WavPack lossless audio encoder.


=head3 Options


The equivalent options for B<wavpack> command line utility are listed in
parentheses.


=head4 Shared options


The following shared options are effective for this encoder. Only special notes
about this particular encoder will be documented here. For the general meaning
of the options, see B<the Codec Options chapter>.


=over 4


=item B<frame_size (>I<--blocksize>B<)>

For this encoder, the range for this option is between 128 and 131072. Default
is automatically decided based on sample rate and number of channel.

For the complete formula of calculating default, see
F<libavcodec/wavpackenc.c>.


=item B<compression_level (>I<-f>B<,> I<-h>B<,> I<-hh>B<, and> I<-x>B<)>


=back



=head4 Private options



=over 4


=item B<joint_stereo (>I<-j>B<)>

Set whether to enable joint stereo. Valid values are:


=over 4


=item B<on (>I<1>B<)>

Force mid/side audio encoding.

=item B<off (>I<0>B<)>

Force left/right audio encoding.

=item B<auto>

Let the encoder decide automatically.

=back



=item B<optimize_mono>

Set whether to enable optimization for mono. This option is only effective for
non-mono streams. Available values:


=over 4


=item B<on>

enabled

=item B<off>

disabled

=back



=back




=head1 VIDEO ENCODERS


A description of some of the currently available video encoders
follows.


=head2 a64_multi, a64_multi5


A64 / Commodore 64 multicolor charset encoder. C<a64_multi5> is extended with 5th color (colram).


=head2 Cinepak


Cinepak aka CVID encoder.
Compatible with Windows 3.1 and vintage MacOS.


=head3 Options



=over 4


=item B<g> I<integer>

Keyframe interval.
A keyframe is inserted at least every C<-g> frames, sometimes sooner.


=item B<q:v> I<integer>

Quality factor. Lower is better. Higher gives lower bitrate.
The following table lists bitrates when encoding akiyo_cif.y4m for various values of C<-q:v> with C<-g 100>:


=over 4


=item B<C<-q:v 1> 1918 kb/s>


=item B<C<-q:v 2> 1735 kb/s>


=item B<C<-q:v 4> 1500 kb/s>


=item B<C<-q:v 10> 1041 kb/s>


=item B<C<-q:v 20> 826 kb/s>


=item B<C<-q:v 40> 553 kb/s>


=item B<C<-q:v 100> 394 kb/s>


=item B<C<-q:v 200> 312 kb/s>


=item B<C<-q:v 400> 266 kb/s>


=item B<C<-q:v 1000> 237 kb/s>


=back



=item B<max_extra_cb_iterations> I<integer>

Max extra codebook recalculation passes, more is better and slower.


=item B<skip_empty_cb> I<boolean>

Avoid wasting bytes, ignore vintage MacOS decoder.


=item B<max_strips> I<integer>


=item B<min_strips> I<integer>

The minimum and maximum number of strips to use.
Wider range sometimes improves quality.
More strips is generally better quality but costs more bits.
Fewer strips tend to yield more keyframes.
Vintage compatible is 1..3.


=item B<strip_number_adaptivity> I<integer>

How much number of strips is allowed to change between frames.
Higher is better but slower.


=back




=head2 ffv1


FFv1 Encoder


=head3 Options


The following options are supported by FFmpeg's FFv1 encoder.


=over 4


=item B<context>

Sets the context size, 0 (default) is small, 1 is big.


=item B<coder>

Set the coder,

=over 4


=item B<rice>

Golomb rice coder

=item B<range_def>

Range coder with default table

=item B<range_tab>

Range coder with custom table

=back



=item B<slicecrc>

-1 (default, automatic), 1 use crc with zero initial and final state, 2 use crc with non zero initial and final state


=item B<qtable>


=over 4


=item B<default>

default, automatic

=item B<8bit>

use 8bit default

=item B<greater8bit>

use E<gt>8bit default

=back



=item B<remap_optimizer>

0 - 5, default 3, how much effort the encoder puts into optimizing the remap table.


=back



=head2 GIF


GIF image/animation encoder.


=head3 Options



=over 4


=item B<gifflags> I<integer>

Sets the flags used for GIF encoding.


=over 4


=item B<offsetting>

Enables picture offsetting.

Default is enabled.


=item B<transdiff>

Enables transparency detection between frames.

Default is enabled.


=back



=item B<gifimage> I<integer>

Enables encoding one full GIF image per frame, rather than an animated GIF.

Default value is B<0>.


=item B<global_palette> I<integer>

Writes a palette to the global GIF header where feasible.

If disabled, every frame will always have a palette written, even if there
is a global palette supplied.

Default value is B<1>.


=back



=head2 Hap


Vidvox Hap video encoder.


=head3 Options



=over 4


=item B<format> I<integer>

Specifies the Hap format to encode.


=over 4


=item B<hap>


=item B<hap_alpha>


=item B<hap_q>


=back


Default value is B<hap>.


=item B<chunks> I<integer>

Specifies the number of chunks to split frames into, between 1 and 64. This
permits multithreaded decoding of large frames, potentially at the cost of
data-rate. The encoder may modify this value to divide frames evenly.

Default value is I<1>.


=item B<compressor> I<integer>

Specifies the second-stage compressor to use. If set to B<none>,
B<chunks> will be limited to 1, as chunked uncompressed frames offer no
benefit.


=over 4


=item B<none>


=item B<snappy>


=back


Default value is B<snappy>.


=back



=head2 jpeg2000


The native jpeg 2000 encoder is lossy by default, the C<-q:v>
option can be used to set the encoding quality. Lossless encoding
can be selected with C<-pred 1>.


=head3 Options



=over 4


=item B<format> I<integer>

Can be set to either C<j2k> or C<jp2> (the default) that
makes it possible to store non-rgb pix_fmts.


=item B<tile_width> I<integer>

Sets tile width. Range is 1 to 1073741824. Default is 256.


=item B<tile_height> I<integer>

Sets tile height. Range is 1 to 1073741824. Default is 256.


=item B<pred> I<integer>

Allows setting the discrete wavelet transform (DWT) type

=over 4


=item B<dwt97int (Lossy)>


=item B<dwt53 (Lossless)>


=back

Default is C<dwt97int>


=item B<sop> I<boolean>

Enable this to add SOP marker at the start of each packet. Disabled by default.


=item B<eph> I<boolean>

Enable this to add EPH marker at the end of each packet header. Disabled by default.


=item B<prog> I<integer>

Sets the progression order to be used by the encoder.
Possible values are:

=over 4


=item B<lrcp>


=item B<rlcp>


=item B<rpcl>


=item B<pcrl>


=item B<cprl>


=back

Set to C<lrcp> by default.


=item B<layer_rates> I<string>

By default, when this option is not used, compression is done using the quality metric.
This option allows for compression using compression ratio. The compression ratio for each
level could be specified. The compression ratio of a layer C<l> species the what ratio of
total file size is contained in the first C<l> layers.

Example usage:

	
	ffmpeg -i input.bmp -c:v jpeg2000 -layer_rates "100,10,1" output.j2k


This would compress the image to contain 3 layers, where the data contained in the
first layer would be compressed by 1000 times, compressed by 100 in the first two layers,
and shall contain all data while using all 3 layers.


=back



=head2 librav1e


rav1e AV1 encoder wrapper.

Requires the presence of the rav1e headers and library during configuration.
You need to explicitly configure the build with C<--enable-librav1e>.


=head3 Options



=over 4


=item B<qmax>

Sets the maximum quantizer to use when using bitrate mode.


=item B<qmin>

Sets the minimum quantizer to use when using bitrate mode.


=item B<qp>

Uses quantizer mode to encode at the given quantizer (0-255).


=item B<speed>

Selects the speed preset (0-10) to encode with.


=item B<tiles>

Selects how many tiles to encode with.


=item B<tile-rows>

Selects how many rows of tiles to encode with.


=item B<tile-columns>

Selects how many columns of tiles to encode with.


=item B<rav1e-params>

Set rav1e options using a list of I<key>=I<value> pairs separated
by ":". See B<rav1e --help> for a list of options.

For example to specify librav1e encoding options with B<-rav1e-params>:

	
	ffmpeg -i input -c:v librav1e -b:v 500K -rav1e-params speed=5:low_latency=true output.mp4



=back



=head2 libaom-av1


libaom AV1 encoder wrapper.

Requires the presence of the libaom headers and library during
configuration.  You need to explicitly configure the build with
C<--enable-libaom>.


=head3 Options


The wrapper supports the following standard libavcodec options:


=over 4



=item B<b>

Set bitrate target in bits/second.  By default this will use
variable-bitrate mode.  If B<maxrate> and B<minrate> are
also set to the same value then it will use constant-bitrate mode,
otherwise if B<crf> is set as well then it will use
constrained-quality mode.


=item B<g keyint_min>

Set key frame placement.  The GOP size sets the maximum distance between
key frames; if zero the output stream will be intra-only.  The minimum
distance is ignored unless it is the same as the GOP size, in which case
key frames will always appear at a fixed interval.  Not set by default,
so without this option the library has completely free choice about
where to place key frames.


=item B<qmin qmax>

Set minimum/maximum quantisation values.  Valid range is from 0 to 63
(warning: this does not match the quantiser values actually used by AV1
- divide by four to map real quantiser values to this range).  Defaults
to min/max (no constraint).


=item B<minrate maxrate bufsize rc_init_occupancy>

Set rate control buffering parameters.  Not used if not set - defaults
to unconstrained variable bitrate.


=item B<threads>

Set the number of threads to use while encoding.  This may require the
B<tiles> or B<row-mt> options to also be set to actually
use the specified number of threads fully. Defaults to the number of
hardware threads supported by the host machine.


=item B<profile>

Set the encoding profile.  Defaults to using the profile which matches
the bit depth and chroma subsampling of the input.


=back


The wrapper also has some specific options:


=over 4



=item B<cpu-used>

Set the quality/encoding speed tradeoff.  Valid range is from 0 to 8,
higher numbers indicating greater speed and lower quality.  The default
value is 1, which will be slow and high quality.


=item B<auto-alt-ref>

Enable use of alternate reference frames.  Defaults to the internal
default of the library.


=item B<arnr-max-frames (>I<frames>B<)>

Set altref noise reduction max frame count. Default is -1.


=item B<arnr-strength (>I<strength>B<)>

Set altref noise reduction filter strength. Range is -1 to 6. Default is -1.


=item B<aq-mode (>I<aq-mode>B<)>

Set adaptive quantization mode. Possible values:


=over 4


=item B<none (>I<0>B<)>

Disabled.


=item B<variance (>I<1>B<)>

Variance-based.


=item B<complexity (>I<2>B<)>

Complexity-based.


=item B<cyclic (>I<3>B<)>

Cyclic refresh.

=back



=item B<tune (>I<tune>B<)>

Set the distortion metric the encoder is tuned with. Default is C<psnr>.


=over 4


=item B<psnr (>I<0>B<)>



=item B<ssim (>I<1>B<)>


=back



=item B<lag-in-frames>

Set the maximum number of frames which the encoder may keep in flight
at any one time for lookahead purposes.  Defaults to the internal
default of the library.


=item B<error-resilience>

Enable error resilience features:

=over 4


=item B<default>

Improve resilience against losses of whole frames.

=back

Not enabled by default.


=item B<crf>

Set the quality/size tradeoff for constant-quality (no bitrate target)
and constrained-quality (with maximum bitrate target) modes. Valid
range is 0 to 63, higher numbers indicating lower quality and smaller
output size.  Only used if set; by default only the bitrate target is
used.


=item B<static-thresh>

Set a change threshold on blocks below which they will be skipped by
the encoder.  Defined in arbitrary units as a nonnegative integer,
defaulting to zero (no blocks are skipped).


=item B<drop-threshold>

Set a threshold for dropping frames when close to rate control bounds.
Defined as a percentage of the target buffer - when the rate control
buffer falls below this percentage, frames will be dropped until it
has refilled above the threshold.  Defaults to zero (no frames are
dropped).


=item B<denoise-noise-level (>I<level>B<)>

Amount of noise to be removed for grain synthesis. Grain synthesis is disabled if
this option is not set or set to 0.


=item B<denoise-block-size (>I<pixels>B<)>

Block size used for denoising for grain synthesis. If not set, AV1 codec
uses the default value of 32.


=item B<undershoot-pct (>I<pct>B<)>

Set datarate undershoot (min) percentage of the target bitrate. Range is -1 to 100.
Default is -1.


=item B<overshoot-pct (>I<pct>B<)>

Set datarate overshoot (max) percentage of the target bitrate. Range is -1 to 1000.
Default is -1.


=item B<minsection-pct (>I<pct>B<)>

Minimum percentage variation of the GOP bitrate from the target bitrate. If minsection-pct
is not set, the libaomenc wrapper computes it as follows: C<(minrate * 100 / bitrate)>.
Range is -1 to 100. Default is -1 (unset).


=item B<maxsection-pct (>I<pct>B<)>

Maximum percentage variation of the GOP bitrate from the target bitrate. If maxsection-pct
is not set, the libaomenc wrapper computes it as follows: C<(maxrate * 100 / bitrate)>.
Range is -1 to 5000. Default is -1 (unset).


=item B<frame-parallel (>I<boolean>B<)>

Enable frame parallel decodability features. Default is true.


=item B<tiles>

Set the number of tiles to encode the input video with, as columns x
rows.  Larger numbers allow greater parallelism in both encoding and
decoding, but may decrease coding efficiency.  Defaults to the minimum
number of tiles required by the size of the input video (this is 1x1
(that is, a single tile) for sizes up to and including 4K).


=item B<tile-columns tile-rows>

Set the number of tiles as log2 of the number of tile rows and columns.
Provided for compatibility with libvpx/VP9.


=item B<row-mt (Requires libaom E<gt>= 1.0.0-759-g90a15f4f2)>

Enable row based multi-threading. Disabled by default.


=item B<enable-cdef (>I<boolean>B<)>

Enable Constrained Directional Enhancement Filter. The libaom-av1
encoder enables CDEF by default.


=item B<enable-restoration (>I<boolean>B<)>

Enable Loop Restoration Filter. Default is true for libaom-av1.


=item B<enable-global-motion (>I<boolean>B<)>

Enable the use of global motion for block prediction. Default is true.


=item B<enable-intrabc (>I<boolean>B<)>

Enable block copy mode for intra block prediction. This mode is
useful for screen content. Default is true.


=item B<enable-rect-partitions (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable rectangular partitions. Default is true.


=item B<enable-1to4-partitions (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable 1:4/4:1 partitions. Default is true.


=item B<enable-ab-partitions (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable AB shape partitions. Default is true.


=item B<enable-angle-delta (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable angle delta intra prediction. Default is true.


=item B<enable-cfl-intra (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable chroma predicted from luma intra prediction. Default is true.


=item B<enable-filter-intra (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable filter intra predictor. Default is true.


=item B<enable-intra-edge-filter (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable intra edge filter. Default is true.


=item B<enable-smooth-intra (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable smooth intra prediction mode. Default is true.


=item B<enable-paeth-intra (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable paeth predictor in intra prediction. Default is true.


=item B<enable-palette (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable palette prediction mode. Default is true.


=item B<enable-flip-idtx (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable extended transform type, including FLIPADST_DCT, DCT_FLIPADST,
FLIPADST_FLIPADST, ADST_FLIPADST, FLIPADST_ADST, IDTX, V_DCT, H_DCT,
V_ADST, H_ADST, V_FLIPADST, H_FLIPADST. Default is true.


=item B<enable-tx64 (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable 64-pt transform. Default is true.


=item B<reduced-tx-type-set (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Use reduced set of transform types. Default is false.


=item B<use-intra-dct-only (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Use DCT only for INTRA modes. Default is false.


=item B<use-inter-dct-only (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Use DCT only for INTER modes. Default is false.


=item B<use-intra-default-tx-only (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Use Default-transform only for INTRA modes. Default is false.


=item B<enable-ref-frame-mvs (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable temporal mv prediction. Default is true.


=item B<enable-reduced-reference-set (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Use reduced set of single and compound references. Default is false.


=item B<enable-obmc (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable obmc. Default is true.


=item B<enable-dual-filter (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable dual filter. Default is true.


=item B<enable-diff-wtd-comp (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable difference-weighted compound. Default is true.


=item B<enable-dist-wtd-comp (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable distance-weighted compound. Default is true.


=item B<enable-onesided-comp (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable one sided compound. Default is true.


=item B<enable-interinter-wedge (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable interinter wedge compound. Default is true.


=item B<enable-interintra-wedge (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable interintra wedge compound. Default is true.


=item B<enable-masked-comp (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable masked compound. Default is true.


=item B<enable-interintra-comp (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable interintra compound. Default is true.


=item B<enable-smooth-interintra (>I<boolean>B<) (Requires libaom E<gt>= v2.0.0)>

Enable smooth interintra mode. Default is true.


=item B<aom-params>

Set libaom options using a list of I<key>=I<value> pairs separated
by ":". For a list of supported options, see B<aomenc --help> under the
section "AV1 Specific Options".

For example to specify libaom encoding options with B<-aom-params>:

	
	ffmpeg -i input -c:v libaom-av1 -b:v 500K -aom-params tune=psnr:enable-tpl-model=1 output.mp4



=back



=head2 liboapv


Advanced Professional Video codec encoder wrapper.

This encoder requires the presence of the liboapv headers and library
during configuration. You need to explicitly configure the build with
B<--enable-liboapv>.


=over 4

Many liboapv encoder options are mapped to FFmpeg global codec options,
while unique encoder options are provided through private options.

=back


The apv project website is at E<lt>B<https://github.com/AcademySoftwareFoundation/openapv>E<gt>.


=head3 Options


The following options are supported by the liboapv wrapper.


=over 4

To get a more extensive documentation of the liboapv options, consult the
liboapv documentation.

=back



=over 4


=item B<preset>

Set the quality-speed tradeoff [fastest, fast, medium, slow, placebo, default]


=item B<qp>

Set the quantization parameter value for CQP rate control mode.


=item B<oapv-params (>I<parse_apv_params>B<)>

Set liboapvenc options using a list of I<key>=I<value> pairs separated
by ":". See the liboapv encoder user guide for a list of accepted parameters.

=back



=head2 libsvtav1


SVT-AV1 encoder wrapper.

Requires the presence of the SVT-AV1 headers and library during configuration.
You need to explicitly configure the build with C<--enable-libsvtav1>.


=head3 Options



=over 4


=item B<profile>

Set the encoding profile.

=over 4


=item B<main>


=item B<high>


=item B<professional>


=back



=item B<level>

Set the operating point level. For example: '4.0'


=item B<hielevel>

Set the Hierarchical prediction levels.

=over 4


=item B<3level>


=item B<4level>

This is the default.

=back



=item B<tier>

Set the operating point tier.

=over 4


=item B<main>

This is the default.

=item B<high>


=back



=item B<qmax>

Set the maximum quantizer to use when using a bitrate mode.


=item B<qmin>

Set the minimum quantizer to use when using a bitrate mode.


=item B<crf>

Constant rate factor value used in crf rate control mode (0-63).


=item B<qp>

Set the quantizer used in cqp rate control mode (0-63).


=item B<sc_detection>

Enable scene change detection.


=item B<la_depth>

Set number of frames to look ahead (0-120).


=item B<preset>

Set the quality-speed tradeoff, in the range 0 to 13.  Higher values are
faster but lower quality.


=item B<tile_rows>

Set log2 of the number of rows of tiles to use (0-6).


=item B<tile_columns>

Set log2 of the number of columns of tiles to use (0-4).


=item B<svtav1-params>

Set SVT-AV1 options using a list of I<key>=I<value> pairs separated
by ":". See the SVT-AV1 encoder user guide for a list of accepted parameters.


=back



=head2 libjxl


libjxl JPEG XL encoder wrapper.

Requires the presence of the libjxl headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libjxl>.


=head3 Options


The libjxl wrapper supports the following options:


=over 4



=item B<distance>

Set the target Butteraugli distance. This is a quality setting: lower
distance yields higher quality, with distance=1.0 roughly comparable to
libjpeg Quality 90 for photographic content. Setting distance=0.0 yields
true lossless encoding. Valid values range between 0.0 and 15.0, and sane
values rarely exceed 5.0. Setting distance=0.1 usually attains
transparency for most input. The default is 1.0.


=item B<effort>

Set the encoding effort used. Higher effort values produce more consistent
quality and usually produces a better quality/bpp curve, at the cost of
more CPU time required. Valid values range from 1 to 9, and the default is 7.


=item B<modular>

Force the encoder to use Modular mode instead of choosing automatically. The
default is to use VarDCT for lossy encoding and Modular for lossless. VarDCT
is generally superior to Modular for lossy encoding but does not support
lossless encoding.


=back



=head2 libkvazaar


Kvazaar H.265/HEVC encoder.

Requires the presence of the libkvazaar headers and library during
configuration. You need to explicitly configure the build with
B<--enable-libkvazaar>.


=head3 Options



=over 4



=item B<b>

Set target video bitrate in bit/s and enable rate control.


=item B<kvazaar-params>

Set kvazaar parameters as a list of I<name>=I<value> pairs separated
by commas (,). See kvazaar documentation for a list of options.


=back



=head2 libopenh264


Cisco libopenh264 H.264/MPEG-4 AVC encoder wrapper.

This encoder requires the presence of the libopenh264 headers and
library during configuration. You need to explicitly configure the
build with C<--enable-libopenh264>. The library is detected using
B<pkg-config>.

For more information about the library see
E<lt>B<http://www.openh264.org>E<gt>.


=head3 Options


The following FFmpeg global options affect the configurations of the
libopenh264 encoder.


=over 4


=item B<b>

Set the bitrate (as a number of bits per second).


=item B<g>

Set the GOP size.


=item B<maxrate>

Set the max bitrate (as a number of bits per second).


=item B<flags +global_header>

Set global header in the bitstream.


=item B<slices>

Set the number of slices, used in parallelized encoding. Default value
is 0. This is only used when B<slice_mode> is set to
B<fixed>.


=item B<loopfilter>

Enable loop filter, if set to 1 (automatically enabled). To disable
set a value of 0.


=item B<profile>

Set profile restrictions. If set to the value of B<main> enable
CABAC (set the C<SEncParamExt.iEntropyCodingModeFlag> flag to 1).


=item B<max_nal_size>

Set maximum NAL size in bytes.


=item B<allow_skip_frames>

Allow skipping frames to hit the target bitrate if set to 1.

=back



=head2 libtheora


libtheora Theora encoder wrapper.

Requires the presence of the libtheora headers and library during
configuration. You need to explicitly configure the build with
C<--enable-libtheora>.

For more information about the libtheora project see
E<lt>B<http://www.theora.org/>E<gt>.


=head3 Options


The following global options are mapped to internal libtheora options
which affect the quality and the bitrate of the encoded stream.


=over 4


=item B<b>

Set the video bitrate in bit/s for CBR (Constant Bit Rate) mode.  In
case VBR (Variable Bit Rate) mode is enabled this option is ignored.


=item B<flags>

Used to enable constant quality mode (VBR) encoding through the
B<qscale> flag, and to enable the C<pass1> and C<pass2>
modes.


=item B<g>

Set the GOP size.


=item B<global_quality>

Set the global quality as an integer in lambda units.

Only relevant when VBR mode is enabled with C<flags +qscale>. The
value is converted to QP units by dividing it by C<FF_QP2LAMBDA>,
clipped in the [0 - 10] range, and then multiplied by 6.3 to get a
value in the native libtheora range [0-63]. A higher value corresponds
to a higher quality.


=item B<q>

Enable VBR mode when set to a non-negative value, and set constant
quality value as a double floating point value in QP units.

The value is clipped in the [0-10] range, and then multiplied by 6.3
to get a value in the native libtheora range [0-63].

This option is valid only using the B<ffmpeg> command-line
tool. For library interface users, use B<global_quality>.

=back



=head3 Examples



=over 4


=item *

Set maximum constant quality (VBR) encoding with B<ffmpeg>:
	
	ffmpeg -i INPUT -codec:v libtheora -q:v 10 OUTPUT.ogg



=item *

Use B<ffmpeg> to convert a CBR 1000 kbps Theora video stream:
	
	ffmpeg -i INPUT -codec:v libtheora -b:v 1000k OUTPUT.ogg


=back



=head2 libvpx


VP8/VP9 format supported through libvpx.

Requires the presence of the libvpx headers and library during configuration.
You need to explicitly configure the build with C<--enable-libvpx>.


=head3 Options


The following options are supported by the libvpx wrapper. The
B<vpxenc>-equivalent options or values are listed in parentheses
for easy migration.

To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
B<the Codec Options chapter>.

To get more documentation of the libvpx options, invoke the command
B<ffmpeg -h encoder=libvpx>, B<ffmpeg -h encoder=libvpx-vp9> or
B<vpxenc --help>. Further information is available in the libvpx API
documentation.


=over 4



=item B<b (>I<target-bitrate>B<)>

Set bitrate in bits/s. Note that FFmpeg's B<b> option is
expressed in bits/s, while B<vpxenc>'s B<target-bitrate> is in
kilobits/s.


=item B<g (>I<kf-max-dist>B<)>



=item B<keyint_min (>I<kf-min-dist>B<)>



=item B<qmin (>I<min-q>B<)>

Minimum (Best Quality) Quantizer.


=item B<qmax (>I<max-q>B<)>

Maximum (Worst Quality) Quantizer.
Can be changed per-frame.


=item B<bufsize (>I<buf-sz>B<,> I<buf-optimal-sz>B<)>

Set ratecontrol buffer size (in bits). Note B<vpxenc>'s options are
specified in milliseconds, the libvpx wrapper converts this value as follows:
C<buf-sz = bufsize * 1000 / bitrate>,
C<buf-optimal-sz = bufsize * 1000 / bitrate * 5 / 6>.


=item B<rc_init_occupancy (>I<buf-initial-sz>B<)>

Set number of bits which should be loaded into the rc buffer before decoding
starts. Note B<vpxenc>'s option is specified in milliseconds, the libvpx
wrapper converts this value as follows:
C<rc_init_occupancy * 1000 / bitrate>.


=item B<undershoot-pct>

Set datarate undershoot (min) percentage of the target bitrate.


=item B<overshoot-pct>

Set datarate overshoot (max) percentage of the target bitrate.


=item B<skip_threshold (>I<drop-frame>B<)>



=item B<qcomp (>I<bias-pct>B<)>



=item B<maxrate (>I<maxsection-pct>B<)>

Set GOP max bitrate in bits/s. Note B<vpxenc>'s option is specified as a
percentage of the target bitrate, the libvpx wrapper converts this value as
follows: C<(maxrate * 100 / bitrate)>.


=item B<minrate (>I<minsection-pct>B<)>

Set GOP min bitrate in bits/s. Note B<vpxenc>'s option is specified as a
percentage of the target bitrate, the libvpx wrapper converts this value as
follows: C<(minrate * 100 / bitrate)>.


=item B<minrate, maxrate, b> I<end-usage=cbr>

C<(minrate == maxrate == bitrate)>.


=item B<crf (>I<end-usage=cq>B<,> I<cq-level>B<)>



=item B<tune (>I<tune>B<)>


=over 4


=item B<psnr (>I<psnr>B<)>


=item B<ssim (>I<ssim>B<)>


=back



=item B<quality, deadline (>I<deadline>B<)>


=over 4


=item B<best>

Use best quality deadline. Poorly named and quite slow, this option should be
avoided as it may give worse quality output than good.

=item B<good>

Use good quality deadline. This is a good trade-off between speed and quality
when used with the B<cpu-used> option.

=item B<realtime>

Use realtime quality deadline.

=back



=item B<speed, cpu-used (>I<cpu-used>B<)>

Set quality/speed ratio modifier. Higher values speed up the encode at the cost
of quality.


=item B<nr (>I<noise-sensitivity>B<)>



=item B<static-thresh>

Set a change threshold on blocks below which they will be skipped by the
encoder.


=item B<slices (>I<token-parts>B<)>

Note that FFmpeg's B<slices> option gives the total number of partitions,
while B<vpxenc>'s B<token-parts> is given as
C<log2(partitions)>.


=item B<max-intra-rate>

Set maximum I-frame bitrate as a percentage of the target bitrate. A value of 0
means unlimited.


=item B<force_key_frames>

C<VPX_EFLAG_FORCE_KF>


=item B<Alternate reference frame related>


=over 4


=item B<auto-alt-ref>

Enable use of alternate reference frames (2-pass only).
Values greater than 1 enable multi-layer alternate reference frames (VP9 only).

=item B<arnr-maxframes>

Set altref noise reduction max frame count.

=item B<arnr-type>

Set altref noise reduction filter type: backward, forward, centered.

=item B<arnr-strength>

Set altref noise reduction filter strength.

=item B<rc-lookahead, lag-in-frames (>I<lag-in-frames>B<)>

Set number of frames to look ahead for frametype and ratecontrol.

=item B<min-gf-interval>

Set minimum golden/alternate reference frame interval (VP9 only).

=back



=item B<error-resilient>

Enable error resiliency features.


=item B<sharpness> I<integer>

Increase sharpness at the expense of lower PSNR.
The valid range is [0, 7].


=item B<ts-parameters>

Sets the temporal scalability configuration using a :-separated list of
key=value pairs. For example, to specify temporal scalability parameters
with C<ffmpeg>:
	
	ffmpeg -i INPUT -c:v libvpx -ts-parameters ts_number_layers=3:\
	ts_target_bitrate=250,500,1000:ts_rate_decimator=4,2,1:\
	ts_periodicity=4:ts_layer_id=0,2,1,2:ts_layering_mode=3 OUTPUT

Below is a brief explanation of each of the parameters, please
refer to C<struct vpx_codec_enc_cfg> in C<vpx/vpx_encoder.h> for more
details.

=over 4


=item B<ts_number_layers>

Number of temporal coding layers.

=item B<ts_target_bitrate>

Target bitrate for each temporal layer (in kbps).
(bitrate should be inclusive of the lower temporal layer).

=item B<ts_rate_decimator>

Frame rate decimation factor for each temporal layer.

=item B<ts_periodicity>

Length of the sequence defining frame temporal layer membership.

=item B<ts_layer_id>

Template defining the membership of frames to temporal layers.

=item B<ts_layering_mode>

(optional) Selecting the temporal structure from a set of pre-defined temporal layering modes.
Currently supports the following options.

=over 4


=item B<0>

No temporal layering flags are provided internally,
relies on flags being passed in using C<metadata> field in C<AVFrame>
with following keys.

=over 4


=item B<vp8-flags>

Sets the flags passed into the encoder to indicate the referencing scheme for
the current frame.
Refer to function C<vpx_codec_encode> in C<vpx/vpx_encoder.h> for more
details.

=item B<temporal_id>

Explicitly sets the temporal id of the current frame to encode.

=back


=item B<2>

Two temporal layers. 0-1...

=item B<3>

Three temporal layers. 0-2-1-2...; with single reference frame.

=item B<4>

Same as option "3", except there is a dependency between
the two temporal layer 2 frames within the temporal period.

=back


=back



=item B<VP8-specific options>


=over 4


=item B<screen-content-mode>

Screen content mode, one of: 0 (off), 1 (screen), 2 (screen with more aggressive rate control).

=back



=item B<VP9-specific options>


=over 4


=item B<lossless>

Enable lossless mode.

=item B<tile-columns>

Set number of tile columns to use. Note this is given as
C<log2(tile_columns)>. For example, 8 tile columns would be requested by
setting the B<tile-columns> option to 3.

=item B<tile-rows>

Set number of tile rows to use. Note this is given as C<log2(tile_rows)>.
For example, 4 tile rows would be requested by setting the B<tile-rows>
option to 2.

=item B<frame-parallel>

Enable frame parallel decodability features.

=item B<aq-mode>

Set adaptive quantization mode (0: off (default), 1: variance 2: complexity, 3:
cyclic refresh, 4: equator360).

=item B<colorspace> I<color-space>

Set input color space. The VP9 bitstream supports signaling the following
colorspaces:

=over 4


=item B<B<rgb> I<sRGB>>


=item B<B<bt709> I<bt709>>


=item B<B<unspecified> I<unknown>>


=item B<B<bt470bg> I<bt601>>


=item B<B<smpte170m> I<smpte170>>


=item B<B<smpte240m> I<smpte240>>


=item B<B<bt2020_ncl> I<bt2020>>


=back


=item B<row-mt> I<boolean>

Enable row based multi-threading.

=item B<tune-content>

Set content type: default (0), screen (1), film (2).

=item B<corpus-complexity>

Corpus VBR mode is a variant of standard VBR where the complexity distribution
midpoint is passed in rather than calculated for a specific clip or chunk.

The valid range is [0, 10000]. 0 (default) uses standard VBR.

=item B<enable-tpl> I<boolean>

Enable temporal dependency model.

=item B<ref-frame-config>

Using per-frame metadata, set members of the structure C<vpx_svc_ref_frame_config_t> in C<vpx/vp8cx.h> to fine-control referencing schemes and frame buffer management.
Use a :-separated list of key=value pairs.
For example,
	
	av_dict_set(&av_frame->metadata, "ref-frame-config", \
	"rfc_update_buffer_slot=7:rfc_lst_fb_idx=0:rfc_gld_fb_idx=1:rfc_alt_fb_idx=2:rfc_reference_last=0:rfc_reference_golden=0:rfc_reference_alt_ref=0");


=over 4


=item B<rfc_update_buffer_slot>

Indicates the buffer slot number to update

=item B<rfc_update_last>

Indicates whether to update the LAST frame

=item B<rfc_update_golden>

Indicates whether to update GOLDEN frame

=item B<rfc_update_alt_ref>

Indicates whether to update ALT_REF frame

=item B<rfc_lst_fb_idx>

LAST frame buffer index

=item B<rfc_gld_fb_idx>

GOLDEN frame buffer index

=item B<rfc_alt_fb_idx>

ALT_REF frame buffer index

=item B<rfc_reference_last>

Indicates whether to reference LAST frame

=item B<rfc_reference_golden>

Indicates whether to reference GOLDEN frame

=item B<rfc_reference_alt_ref>

Indicates whether to reference ALT_REF frame

=item B<rfc_reference_duration>

Indicates frame duration

=back


=back



=back


For more information about libvpx see:
E<lt>B<http://www.webmproject.org/>E<gt>


=head2 libvvenc


VVenC H.266/VVC encoder wrapper.

This encoder requires the presence of the libvvenc headers and library
during configuration. You need to explicitly configure the build with
B<--enable-libvvenc>.

The VVenC project website is at
E<lt>B<https://github.com/fraunhoferhhi/vvenc>E<gt>.


=head3 Supported Pixel Formats


VVenC supports only 10-bit color spaces as input. But the internal (encoded)
bit depth can be set to 8-bit or 10-bit at runtime.


=head3 Options



=over 4


=item B<b>

Sets target video bitrate.


=item B<g>

Set the GOP size. Currently support for g=1 (Intra only) or default.


=item B<preset>

Set the VVenC preset.


=item B<levelidc>

Set level idc.


=item B<tier>

Set vvc tier.


=item B<qp>

Set constant quantization parameter.


=item B<subopt> I<boolean>

Set subjective (perceptually motivated) optimization. Default is 1 (on).


=item B<bitdepth8> I<boolean>

Set 8bit coding mode instead of using 10bit. Default is 0 (off).


=item B<period>

set (intra) refresh period in seconds.


=item B<vvenc-params>

Set vvenc options using a list of I<key>=I<value> couples separated
by ":". See B<vvencapp --fullhelp> or B<vvencFFapp --fullhelp> for a list of options.

For example, the options might be provided as:

	
	intraperiod=64:decodingrefreshtype=idr:poc0idr=1:internalbitdepth=8


For example the encoding options might be provided with B<-vvenc-params>:

	
	ffmpeg -i input -c:v libvvenc -b 1M -vvenc-params intraperiod=64:decodingrefreshtype=idr:poc0idr=1:internalbitdepth=8 output.mp4



=back



=head2 libwebp


libwebp WebP Image encoder wrapper

libwebp is Google's official encoder for WebP images. It can encode in either
lossy or lossless mode. Lossy images are essentially a wrapper around a VP8
frame. Lossless images are a separate codec developed by Google.


=head3 Pixel Format


Currently, libwebp only supports YUV420 for lossy and RGB for lossless due
to limitations of the format and libwebp. Alpha is supported for either mode.
Because of API limitations, if RGB is passed in when encoding lossy or YUV is
passed in for encoding lossless, the pixel format will automatically be
converted using functions from libwebp. This is not ideal and is done only for
convenience.


=head3 Options



=over 4



=item B<-lossless> I<boolean>

Enables/Disables use of lossless mode. Default is 0.


=item B<-compression_level> I<integer>

For lossy, this is a quality/speed tradeoff. Higher values give better quality
for a given size at the cost of increased encoding time. For lossless, this is
a size/speed tradeoff. Higher values give smaller size at the cost of increased
encoding time. More specifically, it controls the number of extra algorithms
and compression tools used, and varies the combination of these tools. This
maps to the I<method> option in libwebp. The valid range is 0 to 6.
Default is 4.


=item B<-quality> I<float>

For lossy encoding, this controls image quality. For lossless encoding, this
controls the effort and time spent in compression.
Range is 0 to 100. Default is 75.


=item B<-preset> I<type>

Configuration preset. This does some automatic settings based on the general
type of the image.

=over 4


=item B<none>

Do not use a preset.

=item B<default>

Use the encoder default.

=item B<picture>

Digital picture, like portrait, inner shot

=item B<photo>

Outdoor photograph, with natural lighting

=item B<drawing>

Hand or line drawing, with high-contrast details

=item B<icon>

Small-sized colorful images

=item B<text>

Text-like

=back



=back



=head2 libx264, libx264rgb


x264 H.264/MPEG-4 AVC encoder wrapper.

This encoder requires the presence of the libx264 headers and library
during configuration. You need to explicitly configure the build with
C<--enable-libx264>.

libx264 supports an impressive number of features, including 8x8 and
4x4 adaptive spatial transform, adaptive B-frame placement, CAVLC/CABAC
entropy coding, interlacing (MBAFF), lossless mode, psy optimizations
for detail retention (adaptive quantization, psy-RD, psy-trellis).

Many libx264 encoder options are mapped to FFmpeg global codec
options, while unique encoder options are provided through private
options. Additionally the B<x264opts> and B<x264-params>
private options allows one to pass a list of key=value tuples as accepted
by the libx264 C<x264_param_parse> function.

The x264 project website is at
E<lt>B<http://www.videolan.org/developers/x264.html>E<gt>.

The libx264rgb encoder is the same as libx264, except it accepts packed RGB
pixel formats as input instead of YUV.


=head3 Supported Pixel Formats


x264 supports 8- to 10-bit color spaces. The exact bit depth is controlled at
x264's configure time.


=head3 Options


The following options are supported by the libx264 wrapper. The
B<x264>-equivalent options or values are listed in parentheses
for easy migration.

To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
B<the Codec Options chapter>.

To get a more accurate and extensive documentation of the libx264
options, invoke the command B<x264 --fullhelp> or consult
the libx264 documentation.

In the list below, note that the B<x264> option name is shown
in parentheses after the libavcodec corresponding name, in case there
is a direct mapping.


=over 4


=item B<b (>I<bitrate>B<)>

Set bitrate in bits/s. Note that FFmpeg's B<b> option is
expressed in bits/s, while B<x264>'s B<bitrate> is in
kilobits/s.


=item B<bf (>I<bframes>B<)>

Number of B-frames between I and P-frames


=item B<g (>I<keyint>B<)>

Maximum GOP size


=item B<qmin (>I<qpmin>B<)>

Minimum quantizer scale


=item B<qmax (>I<qpmax>B<)>

Maximum quantizer scale


=item B<qdiff (>I<qpstep>B<)>

Maximum difference between quantizer scales


=item B<qblur (>I<qblur>B<)>

Quantizer curve blur


=item B<qcomp (>I<qcomp>B<)>

Quantizer curve compression factor


=item B<refs (>I<ref>B<)>

Number of reference frames each P-frame can use. The range is I<0-16>.


=item B<level (>I<level>B<)>

Set the C<x264_param_t.i_level_idc> value in case the value is
positive, it is ignored otherwise.

This value can be set using the C<AVCodecContext> API (e.g. by
setting the C<AVCodecContext> value directly), and is specified as
an integer mapped on a corresponding level (e.g. the value 31 maps
to H.264 level IDC "3.1", as defined in the C<x264_levels>
table). It is ignored when set to a non positive value.

Alternatively it can be set as a private option, overriding the value
set in C<AVCodecContext>, and in this case must be specified as
the level IDC identifier (e.g. "3.1"), as defined by H.264 Annex A.


=item B<sc_threshold (>I<scenecut>B<)>

Sets the threshold for the scene change detection.


=item B<trellis (>I<trellis>B<)>

Performs Trellis quantization to increase efficiency. Enabled by default.


=item B<nr (>I<nr>B<)>

Noise reduction


=item B<me_range (>I<merange>B<)>

Maximum range of the motion search in pixels.


=item B<me_method (>I<me>B<)>

Set motion estimation method. Possible values in the decreasing order
of speed:


=over 4


=item B<dia (>I<dia>B<)>


=item B<epzs (>I<dia>B<)>

Diamond search with radius 1 (fastest). B<epzs> is an alias for
B<dia>.

=item B<hex (>I<hex>B<)>

Hexagonal search with radius 2.

=item B<umh (>I<umh>B<)>

Uneven multi-hexagon search.

=item B<esa (>I<esa>B<)>

Exhaustive search.

=item B<tesa (>I<tesa>B<)>

Hadamard exhaustive search (slowest).

=back



=item B<forced-idr>

Normally, when forcing a I-frame type, the encoder can select any type
of I-frame. This option forces it to choose an IDR-frame.


=item B<subq (>I<subme>B<)>

Sub-pixel motion estimation method.


=item B<b_strategy (>I<b-adapt>B<)>

Adaptive B-frame placement decision algorithm. Use only on first-pass.


=item B<keyint_min (>I<min-keyint>B<)>

Minimum GOP size.


=item B<coder>

Set entropy encoder. Possible values:


=over 4


=item B<ac>

Enable CABAC.


=item B<vlc>

Enable CAVLC and disable CABAC. It generates the same effect as
B<x264>'s B<--no-cabac> option.

=back



=item B<cmp>

Set full pixel motion estimation comparison algorithm. Possible values:


=over 4


=item B<chroma>

Enable chroma in motion estimation.


=item B<sad>

Ignore chroma in motion estimation. It generates the same effect as
B<x264>'s B<--no-chroma-me> option.

=back



=item B<threads (>I<threads>B<)>

Number of encoding threads.


=item B<thread_type>

Set multithreading technique. Possible values:


=over 4


=item B<slice>

Slice-based multithreading. It generates the same effect as
B<x264>'s B<--sliced-threads> option.

=item B<frame>

Frame-based multithreading.

=back



=item B<flags>

Set encoding flags. It can be used to disable closed GOP and enable
open GOP by setting it to C<-cgop>. The result is similar to
the behavior of B<x264>'s B<--open-gop> option.


=item B<rc_init_occupancy (>I<vbv-init>B<)>

Initial VBV buffer occupancy


=item B<preset (>I<preset>B<)>

Set the encoding preset.


=item B<tune (>I<tune>B<)>

Set tuning of the encoding params.


=item B<profile (>I<profile>B<)>

Set profile restrictions.


=item B<fastfirstpass>

Enable fast settings when encoding first pass, when set to 1. When set
to 0, it has the same effect of B<x264>'s
B<--slow-firstpass> option.


=item B<crf (>I<crf>B<)>

Set the quality for constant quality mode.


=item B<crf_max (>I<crf-max>B<)>

In CRF mode, prevents VBV from lowering quality beyond this point.


=item B<qp (>I<qp>B<)>

Set constant quantization rate control method parameter.


=item B<aq-mode (>I<aq-mode>B<)>

Set AQ method. Possible values:


=over 4


=item B<none (>I<0>B<)>

Disabled.


=item B<variance (>I<1>B<)>

Variance AQ (complexity mask).


=item B<autovariance (>I<2>B<)>

Auto-variance AQ (experimental).

=back



=item B<aq-strength (>I<aq-strength>B<)>

Set AQ strength, reduce blocking and blurring in flat and textured areas.


=item B<psy>

Use psychovisual optimizations when set to 1. When set to 0, it has the
same effect as B<x264>'s B<--no-psy> option.


=item B<psy-rd (>I<psy-rd>B<)>

Set strength of psychovisual optimization, in
I<psy-rd>:I<psy-trellis> format.


=item B<rc-lookahead (>I<rc-lookahead>B<)>

Set number of frames to look ahead for frametype and ratecontrol.


=item B<weightb>

Enable weighted prediction for B-frames when set to 1. When set to 0,
it has the same effect as B<x264>'s B<--no-weightb> option.


=item B<weightp (>I<weightp>B<)>

Set weighted prediction method for P-frames. Possible values:


=over 4


=item B<none (>I<0>B<)>

Disabled

=item B<simple (>I<1>B<)>

Enable only weighted refs

=item B<smart (>I<2>B<)>

Enable both weighted refs and duplicates

=back



=item B<ssim (>I<ssim>B<)>

Enable calculation and printing SSIM stats after the encoding.


=item B<intra-refresh (>I<intra-refresh>B<)>

Enable the use of Periodic Intra Refresh instead of IDR frames when set
to 1.


=item B<avcintra-class (>I<class>B<)>

Configure the encoder to generate AVC-Intra.
Valid values are 50, 100 and 200


=item B<bluray-compat (>I<bluray-compat>B<)>

Configure the encoder to be compatible with the bluray standard.
It is a shorthand for setting "bluray-compat=1 force-cfr=1".


=item B<b-bias (>I<b-bias>B<)>

Set the influence on how often B-frames are used.


=item B<b-pyramid (>I<b-pyramid>B<)>

Set method for keeping of some B-frames as references. Possible values:


=over 4


=item B<none (>I<none>B<)>

Disabled.

=item B<strict (>I<strict>B<)>

Strictly hierarchical pyramid.

=item B<normal (>I<normal>B<)>

Non-strict (not Blu-ray compatible).

=back



=item B<mixed-refs>

Enable the use of one reference per partition, as opposed to one
reference per macroblock when set to 1. When set to 0, it has the
same effect as B<x264>'s B<--no-mixed-refs> option.


=item B<8x8dct>

Enable adaptive spatial transform (high profile 8x8 transform)
when set to 1. When set to 0, it has the same effect as
B<x264>'s B<--no-8x8dct> option.


=item B<fast-pskip>

Enable early SKIP detection on P-frames when set to 1. When set
to 0, it has the same effect as B<x264>'s
B<--no-fast-pskip> option.


=item B<aud (>I<aud>B<)>

Enable use of access unit delimiters when set to 1.


=item B<mbtree>

Enable use macroblock tree ratecontrol when set to 1. When set
to 0, it has the same effect as B<x264>'s
B<--no-mbtree> option.


=item B<deblock (>I<deblock>B<)>

Set loop filter parameters, in I<alpha>:I<beta> form.


=item B<cplxblur (>I<cplxblur>B<)>

Set fluctuations reduction in QP (before curve compression).


=item B<partitions (>I<partitions>B<)>

Set partitions to consider as a comma-separated list of values.
Possible values in the list:


=over 4


=item B<p8x8>

8x8 P-frame partition.

=item B<p4x4>

4x4 P-frame partition.

=item B<b8x8>

4x4 B-frame partition.

=item B<i8x8>

8x8 I-frame partition.

=item B<i4x4>

4x4 I-frame partition.
(Enabling B<p4x4> requires B<p8x8> to be enabled. Enabling
B<i8x8> requires adaptive spatial transform (B<8x8dct>
option) to be enabled.)

=item B<none (>I<none>B<)>

Do not consider any partitions.

=item B<all (>I<all>B<)>

Consider every partition.

=back



=item B<direct-pred (>I<direct>B<)>

Set direct MV prediction mode. Possible values:


=over 4


=item B<none (>I<none>B<)>

Disable MV prediction.

=item B<spatial (>I<spatial>B<)>

Enable spatial predicting.

=item B<temporal (>I<temporal>B<)>

Enable temporal predicting.

=item B<auto (>I<auto>B<)>

Automatically decided.

=back



=item B<slice-max-size (>I<slice-max-size>B<)>

Set the limit of the size of each slice in bytes. If not specified
but RTP payload size (B<ps>) is specified, that is used.


=item B<stats (>I<stats>B<)>

Set the file name for multi-pass stats.


=item B<nal-hrd (>I<nal-hrd>B<)>

Set signal HRD information (requires B<vbv-bufsize> to be set).
Possible values:


=over 4


=item B<none (>I<none>B<)>

Disable HRD information signaling.

=item B<vbr (>I<vbr>B<)>

Variable bit rate.

=item B<cbr (>I<cbr>B<)>

Constant bit rate (not allowed in MP4 container).

=back



=item B<x264opts> I<opts>


=item B<x264-params> I<opts>

Override the x264 configuration using a :-separated list of key=value
options.

The argument for both options is a list of I<key>=I<value>
couples separated by ":". With B<x264opts> the value can be
omitted, and the value C<1> is assumed in that case.

For I<filter> and I<psy-rd> options values that use ":" as a
separator themselves, use "," instead. They accept it as well since
long ago but this is kept undocumented for some reason.

For example, the options might be provided as:
	
	level=30:bframes=0:weightp=0:cabac=0:ref=1:vbv-maxrate=768:vbv-bufsize=2000:analyse=all:me=umh:no-fast-pskip=1:subq=6:8x8dct=0:trellis=0


For example to specify libx264 encoding options with B<ffmpeg>:
	
	ffmpeg -i foo.mpg -c:v libx264 -x264opts keyint=123:min-keyint=20 -an out.mkv


To get the complete list of the libx264 options, invoke the command
B<x264 --fullhelp> or consult the libx264 documentation.


=item B<a53cc> I<boolean>

Import closed captions (which must be ATSC compatible format) into output.
Only the mpeg2 and h264 decoders provide these. Default is 1 (on).


=item B<udu_sei> I<boolean>

Import user data unregistered SEI if available into output. Default is 0 (off).


=item B<mb_info> I<boolean>

Set mb_info data through AVFrameSideData, only useful when used from the
API. Default is 0 (off).

=back


Encoding ffpresets for common usages are provided so they can be used with the
general presets system (e.g. passing the B<pre> option).


=head2 libx265


x265 H.265/HEVC encoder wrapper.

This encoder requires the presence of the libx265 headers and library
during configuration. You need to explicitly configure the build with
B<--enable-libx265>.


=head3 Options



=over 4


=item B<b>

Sets target video bitrate.


=item B<bf>



=item B<g>

Set the GOP size.


=item B<keyint_min>

Minimum GOP size.


=item B<refs>

Number of reference frames each P-frame can use. The range is from I<1-16>.


=item B<preset>

Set the x265 preset.


=item B<tune>

Set the x265 tune parameter.


=item B<profile>

Set profile restrictions.


=item B<crf>

Set the quality for constant quality mode.


=item B<qp>

Set constant quantization rate control method parameter.


=item B<qmin>

Minimum quantizer scale.


=item B<qmax>

Maximum quantizer scale.


=item B<qdiff>

Maximum difference between quantizer scales.


=item B<qblur>

Quantizer curve blur


=item B<qcomp>

Quantizer curve compression factor


=item B<i_qfactor>



=item B<b_qfactor>



=item B<forced-idr>

Normally, when forcing a I-frame type, the encoder can select any type
of I-frame. This option forces it to choose an IDR-frame.


=item B<udu_sei> I<boolean>

Import user data unregistered SEI if available into output. Default is 0 (off).


=item B<x265-params>

Set x265 options using a list of I<key>=I<value> couples separated
by ":". See B<x265 --help> for a list of options.

For example to specify libx265 encoding options with B<-x265-params>:

	
	ffmpeg -i input -c:v libx265 -x265-params crf=26:psy-rd=1 output.mp4


=back



=head2 libxavs2


xavs2 AVS2-P2/IEEE1857.4 encoder wrapper.

This encoder requires the presence of the libxavs2 headers and library
during configuration. You need to explicitly configure the build with
B<--enable-libxavs2>.

The following standard libavcodec options are used:

=over 4


=item *

B<b> / B<bit_rate>

=item *

B<g> / B<gop_size>

=item *

B<bf> / B<max_b_frames>

=back


The encoder also has its own specific options:

=head3 Options



=over 4


=item B<lcu_row_threads>

Set the number of parallel threads for rows from 1 to 8 (default 5).


=item B<initial_qp>

Set the xavs2 quantization parameter from 1 to 63 (default 34). This is
used to set the initial qp for the first frame.


=item B<qp>

Set the xavs2 quantization parameter from 1 to 63 (default 34). This is
used to set the qp value under constant-QP mode.


=item B<max_qp>

Set the max qp for rate control from 1 to 63 (default 55).


=item B<min_qp>

Set the min qp for rate control from 1 to 63 (default 20).


=item B<speed_level>

Set the Speed level from 0 to 9 (default 0). Higher is better but slower.


=item B<log_level>

Set the log level from -1 to 3 (default 0). -1: none, 0: error,
1: warning, 2: info, 3: debug.


=item B<xavs2-params>

Set xavs2 options using a list of I<key>=I<value> couples separated
by ":".

For example to specify libxavs2 encoding options with B<-xavs2-params>:

	
	ffmpeg -i input -c:v libxavs2 -xavs2-params RdoqLevel=0 output.avs2


=back



=head2 libxeve


eXtra-fast Essential Video Encoder (XEVE) MPEG-5 EVC encoder wrapper.
The xeve-equivalent options or values are listed in parentheses for easy migration.

This encoder requires the presence of the libxeve headers and library
during configuration. You need to explicitly configure the build with
B<--enable-libxeve>.


=over 4

Many libxeve encoder options are mapped to FFmpeg global codec options,
while unique encoder options are provided through private options.
Additionally the xeve-params private options allows one to pass a list
of key=value tuples as accepted by the libxeve C<parse_xeve_params> function.

=back


The xeve project website is at E<lt>B<https://github.com/mpeg5/xeve>E<gt>.


=head3 Options


The following options are supported by the libxeve wrapper.
The xeve-equivalent options or values are listed in parentheses for easy migration.


=over 4

To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
B<the Codec Options chapter>.

=back



=over 4

To get a more accurate and extensive documentation of the libxeve options,
invoke the command  C<xeve_app --help> or consult the libxeve documentation.

=back



=over 4


=item B<b (>I<bitrate>B<)>

Set target video bitrate in bits/s.
Note that FFmpeg's b option is expressed in bits/s, while xeve's bitrate is in kilobits/s.


=item B<bf (>I<bframes>B<)>

Set the maximum number of B frames (1,3,7,15).


=item B<g (>I<keyint>B<)>

Set the GOP size (I-picture period).


=item B<preset (>I<preset>B<)>

Set the xeve preset.
Set the encoder preset value to determine encoding speed [fast, medium, slow, placebo]


=item B<tune (>I<tune>B<)>

Set the encoder tune parameter [psnr, zerolatency]


=item B<profile (>I<profile>B<)>

Set the encoder profile [0: baseline; 1: main]


=item B<crf (>I<crf>B<)>

Set the quality for constant quality mode.
Constant rate factor E<lt>10..49E<gt> [default: 32]


=item B<qp (>I<qp>B<)>

Set constant quantization rate control method parameter.
Quantization parameter qp E<lt>0..51E<gt> [default: 32]


=item B<threads (>I<threads>B<)>

Force to use a specific number of threads


=back



=head2 libxvid


Xvid MPEG-4 Part 2 encoder wrapper.

This encoder requires the presence of the libxvidcore headers and library
during configuration. You need to explicitly configure the build with
C<--enable-libxvid --enable-gpl>.

The native C<mpeg4> encoder supports the MPEG-4 Part 2 format, so
users can encode to this format without this library.


=head3 Options


The following options are supported by the libxvid wrapper. Some of
the following options are listed but are not documented, and
correspond to shared codec options. See B<the Codec
Options chapter> for their documentation. The other shared options
which are not listed have no effect for the libxvid encoder.


=over 4


=item B<b>



=item B<g>



=item B<qmin>



=item B<qmax>



=item B<mpeg_quant>



=item B<threads>



=item B<bf>



=item B<b_qfactor>



=item B<b_qoffset>



=item B<flags>

Set specific encoding flags. Possible values:


=over 4



=item B<mv4>

Use four motion vector by macroblock.


=item B<aic>

Enable high quality AC prediction.


=item B<gray>

Only encode grayscale.


=item B<qpel>

Enable quarter-pixel motion compensation.


=item B<cgop>

Enable closed GOP.


=item B<global_header>

Place global headers in extradata instead of every keyframe.


=back



=item B<gmc>

Enable the use of global motion compensation (GMC).  Default is 0
(disabled).


=item B<me_quality>

Set motion estimation quality level. Possible values in decreasing order of
speed and increasing order of quality:


=over 4


=item B<0>

Use no motion estimation (default).


=item B<1, 2>

Enable advanced diamond zonal search for 16x16 blocks and half-pixel
refinement for 16x16 blocks.


=item B<3, 4>

Enable all of the things described above, plus advanced diamond zonal
search for 8x8 blocks and half-pixel refinement for 8x8 blocks, also
enable motion estimation on chroma planes for P and B-frames.


=item B<5, 6>

Enable all of the things described above, plus extended 16x16 and 8x8
blocks search.

=back



=item B<mbd>

Set macroblock decision algorithm. Possible values in the increasing
order of quality:


=over 4


=item B<simple>

Use macroblock comparing function algorithm (default).


=item B<bits>

Enable rate distortion-based half pixel and quarter pixel refinement for
16x16 blocks.


=item B<rd>

Enable all of the things described above, plus rate distortion-based
half pixel and quarter pixel refinement for 8x8 blocks, and rate
distortion-based search using square pattern.

=back



=item B<lumi_aq>

Enable lumi masking adaptive quantization when set to 1. Default is 0
(disabled).


=item B<variance_aq>

Enable variance adaptive quantization when set to 1. Default is 0
(disabled).

When combined with B<lumi_aq>, the resulting quality will not
be better than any of the two specified individually. In other
words, the resulting quality will be the worse one of the two
effects.


=item B<trellis>

Set rate-distortion optimal quantization.


=item B<ssim>

Set structural similarity (SSIM) displaying method. Possible values:


=over 4


=item B<off>

Disable displaying of SSIM information.


=item B<avg>

Output average SSIM at the end of encoding to stdout. The format of
showing the average SSIM is:

	
	Average SSIM: %f


For users who are not familiar with C, %f means a float number, or
a decimal (e.g. 0.939232).


=item B<frame>

Output both per-frame SSIM data during encoding and average SSIM at
the end of encoding to stdout. The format of per-frame information
is:

	
	       SSIM: avg: %1.3f min: %1.3f max: %1.3f


For users who are not familiar with C, %1.3f means a float number
rounded to 3 digits after the dot (e.g. 0.932).


=back



=item B<ssim_acc>

Set SSIM accuracy. Valid options are integers within the range of
0-4, while 0 gives the most accurate result and 4 computes the
fastest.


=back



=head2 MediaCodec


MediaCodec encoder wrapper enables hardware-accelerated video encoding on
Android device. It supports H.264, H.265 (HEVC), VP8, VP9, MPEG-4, and AV1
encoding (whether works or not is device dependent).

Android provides two sets of APIs: Java MediaCodec and NDK MediaCodec. The
MediaCodec encoder wrapper supports both. Note that the NDK MediaCodec API
operates without requiring JVM, but may fail to function outside the JVM
environment due to dependencies on system framework services, particularly
after Android 15.


=over 4


=item B<ndk_codec> I<boolean>

Use the NDK-based MediaCodec API instead of the Java API. Enabled by default
if C<av_jni_get_java_vm()> return NULL.


=item B<ndk_async> I<boolean>

Use NDK MediaCodec in async mode. Async mode has less overhead than poll in a
loop in sync mode. The drawback of async mode is AV_CODEC_FLAG_GLOBAL_HEADER
doesn't work (use extract_extradata bsf when necessary). It doesn't work and
will be disabled automatically on devices below Android 8.0.


=item B<codec_name> I<string>

A codec type can have multiple implementations on a single device, this option
specify which backend to use (via MediaCodec createCodecByName API). It's NULL
by default, and encoder is created by createEncoderByType.


=item B<bitrate_mode> I<integer>


Possible values:

=over 4


=item B<cq>

Constant quality mode

=item B<vbr>

Variable bitrate mode

=item B<cbr>

Constant bitrate mode

=item B<cbr_fd>

Constant bitrate mode with frame drops

=back



=item B<pts_as_dts> I<boolean>

Use PTS as DTS. This is a workaround since MediaCodec API doesn't provide
decoding timestamp. It is enabled automatically if B frame is 0.


=item B<operating_rate> I<integer>

The desired operating rate that the codec will need to operate at, zero for
unspecified. This is used for cases like high-speed/slow-motion video capture,
where the video encoder format contains the target playback rate (e.g. 30fps),
but the component must be able to handle the high operating capture rate (e.g.
240fps). This rate will be used by codec for resource planning and setting the
operating points.


=item B<qp_i_min> I<integer>

Minimum quantization parameter for I frame.

=item B<qp_p_min> I<integer>

Minimum quantization parameter for P frame.

=item B<qp_b_min> I<integer>

Minimum quantization parameter for B frame.

=item B<qp_i_max> I<integer>

Maximum quantization parameter for I frame.

=item B<qp_p_max> I<integer>

Maximum quantization parameter for P frame.

=item B<qp_b_max> I<integer>

Maximum quantization parameter for B frame.


=back



=head2 MediaFoundation


This provides wrappers to encoders (both audio and video) in the
MediaFoundation framework. It can access both SW and HW encoders.
Video encoders can take input in either of nv12 or yuv420p form
(some encoders support both, some support only either - in practice,
nv12 is the safer choice, especially among HW encoders).


=head2 Microsoft RLE


Microsoft RLE aka MSRLE encoder.
Only 8-bit palette mode supported.
Compatible with Windows 3.1 and Windows 95.


=head3 Options



=over 4


=item B<g> I<integer>

Keyframe interval.
A keyframe is inserted at least every C<-g> frames, sometimes sooner.

=back



=head2 mpeg2


MPEG-2 video encoder.


=head3 Options



=over 4


=item B<profile>

Select the mpeg2 profile to encode:


=over 4


=item B<422>


=item B<high>


=item B<ss>

Spatially Scalable

=item B<snr>

SNR Scalable

=item B<main>


=item B<simple>


=back



=item B<level>

Select the mpeg2 level to encode:


=over 4


=item B<high>


=item B<high1440>


=item B<main>


=item B<low>


=back



=item B<seq_disp_ext> I<integer>

Specifies if the encoder should write a sequence_display_extension to the
output.

=over 4


=item B<-1>


=item B<auto>

Decide automatically to write it or not (this is the default) by checking if
the data to be written is different from the default or unspecified values.

=item B<0>


=item B<never>

Never write it.

=item B<1>


=item B<always>

Always write it.

=back


=item B<video_format> I<integer>

Specifies the video_format written into the sequence display extension
indicating the source of the video pictures. The default is B<unspecified>,
can be B<component>, B<pal>, B<ntsc>, B<secam> or B<mac>.
For maximum compatibility, use B<component>.

=item B<a53cc> I<boolean>

Import closed captions (which must be ATSC compatible format) into output.
Default is 1 (on).

=back



=head2 png


PNG image encoder.


=head3 Options



=over 4


=item B<compression_level>

Sets the compression level, from 0 to 9(default)

=back



=head3 Private options



=over 4


=item B<dpi> I<integer>

Set physical density of pixels, in dots per inch, unset by default

=item B<dpm> I<integer>

Set physical density of pixels, in dots per meter, unset by default

=item B<pred> I<method>

Set prediction method (none, sub, up, avg, paeth, mixed), default is paeth

=back



=head2 ProRes


Apple ProRes encoder.

FFmpeg contains 2 ProRes encoders, the prores-aw and prores-ks encoder.
The used encoder can be chosen with the C<-vcodec> option.


=head3 Private Options for prores-ks



=over 4


=item B<profile> I<integer>

Select the ProRes profile to encode

=over 4


=item B<proxy>


=item B<lt>


=item B<standard>


=item B<hq>


=item B<4444>


=item B<4444xq>


=back



=item B<quant_mat> I<integer>

Select quantization matrix.

=over 4


=item B<auto>


=item B<default>


=item B<proxy>


=item B<lt>


=item B<standard>


=item B<hq>


=back

If set to I<auto>, the matrix matching the profile will be picked.
If not set, the matrix providing the highest quality, I<default>, will be
picked.


=item B<bits_per_mb> I<integer>

How many bits to allot for coding one macroblock. Different profiles use
between 200 and 2400 bits per macroblock, the maximum is 8000.


=item B<mbs_per_slice> I<integer>

Number of macroblocks in each slice (1-8); the default value (8)
should be good in almost all situations.


=item B<vendor> I<string>

Override the 4-byte vendor ID.
A custom vendor ID like I<apl0> would claim the stream was produced by
the Apple encoder.


=item B<alpha_bits> I<integer>

Specify number of bits for alpha component.
Possible values are I<0>, I<8> and I<16>.
Use I<0> to disable alpha plane coding.


=back



=head3 Speed considerations


In the default mode of operation the encoder has to honor frame constraints
(i.e. not produce frames with size bigger than requested) while still making
output picture as good as possible.
A frame containing a lot of small details is harder to compress and the encoder
would spend more time searching for appropriate quantizers for each slice.

Setting a higher B<bits_per_mb> limit will improve the speed.

For the fastest encoding speed set the B<qscale> parameter (4 is the
recommended value) and do not set a size constraint.


=head2 QSV Encoders


The family of Intel QuickSync Video encoders (MPEG-2, H.264, HEVC, JPEG/MJPEG,
VP9, AV1)


=head3 Ratecontrol Method

The ratecontrol method is selected as follows:

=over 4


=item *

When B<global_quality> is specified, a quality-based mode is used.
Specifically this means either

=over 4


=item -

I<CQP> - constant quantizer scale, when the B<qscale> codec flag is
also set (the B<-qscale> ffmpeg option).


=item -

I<LA_ICQ> - intelligent constant quality with lookahead, when the
B<look_ahead> option is also set.


=item -

I<ICQ> -- intelligent constant quality otherwise. For the ICQ modes, global
quality range is 1 to 51, with 1 being the best quality.

=back



=item *

Otherwise when the desired average bitrate is specified with the B<b>
option, a bitrate-based mode is used.

=over 4


=item -

I<LA> - VBR with lookahead, when the B<look_ahead> option is specified.


=item -

I<VCM> - video conferencing mode, when the B<vcm> option is set.


=item -

I<CBR> - constant bitrate, when B<maxrate> is specified and equal to
the average bitrate.


=item -

I<VBR> - variable bitrate, when B<maxrate> is specified, but is higher
than the average bitrate.


=item -

I<AVBR> - average VBR mode, when B<maxrate> is not specified, both
B<avbr_accuracy> and B<avbr_convergence> are set to non-zero. This
mode is available for H264 and HEVC on Windows.

=back



=item *

Otherwise the default ratecontrol method I<CQP> is used.

=back


Note that depending on your system, a different mode than the one you specified
may be selected by the encoder. Set the verbosity level to I<verbose> or
higher to see the actual settings used by the QSV runtime.


=head3 Global Options -E<gt> MSDK Options

Additional libavcodec global options are mapped to MSDK options as follows:


=over 4


=item *

B<g/gop_size> -E<gt> B<GopPicSize>


=item *

B<bf/max_b_frames>+1 -E<gt> B<GopRefDist>


=item *

B<rc_init_occupancy/rc_initial_buffer_occupancy> -E<gt>
B<InitialDelayInKB>


=item *

B<slices> -E<gt> B<NumSlice>


=item *

B<refs> -E<gt> B<NumRefFrame>


=item *

B<b_strategy/b_frame_strategy> -E<gt> B<BRefType>


=item *

B<cgop/CLOSED_GOP> codec flag -E<gt> B<GopOptFlag>


=item *

For the I<CQP> mode, the B<i_qfactor/i_qoffset> and
B<b_qfactor/b_qoffset> set the difference between I<QPP> and I<QPI>,
and I<QPP> and I<QPB> respectively.


=item *

Setting the B<coder> option to the value I<vlc> will make the H.264
encoder use CAVLC instead of CABAC.


=back



=head3 Common Options

Following options are used by all qsv encoders.


=over 4


=item I<async_depth>

Specifies how many asynchronous operations an application performs
before the application explicitly synchronizes the result. If zero,
the value is not specified.


=item I<preset>

This option itemizes a range of choices from veryfast (best speed) to veryslow
(best quality).

=over 4


=item B<veryfast>


=item B<faster>


=item B<fast>


=item B<medium>


=item B<slow>


=item B<slower>


=item B<veryslow>


=back



=item I<forced_idr>

Forcing I frames as IDR frames.


=item I<low_power>

For encoders set this flag to ON to reduce power consumption and GPU usage.

=back



=head3 Runtime Options

Following options can be used during qsv encoding.


=over 4


=item I<global_quality>


=item I<i_quant_factor>


=item I<i_quant_offset>


=item I<b_quant_factor>


=item I<b_quant_offset>

Supported in h264_qsv and hevc_qsv.
Change these value to reset qsv codec's qp configuration.


=item I<max_frame_size>

Supported in h264_qsv and hevc_qsv.
Change this value to reset qsv codec's MaxFrameSize configuration.


=item I<gop_size>

Change this value to reset qsv codec's gop configuration.


=item I<int_ref_type>


=item I<int_ref_cycle_size>


=item I<int_ref_qp_delta>


=item I<int_ref_cycle_dist>

Supported in h264_qsv and hevc_qsv.
Change these value to reset qsv codec's Intra Refresh configuration.


=item I<qmax>


=item I<qmin>


=item I<max_qp_i>


=item I<min_qp_i>


=item I<max_qp_p>


=item I<min_qp_p>


=item I<max_qp_b>


=item I<min_qp_b>

Supported in h264_qsv.
Change these value to reset qsv codec's max/min qp configuration.


=item I<low_delay_brc>

Supported in h264_qsv, hevc_qsv and av1_qsv.
Change this value to reset qsv codec's low_delay_brc configuration.


=item I<framerate>

Change this value to reset qsv codec's framerate configuration.


=item I<bit_rate>


=item I<rc_buffer_size>


=item I<rc_initial_buffer_occupancy>


=item I<rc_max_rate>

Change these value to reset qsv codec's bitrate control configuration.


=item I<pic_timing_sei>

Supported in h264_qsv and hevc_qsv.
Change this value to reset qsv codec's pic_timing_sei configuration.


=item I<qsv_params>

Set QSV encoder parameters as a colon-separated list of key-value pairs.

The B<qsv_params> should be formatted as C<key1=value1:key2=value2:...>.

These parameters are passed directly to the underlying Intel Quick Sync Video (QSV) encoder using the MFXSetParameter function.

Example:
	
	ffmpeg -i input.mp4 -c:v h264_qsv -qsv_params "CodingOption1=1:CodingOption2=2" output.mp4


This option allows fine-grained control over various encoder-specific settings provided by the QSV encoder.

=back



=head3 H264 options

These options are used by h264_qsv


=over 4


=item I<extbrc>

Extended bitrate control.


=item I<recovery_point_sei>

Set this flag to insert the recovery point SEI message at the beginning of every
intra refresh cycle.


=item I<rdo>

Enable rate distortion optimization.


=item I<max_frame_size>

Maximum encoded frame size in bytes.


=item I<max_frame_size_i>

Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.


=item I<max_frame_size_p>

Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.


=item I<max_slice_size>

Maximum encoded slice size in bytes.


=item I<bitrate_limit>

Toggle bitrate limitations.
Modifies bitrate to be in the range imposed by the QSV encoder. Setting this
flag off may lead to violation of HRD conformance. Mind that specifying bitrate
below the QSV encoder range might significantly affect quality. If on this
option takes effect in non CQP modes: if bitrate is not in the range imposed
by the QSV encoder, it will be changed to be in the range.


=item I<mbbrc>

Setting this flag enables macroblock level bitrate control that generally
improves subjective visual quality. Enabling this flag may have negative impact
on performance and objective visual quality metric.


=item I<low_delay_brc>

Setting this flag turns on or off LowDelayBRC feature in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: -1-default 0-off 1-on


=item I<adaptive_i>

This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.


=item I<adaptive_b>

This flag controls changing of frame type from B to P.


=item I<p_strategy>

Enable P-pyramid: 0-default 1-simple 2-pyramid(bf need to be set to 0).


=item I<b_strategy>

This option controls usage of B frames as reference.


=item I<dblk_idc>

This option disable deblocking. It has value in range 0~2.


=item I<cavlc>

If set, CAVLC is used; if unset, CABAC is used for encoding.


=item I<vcm>

Video conferencing mode, please see ratecontrol method.


=item I<idr_interval>

Distance (in I-frames) between IDR frames.


=item I<pic_timing_sei>

Insert picture timing SEI with pic_struct_syntax element.


=item I<single_sei_nal_unit>

Put all the SEI messages into one NALU.


=item I<max_dec_frame_buffering>

Maximum number of frames buffered in the DPB.


=item I<look_ahead>

Use VBR algorithm with look ahead.


=item I<look_ahead_depth>

Depth of look ahead in number frames.


=item I<look_ahead_downsampling>

Downscaling factor for the frames saved for the lookahead analysis.

=over 4


=item B<unknown>


=item B<auto>


=item B<off>


=item B<2x>


=item B<4x>


=back



=item I<int_ref_type>

Specifies intra refresh type. The major goal of intra refresh is improvement of
error resilience without significant impact on encoded bitstream size caused by
I frames. The SDK encoder achieves this by encoding part of each frame in
refresh cycle using intra MBs. I<none> means no refresh. I<vertical> means
vertical refresh, by column of MBs. I<horizontal> means horizontal refresh,
by rows of MBs. I<slice> means horizontal refresh by slices without
overlapping. In case of I<slice>, in_ref_cycle_size is ignored. To enable
intra refresh, B frame should be set to 0.


=item I<int_ref_cycle_size>

Specifies number of pictures within refresh cycle starting from 2. 0 and 1 are
invalid values.


=item I<int_ref_qp_delta>

Specifies QP difference for inserted intra MBs. This is signed value in
[-51, 51] range if target encoding bit-depth for luma samples is 8 and this
range is [-63, 63] for 10 bit-depth or [-75, 75] for 12 bit-depth respectively.


=item I<int_ref_cycle_dist>

Distance between the beginnings of the intra-refresh cycles in frames.


=item I<profile>


=over 4


=item B<unknown>


=item B<baseline>


=item B<main>


=item B<high>


=back



=item I<a53cc>

Use A53 Closed Captions (if available).


=item I<aud>

Insert the Access Unit Delimiter NAL.


=item I<mfmode>

Multi-Frame Mode.

=over 4


=item B<off>


=item B<auto>


=back



=item I<repeat_pps>

Repeat pps for every frame.


=item I<max_qp_i>

Maximum video quantizer scale for I frame.


=item I<min_qp_i>

Minimum video quantizer scale for I frame.


=item I<max_qp_p>

Maximum video quantizer scale for P frame.


=item I<min_qp_p>

Minimum video quantizer scale for P frame.


=item I<max_qp_b>

Maximum video quantizer scale for B frame.


=item I<min_qp_b>

Minimum video quantizer scale for B frame.


=item I<scenario>

Provides a hint to encoder about the scenario for the encoding session.

=over 4


=item B<unknown>


=item B<displayremoting>


=item B<videoconference>


=item B<archive>


=item B<livestreaming>


=item B<cameracapture>


=item B<videosurveillance>


=item B<gamestreaming>


=item B<remotegaming>


=back



=item I<avbr_accuracy>

Accuracy of the AVBR ratecontrol (unit of tenth of percent).


=item I<avbr_convergence>

Convergence of the AVBR ratecontrol (unit of 100 frames)

The parameters I<avbr_accuracy> and I<avbr_convergence> are for the
average variable bitrate control (AVBR) algorithm.
The algorithm focuses on overall encoding quality while meeting the specified
bitrate, I<target_bitrate>, within the accuracy range I<avbr_accuracy>,
after a I<avbr_Convergence> period. This method does not follow HRD and the
instant bitrate is not capped or padded.


=item I<skip_frame>

Use per-frame metadata "qsv_skip_frame" to skip frame when encoding. This option
defines the usage of this metadata.

=over 4


=item B<no_skip>

Frame skipping is disabled.

=item B<insert_dummy>

Encoder inserts into bitstream frame where all macroblocks are encoded as
skipped.

=item B<insert_nothing>

Similar to insert_dummy, but encoder inserts nothing into bitstream. The skipped
frames are still used in brc. For example, gop still include skipped frames, and
the frames after skipped frames will be larger in size.

=item B<brc_only>

skip_frame metadata indicates the number of missed frames before the current
frame.

=back



=back



=head3 HEVC Options

These options are used by hevc_qsv


=over 4


=item I<extbrc>

Extended bitrate control.


=item I<recovery_point_sei>

Set this flag to insert the recovery point SEI message at the beginning of every
intra refresh cycle.


=item I<rdo>

Enable rate distortion optimization.


=item I<max_frame_size>

Maximum encoded frame size in bytes.


=item I<max_frame_size_i>

Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.


=item I<max_frame_size_p>

Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.


=item I<max_slice_size>

Maximum encoded slice size in bytes.


=item I<mbbrc>

Setting this flag enables macroblock level bitrate control that generally
improves subjective visual quality. Enabling this flag may have negative impact
on performance and objective visual quality metric.


=item I<low_delay_brc>

Setting this flag turns on or off LowDelayBRC feature in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: -1-default 0-off 1-on


=item I<adaptive_i>

This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.


=item I<adaptive_b>

This flag controls changing of frame type from B to P.


=item I<p_strategy>

Enable P-pyramid: 0-default 1-simple 2-pyramid(bf need to be set to 0).


=item I<b_strategy>

This option controls usage of B frames as reference.


=item I<dblk_idc>

This option disable deblocking. It has value in range 0~2.


=item I<idr_interval>

Distance (in I-frames) between IDR frames.

=over 4


=item B<begin_only>

Output an IDR-frame only at the beginning of the stream.

=back



=item I<load_plugin>

A user plugin to load in an internal session.

=over 4


=item B<none>


=item B<hevc_sw>


=item B<hevc_hw>


=back



=item I<load_plugins>

A :-separate list of hexadecimal plugin UIDs to load in
an internal session.


=item I<look_ahead_depth>

Depth of look ahead in number frames, available when extbrc option is enabled.


=item I<profile>

Set the encoding profile (scc requires libmfx E<gt>= 1.32).


=over 4


=item B<unknown>


=item B<main>


=item B<main10>


=item B<mainsp>


=item B<rext>


=item B<scc>


=back



=item I<tier>

Set the encoding tier (only level E<gt>= 4 can support high tier).
This option only takes effect when the level option is specified.


=over 4


=item B<main>


=item B<high>


=back



=item I<gpb>

1: GPB (generalized P/B frame)

0: regular P frame.


=item I<tile_cols>

Number of columns for tiled encoding.


=item I<tile_rows>

Number of rows for tiled encoding.


=item I<aud>

Insert the Access Unit Delimiter NAL.


=item I<pic_timing_sei>

Insert picture timing SEI with pic_struct_syntax element.


=item I<transform_skip>

Turn this option ON to enable transformskip. It is supported on platform equal
or newer than ICL.


=item I<int_ref_type>

Specifies intra refresh type. The major goal of intra refresh is improvement of
error resilience without significant impact on encoded bitstream size caused by
I frames. The SDK encoder achieves this by encoding part of each frame in
refresh cycle using intra MBs. I<none> means no refresh. I<vertical> means
vertical refresh, by column of MBs. I<horizontal> means horizontal refresh,
by rows of MBs. I<slice> means horizontal refresh by slices without
overlapping. In case of I<slice>, in_ref_cycle_size is ignored. To enable
intra refresh, B frame should be set to 0.


=item I<int_ref_cycle_size>

Specifies number of pictures within refresh cycle starting from 2. 0 and 1 are
invalid values.


=item I<int_ref_qp_delta>

Specifies QP difference for inserted intra MBs. This is signed value in
[-51, 51] range if target encoding bit-depth for luma samples is 8 and this
range is [-63, 63] for 10 bit-depth or [-75, 75] for 12 bit-depth respectively.


=item I<int_ref_cycle_dist>

Distance between the beginnings of the intra-refresh cycles in frames.


=item I<max_qp_i>

Maximum video quantizer scale for I frame.


=item I<min_qp_i>

Minimum video quantizer scale for I frame.


=item I<max_qp_p>

Maximum video quantizer scale for P frame.


=item I<min_qp_p>

Minimum video quantizer scale for P frame.


=item I<max_qp_b>

Maximum video quantizer scale for B frame.


=item I<min_qp_b>

Minimum video quantizer scale for B frame.


=item I<scenario>

Provides a hint to encoder about the scenario for the encoding session.

=over 4


=item B<unknown>


=item B<displayremoting>


=item B<videoconference>


=item B<archive>


=item B<livestreaming>


=item B<cameracapture>


=item B<videosurveillance>


=item B<gamestreaming>


=item B<remotegaming>


=back



=item I<avbr_accuracy>

Accuracy of the AVBR ratecontrol (unit of tenth of percent).


=item I<avbr_convergence>

Convergence of the AVBR ratecontrol (unit of 100 frames)

The parameters I<avbr_accuracy> and I<avbr_convergence> are for the
average variable bitrate control (AVBR) algorithm.
The algorithm focuses on overall encoding quality while meeting the specified
bitrate, I<target_bitrate>, within the accuracy range I<avbr_accuracy>,
after a I<avbr_Convergence> period. This method does not follow HRD and the
instant bitrate is not capped or padded.


=item I<skip_frame>

Use per-frame metadata "qsv_skip_frame" to skip frame when encoding. This option
defines the usage of this metadata.

=over 4


=item B<no_skip>

Frame skipping is disabled.

=item B<insert_dummy>

Encoder inserts into bitstream frame where all macroblocks are encoded as
skipped.

=item B<insert_nothing>

Similar to insert_dummy, but encoder inserts nothing into bitstream. The skipped
frames are still used in brc. For example, gop still include skipped frames, and
the frames after skipped frames will be larger in size.

=item B<brc_only>

skip_frame metadata indicates the number of missed frames before the current
frame.

=back



=back



=head3 MPEG2 Options

These options are used by mpeg2_qsv

=over 4


=item I<profile>


=over 4


=item B<unknown>


=item B<simple>


=item B<main>


=item B<high>


=back


=back



=head3 VP9 Options

These options are used by vp9_qsv

=over 4


=item I<profile>


=over 4


=item B<unknown>


=item B<profile0>


=item B<profile1>


=item B<profile2>


=item B<profile3>


=back



=item I<tile_cols>

Number of columns for tiled encoding (requires libmfx E<gt>= 1.29).


=item I<tile_rows>

Number of rows for tiled encoding (requires libmfx  E<gt>= 1.29).

=back



=head3 AV1 Options

These options are used by av1_qsv (requires libvpl).

=over 4


=item I<profile>


=over 4


=item B<unknown>


=item B<main>


=back



=item I<tile_cols>

Number of columns for tiled encoding.


=item I<tile_rows>

Number of rows for tiled encoding.


=item I<adaptive_i>

This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.


=item I<adaptive_b>

This flag controls changing of frame type from B to P.


=item I<b_strategy>

This option controls usage of B frames as reference.


=item I<extbrc>

Extended bitrate control.


=item I<look_ahead_depth>

Depth of look ahead in number frames, available when extbrc option is enabled.


=item I<low_delay_brc>

Setting this flag turns on or off LowDelayBRC feature in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: -1-default 0-off 1-on


=item I<max_frame_size>

Set the allowed max size in bytes for each frame. If the frame size exceeds
the limitation, encoder will adjust the QP value to control the frame size.
Invalid in CQP rate control mode.


=item I<max_frame_size_i>

Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.


=item I<max_frame_size_p>

Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.

=back



=head2 snow



=head3 Options



=over 4


=item B<iterative_dia_size>

dia size for the iterative motion estimation

=back



=head2 VAAPI encoders


Wrappers for hardware encoders accessible via VAAPI.

These encoders only accept input in VAAPI hardware surfaces.  If you have input
in software frames, use the B<hwupload> filter to upload them to the GPU.

The following standard libavcodec options are used:

=over 4


=item *

B<g> / B<gop_size>

=item *

B<bf> / B<max_b_frames>

=item *

B<profile>

If not set, this will be determined automatically from the format of the input
frames and the profiles supported by the driver.

=item *

B<level>

=item *

B<b> / B<bit_rate>

=item *

B<maxrate> / B<rc_max_rate>

=item *

B<bufsize> / B<rc_buffer_size>

=item *

B<rc_init_occupancy> / B<rc_initial_buffer_occupancy>

=item *

B<compression_level>

Speed / quality tradeoff: higher values are faster / worse quality.

=item *

B<q> / B<global_quality>

Size / quality tradeoff: higher values are smaller / worse quality.

=item *

B<qmin>

=item *

B<qmax>

=item *

B<i_qfactor> / B<i_quant_factor>

=item *

B<i_qoffset> / B<i_quant_offset>

=item *

B<b_qfactor> / B<b_quant_factor>

=item *

B<b_qoffset> / B<b_quant_offset>

=item *

B<slices>

=back


All encoders support the following options:

=over 4


=item B<low_power>

Some drivers/platforms offer a second encoder for some codecs intended to use
less power than the default encoder; setting this option will attempt to use
that encoder.  Note that it may support a reduced feature set, so some other
options may not be available in this mode.


=item B<idr_interval>

Set the number of normal intra frames between full-refresh (IDR) frames in
open-GOP mode.  The intra frames are still IRAPs, but will not include global
headers and may have non-decodable leading pictures.


=item B<b_depth>

Set the B-frame reference depth.  When set to one (the default), all B-frames
will refer only to P- or I-frames.  When set to greater values multiple layers
of B-frames will be present, frames in each layer only referring to frames in
higher layers.


=item B<async_depth>

Maximum processing parallelism. Increase this to improve single channel
performance. This option doesn't work if driver doesn't implement vaSyncBuffer
function. Please make sure there are enough hw_frames allocated if a large
number of async_depth is used.


=item B<max_frame_size>

Set the allowed max size in bytes for each frame. If the frame size exceeds
the limitation, encoder will adjust the QP value to control the frame size.
Invalid in CQP rate control mode.


=item B<rc_mode>

Set the rate control mode to use.  A given driver may only support a subset of
modes.

Possible modes:

=over 4


=item B<auto>

Choose the mode automatically based on driver support and the other options.
This is the default.

=item B<CQP>

Constant-quality.

=item B<CBR>

Constant-bitrate.

=item B<VBR>

Variable-bitrate.

=item B<ICQ>

Intelligent constant-quality.

=item B<QVBR>

Quality-defined variable-bitrate.

=item B<AVBR>

Average variable bitrate.

=back



=item B<blbrc>

Enable block level rate control, which assigns different bitrate block by block.
Invalid for CQP mode.


=back


Each encoder also has its own specific options:

=over 4



=item B<av1_vaapi>

B<profile> sets the value of I<seq_profile>.
B<tier> sets the value of I<seq_tier>.
B<level> sets the value of I<seq_level_idx>.


=over 4


=item B<tiles>

Set the number of tiles to encode the input video with, as columns x rows.
(default is auto, which means use minimal tile column/row number).

=item B<tile_groups>

Set tile groups number. All the tiles will be distributed as evenly as possible to
each tile group. (default is 1).

=back



=item B<h264_vaapi>

B<profile> sets the value of I<profile_idc> and the I<constraint_set*_flag>s.
B<level> sets the value of I<level_idc>.


=over 4


=item B<coder>

Set entropy encoder (default is I<cabac>).  Possible values:


=over 4


=item B<ac>


=item B<cabac>

Use CABAC.


=item B<vlc>


=item B<cavlc>

Use CAVLC.

=back



=item B<aud>

Include access unit delimiters in the stream (not included by default).


=item B<sei>

Set SEI message types to include.
Some combination of the following values:

=over 4


=item B<identifier>

Include a I<user_data_unregistered> message containing information about
the encoder.

=item B<timing>

Include picture timing parameters (I<buffering_period> and
I<pic_timing> messages).

=item B<recovery_point>

Include recovery points where appropriate (I<recovery_point> messages).

=back



=back



=item B<hevc_vaapi>

B<profile> and B<level> set the values of
I<general_profile_idc> and I<general_level_idc> respectively.


=over 4


=item B<aud>

Include access unit delimiters in the stream (not included by default).


=item B<tier>

Set I<general_tier_flag>.  This may affect the level chosen for the stream
if it is not explicitly specified.


=item B<sei>

Set SEI message types to include.
Some combination of the following values:

=over 4


=item B<hdr>

Include HDR metadata if the input frames have it
(I<mastering_display_colour_volume> and I<content_light_level>
messages).

=back



=item B<tiles>

Set the number of tiles to encode the input video with, as columns x rows.
Larger numbers allow greater parallelism in both encoding and decoding, but
may decrease coding efficiency.


=back



=item B<mjpeg_vaapi>

Only baseline DCT encoding is supported.  The encoder always uses the standard
quantisation and huffman tables - B<global_quality> scales the standard
quantisation table (range 1-100).

For YUV, 4:2:0, 4:2:2 and 4:4:4 subsampling modes are supported.  RGB is also
supported, and will create an RGB JPEG.


=over 4


=item B<jfif>

Include JFIF header in each frame (not included by default).

=item B<huffman>

Include standard huffman tables (on by default).  Turning this off will save
a few hundred bytes in each output frame, but may lose compatibility with some
JPEG decoders which don't fully handle MJPEG.

=back



=item B<mpeg2_vaapi>

B<profile> and B<level> set the value of I<profile_and_level_indication>.


=item B<vp8_vaapi>

B-frames are not supported.

B<global_quality> sets the I<q_idx> used for non-key frames (range 0-127).


=over 4


=item B<loop_filter_level>


=item B<loop_filter_sharpness>

Manually set the loop filter parameters.

=back



=item B<vp9_vaapi>

B<global_quality> sets the I<q_idx> used for P-frames (range 0-255).


=over 4


=item B<loop_filter_level>


=item B<loop_filter_sharpness>

Manually set the loop filter parameters.

=back


B-frames are supported, but the output stream is always in encode order rather than display
order.  If B-frames are enabled, it may be necessary to use the B<vp9_raw_reorder>
bitstream filter to modify the output stream to display frames in the correct order.

Only normal frames are produced - the B<vp9_superframe> bitstream filter may be
required to produce a stream usable with all decoders.


=back



=head2 vbn


Vizrt Binary Image encoder.

This format is used by the broadcast vendor Vizrt for quick texture streaming.
Advanced features of the format such as LZW compression of texture data or
generation of mipmaps are not supported.


=head3 Options



=over 4


=item B<format> I<string>

Sets the texture compression used by the VBN file. Can be I<dxt1>,
I<dxt5> or I<raw>. Default is I<dxt5>.

=back



=head2 vc2


SMPTE VC-2 (previously BBC Dirac Pro). This codec was primarily aimed at
professional broadcasting but since it supports yuv420, yuv422 and yuv444 at
8 (limited range or full range), 10 or 12 bits, this makes it suitable for
other tasks which require low overhead and low compression (like screen
recording).


=head3 Options



=over 4



=item B<b>

Sets target video bitrate. Usually that's around 1:6 of the uncompressed
video bitrate (e.g. for 1920x1080 50fps yuv422p10 that's around 400Mbps). Higher
values (close to the uncompressed bitrate) turn on lossless compression mode.


=item B<field_order>

Enables field coding when set (e.g. to tt - top field first) for interlaced
inputs. Should increase compression with interlaced content as it splits the
fields and encodes each separately.


=item B<wavelet_depth>

Sets the total amount of wavelet transforms to apply, between 1 and 5 (default).
Lower values reduce compression and quality. Less capable decoders may not be
able to handle values of B<wavelet_depth> over 3.


=item B<wavelet_type>

Sets the transform type. Currently only I<5_3> (LeGall) and I<9_7>
(Deslauriers-Dubuc)
are implemented, with 9_7 being the one with better compression and thus
is the default.


=item B<slice_width>


=item B<slice_height>

Sets the slice size for each slice. Larger values result in better compression.
For compatibility with other more limited decoders use B<slice_width> of
32 and B<slice_height> of 8.


=item B<tolerance>

Sets the undershoot tolerance of the rate control system in percent. This is
to prevent an expensive search from being run.


=item B<qm>

Sets the quantization matrix preset to use by default or when B<wavelet_depth>
is set to 5

=over 4


=item -

I<default>
Uses the default quantization matrix from the specifications, extended with
values for the fifth level. This provides a good balance between keeping detail
and omitting artifacts.


=item -

I<flat>
Use a completely zeroed out quantization matrix. This increases PSNR but might
reduce perception. Use in bogus benchmarks.


=item -

I<color>
Reduces detail but attempts to preserve color at extremely low bitrates.

=back



=back




=head1 SUBTITLES ENCODERS



=head2 dvbsub


This codec encodes the bitmap subtitle format that is used in DVB
broadcasts and recordings. The bitmaps are typically embedded in a
container such as MPEG-TS as a separate stream.


=head3 Options



=over 4


=item B<min_bpp> I<integer (2, 4, or 8)>

Set a minimum bits-per-pixel value for the subtitle color lookup tables.

DVB supports 2, 4, and 8 bits-per-pixel color lookup tables.  This
option enables forcing a particular bits-per-pixel value regardless of
the number of colors.  Since not all players support or properly
support 2 bits-per-pixel, this value defaults to 4.


=back



=head2 dvdsub


This codec encodes the bitmap subtitle format that is used in DVDs.
Typically they are stored in VOBSUB file pairs (*.idx + *.sub),
and they can also be used in Matroska files.


=head3 Options



=over 4


=item B<palette>

Specify the global palette used by the bitmaps.

The format for this option is a string containing 16 24-bits hexadecimal
numbers (without 0x prefix) separated by commas, for example C<0d00ee,
ee450d, 101010, eaeaea, 0ce60b, ec14ed, ebff0b, 0d617a, 7b7b7b, d1d1d1,
7b2a0e, 0d950c, 0f007b, cf0dec, cfa80c, 7c127b>.


=item B<even_rows_fix>

When set to 1, enable a work-around that makes the number of pixel rows
even in all subtitles.  This fixes a problem with some players that
cut off the bottom row if the number is odd.  The work-around just adds
a fully transparent row if needed.  The overhead is low, typically
one byte per subtitle on average.

By default, this work-around is disabled.

=back



=head2 lrc


This codec encodes the LRC lyrics format.


=head3 Options



=over 4


=item B<precision>

Specify the precision of the fractional part of the timestamp. Time base is
determined based on this value.

Defaults to 2 for centiseconds.

=back




=head1 SEE ALSO



ffmpeg(1), ffplay(1), ffprobe(1), libavcodec(3)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



