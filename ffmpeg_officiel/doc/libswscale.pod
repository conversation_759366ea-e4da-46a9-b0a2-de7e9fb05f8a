=encoding utf8

=head1 NAME

libswscale - video scaling and pixel format conversion library

=head1 DESCRIPTION


The libswscale library performs highly optimized image scaling and
colorspace and pixel format conversion operations.

Specifically, this library performs the following conversions:


=over 4


=item *

I<Rescaling>: is the process of changing the video size. Several
rescaling options and algorithms are available. This is usually a
lossy process.


=item *

I<Pixel format conversion>: is the process of converting the image
format and colorspace of the image, for example from planar YUV420P to
RGB24 packed. It also handles packing conversion, that is converts
from packed layout (all pixels belonging to distinct planes
interleaved in the same buffer), to planar layout (all samples
belonging to the same plane stored in a dedicated buffer or "plane").

This is usually a lossy process in case the source and destination
colorspaces differ.

=back




=head1 SEE ALSO



ffmpeg(1), ffplay(1), ffprobe(1),
ffmpeg-scaler(1),
libavutil(3)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



