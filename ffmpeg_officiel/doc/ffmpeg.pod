=encoding utf8

=head1 NAME

ffmpeg - ffmpeg media converter

=head1 SYNOPSIS


ffmpeg [I<global_options>] {[I<input_file_options>] -i F<input_url>} ... {[I<output_file_options>] F<output_url>} ...


=head1 DESCRIPTION


B<ffmpeg> is a universal media converter. It can read a wide variety of
inputs - including live grabbing/recording devices - filter, and transcode them
into a plethora of output formats.

B<ffmpeg> reads from an arbitrary number of inputs (which can be regular
files, pipes, network streams, grabbing devices, etc.), specified by the
C<-i> option, and writes to an arbitrary number of outputs, which are
specified by a plain output url. Anything found on the command line which cannot
be interpreted as an option is considered to be an output url.

Each input or output can, in principle, contain any number of elementary streams
of different types (video/audio/subtitle/attachment/data), though the allowed
stream counts and/or types may be limited by the container format. Selecting
which streams from which inputs will go into which output is either done
automatically or with the C<-map> option (see the B<Stream selection>
chapter).

To refer to inputs/outputs in options, you must use their indices (0-based).
E.g. the first input is C<0>, the second is C<1>, etc. Similarly,
streams within an input/output are referred to by their indices. E.g. C<2:3>
refers to the fourth stream in the third input or output. Also see the
B<Stream specifiers> chapter.

As a general rule, options are applied to the next specified
file. Therefore, order is important, and you can have the same
option on the command line multiple times. Each occurrence is
then applied to the next input or output file.
Exceptions from this rule are the global options (e.g. verbosity level),
which should be specified first.

Do not mix input and output files -- first specify all input files, then all
output files. Also do not mix options which belong to different files. All
options apply ONLY to the next input or output file and are reset between files.

Some simple examples follow.


=over 4


=item *

Convert an input media file to a different format, by re-encoding media streams:
	
	ffmpeg -i input.avi output.mp4



=item *

Set the video bitrate of the output file to 64 kbit/s:
	
	ffmpeg -i input.avi -b:v 64k -bufsize 64k output.mp4



=item *

Force the frame rate of the output file to 24 fps:
	
	ffmpeg -i input.avi -r 24 output.mp4



=item *

Force the frame rate of the input file (valid for raw formats only) to 1 fps and
the frame rate of the output file to 24 fps:
	
	ffmpeg -r 1 -i input.m2v -r 24 output.mp4


=back


The format option may be needed for raw input files.



=head1 DETAILED DESCRIPTION


B<ffmpeg> builds a transcoding pipeline out of the components listed
below. The program's operation then consists of input data chunks flowing from
the sources down the pipes towards the sinks, while being transformed by the
components they encounter along the way.

The following kinds of components are available:

=over 4


=item *

I<Demuxers> (short for "demultiplexers") read an input source in order to
extract


=over 4


=item *

global properties such as metadata or chapters;

=item *

list of input elementary streams and their properties

=back


One demuxer instance is created for each B<-i> option, and sends encoded
I<packets> to I<decoders> or I<muxers>.

In other literature, demuxers are sometimes called I<splitters>, because
their main function is splitting a file into elementary streams (though some
files only contain one elementary stream).

A schematic representation of a demuxer looks like this:
	
	┌──────────┬───────────────────────┐
	│ demuxer  │                       │ packets for stream 0
	╞══════════╡ elementary stream 0   ├──────────────────────►
	│          │                       │
	│  global  ├───────────────────────┤
	│properties│                       │ packets for stream 1
	│   and    │ elementary stream 1   ├──────────────────────►
	│ metadata │                       │
	│          ├───────────────────────┤
	│          │                       │
	│          │     ...........       │
	│          │                       │
	│          ├───────────────────────┤
	│          │                       │ packets for stream N
	│          │ elementary stream N   ├──────────────────────►
	│          │                       │
	└──────────┴───────────────────────┘
	     ▲
	     │
	     │ read from file, network stream,
	     │     grabbing device, etc.
	     │



=item *

I<Decoders> receive encoded (compressed) I<packets> for an audio, video,
or subtitle elementary stream, and decode them into raw I<frames> (arrays of
pixels for video, PCM for audio). A decoder is typically associated with (and
receives its input from) an elementary stream in a I<demuxer>, but sometimes
may also exist on its own (see B<Loopback decoders>).

A schematic representation of a decoder looks like this:
	
	          ┌─────────┐
	 packets  │         │ raw frames
	─────────►│ decoder ├────────────►
	          │         │
	          └─────────┘



=item *

I<Filtergraphs> process and transform raw audio or video I<frames>. A
filtergraph consists of one or more individual I<filters> linked into a
graph. Filtergraphs come in two flavors - I<simple> and I<complex>,
configured with the B<-filter> and B<-filter_complex> options,
respectively.

A simple filtergraph is associated with an I<output elementary stream>; it
receives the input to be filtered from a I<decoder> and sends filtered
output to that output stream's I<encoder>.

A simple video filtergraph that performs deinterlacing (using the C<yadif>
deinterlacer) followed by resizing (using the C<scale> filter) can look like
this:
	
	
	             ┌────────────────────────┐
	             │  simple filtergraph    │
	 frames from ╞════════════════════════╡ frames for
	 a decoder   │  ┌───────┐  ┌───────┐  │ an encoder
	────────────►├─►│ yadif ├─►│ scale ├─►│────────────►
	             │  └───────┘  └───────┘  │
	             └────────────────────────┘


A complex filtergraph is standalone and not associated with any specific stream.
It may have multiple (or zero) inputs, potentially of different types (audio or
video), each of which receiving data either from a decoder or another complex
filtergraph's output. It also has one or more outputs that feed either an
encoder or another complex filtergraph's input.

The following example diagram represents a complex filtergraph with 3 inputs and
2 outputs (all video):
	
	          ┌─────────────────────────────────────────────────┐
	          │               complex filtergraph               │
	          ╞═════════════════════════════════════════════════╡
	 frames   ├───────┐  ┌─────────┐      ┌─────────┐  ┌────────┤ frames
	─────────►│input 0├─►│ overlay ├─────►│ overlay ├─►│output 0├────────►
	          ├───────┘  │         │      │         │  └────────┤
	 frames   ├───────┐╭►│         │    ╭►│         │           │
	─────────►│input 1├╯ └─────────┘    │ └─────────┘           │
	          ├───────┘                 │                       │
	 frames   ├───────┐ ┌─────┐ ┌─────┬─╯              ┌────────┤ frames
	─────────►│input 2├►│scale├►│split├───────────────►│output 1├────────►
	          ├───────┘ └─────┘ └─────┘                └────────┤
	          └─────────────────────────────────────────────────┘

Frames from second input are overlaid over those from the first. Frames from the
third input are rescaled, then the duplicated into two identical streams. One of
them is overlaid over the combined first two inputs, with the result exposed as
the filtergraph's first output. The other duplicate ends up being the
filtergraph's second output.


=item *

I<Encoders> receive raw audio, video, or subtitle I<frames> and encode
them into encoded I<packets>. The encoding (compression) process is
typically I<lossy> - it degrades stream quality to make the output smaller;
some encoders are I<lossless>, but at the cost of much higher output size. A
video or audio encoder receives its input from some filtergraph's output,
subtitle encoders receive input from a decoder (since subtitle filtering is not
supported yet). Every encoder is associated with some muxer's I<output
elementary stream> and sends its output to that muxer.

A schematic representation of an encoder looks like this:
	
	             ┌─────────┐
	 raw frames  │         │ packets
	────────────►│ encoder ├─────────►
	             │         │
	             └─────────┘



=item *

I<Muxers> (short for "multiplexers") receive encoded I<packets> for
their elementary streams from encoders (the I<transcoding> path) or directly
from demuxers (the I<streamcopy> path), interleave them (when there is more
than one elementary stream), and write the resulting bytes into the output file
(or pipe, network stream, etc.).

A schematic representation of a muxer looks like this:
	
	                       ┌──────────────────────┬───────────┐
	 packets for stream 0  │                      │   muxer   │
	──────────────────────►│  elementary stream 0 ╞═══════════╡
	                       │                      │           │
	                       ├──────────────────────┤  global   │
	 packets for stream 1  │                      │properties │
	──────────────────────►│  elementary stream 1 │   and     │
	                       │                      │ metadata  │
	                       ├──────────────────────┤           │
	                       │                      │           │
	                       │     ...........      │           │
	                       │                      │           │
	                       ├──────────────────────┤           │
	 packets for stream N  │                      │           │
	──────────────────────►│  elementary stream N │           │
	                       │                      │           │
	                       └──────────────────────┴─────┬─────┘
	                                                    │
	                     write to file, network stream, │
	                         grabbing device, etc.      │
	                                                    │
	                                                    ▼



=back



=head2 Streamcopy

The simplest pipeline in B<ffmpeg> is single-stream
I<streamcopy>, that is copying one I<input elementary stream>'s packets
without decoding, filtering, or encoding them. As an example, consider an input
file called F<INPUT.mkv> with 3 elementary streams, from which we take the
second and write it to file F<OUTPUT.mp4>. A schematic representation of
such a pipeline looks like this:
	
	┌──────────┬─────────────────────┐
	│ demuxer  │                     │ unused
	╞══════════╡ elementary stream 0 ├────────╳
	│          │                     │
	│INPUT.mkv ├─────────────────────┤          ┌──────────────────────┬───────────┐
	│          │                     │ packets  │                      │   muxer   │
	│          │ elementary stream 1 ├─────────►│  elementary stream 0 ╞═══════════╡
	│          │                     │          │                      │OUTPUT.mp4 │
	│          ├─────────────────────┤          └──────────────────────┴───────────┘
	│          │                     │ unused
	│          │ elementary stream 2 ├────────╳
	│          │                     │
	└──────────┴─────────────────────┘


The above pipeline can be constructed with the following commandline:
	
	ffmpeg -i INPUT.mkv -map 0:1 -c copy OUTPUT.mp4


In this commandline

=over 4



=item *

there is a single input F<INPUT.mkv>;


=item *

there are no input options for this input;


=item *

there is a single output F<OUTPUT.mp4>;


=item *

there are two output options for this output:


=over 4


=item *

C<-map 0:1> selects the input stream to be used - from input with index 0
(i.e. the first one) the stream with index 1 (i.e. the second one);


=item *

C<-c copy> selects the C<copy> encoder, i.e. streamcopy with no decoding
or encoding.

=back



=back


Streamcopy is useful for changing the elementary stream count, container format,
or modifying container-level metadata. Since there is no decoding or encoding,
it is very fast and there is no quality loss. However, it might not work in some
cases because of a variety of factors (e.g. certain information required by the
target container is not available in the source). Applying filters is obviously
also impossible, since filters work on decoded frames.

More complex streamcopy scenarios can be constructed - e.g. combining streams
from two input files into a single output:
	
	┌──────────┬────────────────────┐         ┌────────────────────┬───────────┐
	│ demuxer 0│                    │ packets │                    │   muxer   │
	╞══════════╡elementary stream 0 ├────────►│elementary stream 0 ╞═══════════╡
	│INPUT0.mkv│                    │         │                    │OUTPUT.mp4 │
	└──────────┴────────────────────┘         ├────────────────────┤           │
	┌──────────┬────────────────────┐         │                    │           │
	│ demuxer 1│                    │ packets │elementary stream 1 │           │
	╞══════════╡elementary stream 0 ├────────►│                    │           │
	│INPUT1.aac│                    │         └────────────────────┴───────────┘
	└──────────┴────────────────────┘

that can be built by the commandline
	
	ffmpeg -i INPUT0.mkv -i INPUT1.aac -map 0:0 -map 1:0 -c copy OUTPUT.mp4


The output B<-map> option is used twice here, creating two streams in the
output file - one fed by the first input and one by the second. The single
instance of the B<-c> option selects streamcopy for both of those streams.
You could also use multiple instances of this option together with
B<Stream specifiers> to apply different values to each stream, as will be
demonstrated in following sections.

A converse scenario is splitting multiple streams from a single input into
multiple outputs:
	
	┌──────────┬─────────────────────┐          ┌───────────────────┬───────────┐
	│ demuxer  │                     │ packets  │                   │ muxer 0   │
	╞══════════╡ elementary stream 0 ├─────────►│elementary stream 0╞═══════════╡
	│          │                     │          │                   │OUTPUT0.mp4│
	│INPUT.mkv ├─────────────────────┤          └───────────────────┴───────────┘
	│          │                     │ packets  ┌───────────────────┬───────────┐
	│          │ elementary stream 1 ├─────────►│                   │ muxer 1   │
	│          │                     │          │elementary stream 0╞═══════════╡
	└──────────┴─────────────────────┘          │                   │OUTPUT1.mp4│
	                                            └───────────────────┴───────────┘

built with
	
	ffmpeg -i INPUT.mkv -map 0:0 -c copy OUTPUT0.mp4 -map 0:1 -c copy OUTPUT1.mp4

Note how a separate instance of the B<-c> option is needed for every
output file even though their values are the same. This is because non-global
options (which is most of them) only apply in the context of the file before
which they are placed.

These  examples can of course be further generalized into arbitrary remappings
of any number of inputs into any number of outputs.


=head2 Transcoding

I<Transcoding> is the process of decoding a stream and then encoding it
again. Since encoding tends to be computationally expensive and in most cases
degrades the stream quality (i.e. it is I<lossy>), you should only transcode
when you need to and perform streamcopy otherwise. Typical reasons to transcode
are:


=over 4


=item *

applying filters - e.g. resizing, deinterlacing, or overlaying video; resampling
or mixing audio;


=item *

you want to feed the stream to something that cannot decode the original codec.

=back

Note that B<ffmpeg> will transcode all audio, video, and subtitle streams
unless you specify B<-c copy> for them.

Consider an example pipeline that reads an input file with one audio and one
video stream, transcodes the video and copies the audio into a single output
file. This can be schematically represented as follows
	
	┌──────────┬─────────────────────┐
	│ demuxer  │                     │       audio packets
	╞══════════╡ stream 0 (audio)    ├─────────────────────────────────────╮
	│          │                     │                                     │
	│INPUT.mkv ├─────────────────────┤ video    ┌─────────┐     raw        │
	│          │                     │ packets  │  video  │ video frames   │
	│          │ stream 1 (video)    ├─────────►│ decoder ├──────────────╮ │
	│          │                     │          │         │              │ │
	└──────────┴─────────────────────┘          └─────────┘              │ │
	                                                                     ▼ ▼
	                                                                     │ │
	┌──────────┬─────────────────────┐ video    ┌─────────┐              │ │
	│ muxer    │                     │ packets  │  video  │              │ │
	╞══════════╡ stream 0 (video)    │◄─────────┤ encoder ├──────────────╯ │
	│          │                     │          │(libx264)│                │
	│OUTPUT.mp4├─────────────────────┤          └─────────┘                │
	│          │                     │                                     │
	│          │ stream 1 (audio)    │◄────────────────────────────────────╯
	│          │                     │
	└──────────┴─────────────────────┘

and implemented with the following commandline:
	
	ffmpeg -i INPUT.mkv -map 0:v -map 0:a -c:v libx264 -c:a copy OUTPUT.mp4

Note how it uses stream specifiers C<:v> and C<:a> to select input
streams and apply different values of the B<-c> option to them; see the
B<Stream specifiers> section for more details.



=head2 Filtering


When transcoding, audio and video streams can be filtered before encoding, with
either a I<simple> or I<complex> filtergraph.


=head3 Simple filtergraphs


Simple filtergraphs are those that have exactly one input and output, both of
the same type (audio or video). They are configured with the per-stream
B<-filter> option (with B<-vf> and B<-af> aliases for
B<-filter:v> (video) and B<-filter:a> (audio) respectively). Note
that simple filtergraphs are tied to their output stream, so e.g. if you have
multiple audio streams, B<-af> will create a separate filtergraph for each
one.

Taking the transcoding example from above, adding filtering (and omitting audio,
for clarity) makes it look like this:
	
	┌──────────┬───────────────┐
	│ demuxer  │               │          ┌─────────┐
	╞══════════╡ video stream  │ packets  │  video  │ frames
	│INPUT.mkv │               ├─────────►│ decoder ├─────►───╮
	│          │               │          └─────────┘         │
	└──────────┴───────────────┘                              │
	                                  ╭───────────◄───────────╯
	                                  │   ┌────────────────────────┐
	                                  │   │  simple filtergraph    │
	                                  │   ╞════════════════════════╡
	                                  │   │  ┌───────┐  ┌───────┐  │
	                                  ╰──►├─►│ yadif ├─►│ scale ├─►├╮
	                                      │  └───────┘  └───────┘  ││
	                                      └────────────────────────┘│
	                                                                │
	                                                                │
	┌──────────┬───────────────┐ video    ┌─────────┐               │
	│ muxer    │               │ packets  │  video  │               │
	╞══════════╡ video stream  │◄─────────┤ encoder ├───────◄───────╯
	│OUTPUT.mp4│               │          │         │
	│          │               │          └─────────┘
	└──────────┴───────────────┘



=head3 Complex filtergraphs


Complex filtergraphs are those which cannot be described as simply a linear
processing chain applied to one stream. This is the case, for example, when the
graph has more than one input and/or output, or when output stream type is
different from input. Complex filtergraphs are configured with the
B<-filter_complex> option. Note that this option is global, since a
complex filtergraph, by its nature, cannot be unambiguously associated with a
single stream or file. Each instance of B<-filter_complex> creates a new
complex filtergraph, and there can be any number of them.

A trivial example of a complex filtergraph is the C<overlay> filter, which
has two video inputs and one video output, containing one video overlaid on top
of the other. Its audio counterpart is the C<amix> filter.



=head2 Loopback decoders

While decoders are normally associated with demuxer streams, it is also possible
to create "loopback" decoders that decode the output from some encoder and allow
it to be fed back to complex filtergraphs. This is done with the C<-dec>
directive, which takes as a parameter the index of the output stream that should
be decoded. Every such directive creates a new loopback decoder, indexed with
successive integers starting at zero. These indices should then be used to refer
to loopback decoders in complex filtergraph link labels, as described in the
documentation for B<-filter_complex>.

Decoding AVOptions can be passed to loopback decoders by placing them before
C<-dec>, analogously to input/output options.

E.g. the following example:

	
	ffmpeg -i INPUT                                        \
	  -map 0:v:0 -c:v libx264 -crf 45 -f null -            \
	  -threads 3 -dec 0:0                                  \
	  -filter_complex '[0:v][dec:0]hstack[stack]'          \
	  -map '[stack]' -c:v ffv1 OUTPUT


reads an input video and

=over 4


=item *

(line 2) encodes it with C<libx264> at low quality;


=item *

(line 3) decodes this encoded stream using 3 threads;


=item *

(line 4) places decoded video side by side with the original input video;


=item *

(line 5) combined video is then losslessly encoded and written into
F<OUTPUT>.


=back


Such a transcoding pipeline can be represented with the following diagram:
	
	┌──────────┬───────────────┐
	│ demuxer  │               │   ┌─────────┐            ┌─────────┐    ┌────────────────────┐
	╞══════════╡ video stream  │   │  video  │            │  video  │    │ null muxer         │
	│   INPUT  │               ├──►│ decoder ├──┬────────►│ encoder ├─┬─►│(discards its input)│
	│          │               │   └─────────┘  │         │(libx264)│ │  └────────────────────┘
	└──────────┴───────────────┘                │         └─────────┘ │
	                                 ╭───────◄──╯   ┌─────────┐       │
	                                 │              │loopback │       │
	                                 │ ╭─────◄──────┤ decoder ├────◄──╯
	                                 │ │            └─────────┘
	                                 │ │
	                                 │ │
	                                 │ │  ┌───────────────────┐
	                                 │ │  │complex filtergraph│
	                                 │ │  ╞═══════════════════╡
	                                 │ │  │  ┌─────────────┐  │
	                                 ╰─╫─►├─►│   hstack    ├─►├╮
	                                   ╰─►├─►│             │  ││
	                                      │  └─────────────┘  ││
	                                      └───────────────────┘│
	                                                           │
	┌──────────┬───────────────┐  ┌─────────┐                  │
	│ muxer    │               │  │  video  │                  │
	╞══════════╡ video stream  │◄─┤ encoder ├───────◄──────────╯
	│  OUTPUT  │               │  │ (ffv1)  │
	│          │               │  └─────────┘
	└──────────┴───────────────┘






=head1 STREAM SELECTION


B<ffmpeg> provides the C<-map> option for manual control of stream selection in each
output file. Users can skip C<-map> and let ffmpeg perform automatic stream selection as
described below. The C<-vn / -an / -sn / -dn> options can be used to skip inclusion of
video, audio, subtitle and data streams respectively, whether manually mapped or automatically
selected, except for those streams which are outputs of complex filtergraphs.


=head2 Description

The sub-sections that follow describe the various rules that are involved in stream selection.
The examples that follow next show how these rules are applied in practice.

While every effort is made to accurately reflect the behavior of the program, FFmpeg is under
continuous development and the code may have changed since the time of this writing.


=head3 Automatic stream selection


In the absence of any map options for a particular output file, ffmpeg inspects the output
format to check which type of streams can be included in it, viz. video, audio and/or
subtitles. For each acceptable stream type, ffmpeg will pick one stream, when available,
from among all the inputs.

It will select that stream based upon the following criteria:

=over 4


=item *

for video, it is the stream with the highest resolution,

=item *

for audio, it is the stream with the most channels,

=item *

for subtitles, it is the first subtitle stream found but there's a caveat.
The output format's default subtitle encoder can be either text-based or image-based,
and only a subtitle stream of the same type will be chosen.

=back


In the case where several streams of the same type rate equally, the stream with the lowest
index is chosen.

Data or attachment streams are not automatically selected and can only be included
using C<-map>.

=head3 Manual stream selection


When C<-map> is used, only user-mapped streams are included in that output file,
with one possible exception for filtergraph outputs described below.


=head3 Complex filtergraphs


If there are any complex filtergraph output streams with unlabeled pads, they will be added
to the first output file. This will lead to a fatal error if the stream type is not supported
by the output format. In the absence of the map option, the inclusion of these streams leads
to the automatic stream selection of their types being skipped. If map options are present,
these filtergraph streams are included in addition to the mapped streams.

Complex filtergraph output streams with labeled pads must be mapped once and exactly once.


=head3 Stream handling


Stream handling is independent of stream selection, with an exception for subtitles described
below. Stream handling is set via the C<-codec> option addressed to streams within a
specific I<output> file. In particular, codec options are applied by ffmpeg after the
stream selection process and thus do not influence the latter. If no C<-codec> option is
specified for a stream type, ffmpeg will select the default encoder registered by the output
file muxer.

An exception exists for subtitles. If a subtitle encoder is specified for an output file, the
first subtitle stream found of any type, text or image, will be included. ffmpeg does not validate
if the specified encoder can convert the selected stream or if the converted stream is acceptable
within the output format. This applies generally as well: when the user sets an encoder manually,
the stream selection process cannot check if the encoded stream can be muxed into the output file.
If it cannot, ffmpeg will abort and I<all> output files will fail to be processed.


=head2 Examples


The following examples illustrate the behavior, quirks and limitations of ffmpeg's stream
selection methods.

They assume the following three input files.

	
	
	input file 'A.avi'
	      stream 0: video 640x360
	      stream 1: audio 2 channels
	
	input file 'B.mp4'
	      stream 0: video 1920x1080
	      stream 1: audio 2 channels
	      stream 2: subtitles (text)
	      stream 3: audio 5.1 channels
	      stream 4: subtitles (text)
	
	input file 'C.mkv'
	      stream 0: video 1280x720
	      stream 1: audio 2 channels
	      stream 2: subtitles (image)



=head4 Example: automatic stream selection

	
	ffmpeg -i A.avi -i B.mp4 out1.mkv out2.wav -map 1:a -c:a copy out3.mov

There are three output files specified, and for the first two, no C<-map> options
are set, so ffmpeg will select streams for these two files automatically.

F<out1.mkv> is a Matroska container file and accepts video, audio and subtitle streams,
so ffmpeg will try to select one of each type.For video, it will select C<stream 0> from F<B.mp4>, which has the highest
resolution among all the input video streams.For audio, it will select C<stream 3> from F<B.mp4>, since it has the greatest
number of channels.For subtitles, it will select C<stream 2> from F<B.mp4>, which is the first subtitle
stream from among F<A.avi> and F<B.mp4>.

F<out2.wav> accepts only audio streams, so only C<stream 3> from F<B.mp4> is
selected.

For F<out3.mov>, since a C<-map> option is set, no automatic stream selection will
occur. The C<-map 1:a> option will select all audio streams from the second input
F<B.mp4>. No other streams will be included in this output file.

For the first two outputs, all included streams will be transcoded. The encoders chosen will
be the default ones registered by each output format, which may not match the codec of the
selected input streams.

For the third output, codec option for audio streams has been set
to C<copy>, so no decoding-filtering-encoding operations will occur, or I<can> occur.
Packets of selected streams shall be conveyed from the input file and muxed within the output
file.


=head4 Example: automatic subtitles selection

	
	ffmpeg -i C.mkv out1.mkv -c:s dvdsub -an out2.mkv

Although F<out1.mkv> is a Matroska container file which accepts subtitle streams, only a
video and audio stream shall be selected. The subtitle stream of F<C.mkv> is image-based
and the default subtitle encoder of the Matroska muxer is text-based, so a transcode operation
for the subtitles is expected to fail and hence the stream isn't selected. However, in
F<out2.mkv>, a subtitle encoder is specified in the command and so, the subtitle stream is
selected, in addition to the video stream. The presence of C<-an> disables audio stream
selection for F<out2.mkv>.


=head4 Example: unlabeled filtergraph outputs

	
	ffmpeg -i A.avi -i C.mkv -i B.mp4 -filter_complex "overlay" out1.mp4 out2.srt

A filtergraph is setup here using the C<-filter_complex> option and consists of a single
video filter. The C<overlay> filter requires exactly two video inputs, but none are
specified, so the first two available video streams are used, those of F<A.avi> and
F<C.mkv>. The output pad of the filter has no label and so is sent to the first output file
F<out1.mp4>. Due to this, automatic selection of the video stream is skipped, which would
have selected the stream in F<B.mp4>. The audio stream with most channels viz. C<stream 3>
in F<B.mp4>, is chosen automatically. No subtitle stream is chosen however, since the MP4
format has no default subtitle encoder registered, and the user hasn't specified a subtitle encoder.

The 2nd output file, F<out2.srt>, only accepts text-based subtitle streams. So, even though
the first subtitle stream available belongs to F<C.mkv>, it is image-based and hence skipped.
The selected stream, C<stream 2> in F<B.mp4>, is the first text-based subtitle stream.


=head4 Example: labeled filtergraph outputs

	
	ffmpeg -i A.avi -i B.mp4 -i C.mkv -filter_complex "[1:v]hue=s=0[outv];overlay;aresample" \
	       -map '[outv]' -an        out1.mp4 \
	                                out2.mkv \
	       -map '[outv]' -map 1:a:0 out3.mkv


The above command will fail, as the output pad labelled C<[outv]> has been mapped twice.
None of the output files shall be processed.

	
	ffmpeg -i A.avi -i B.mp4 -i C.mkv -filter_complex "[1:v]hue=s=0[outv];overlay;aresample" \
	       -an        out1.mp4 \
	                  out2.mkv \
	       -map 1:a:0 out3.mkv


This command above will also fail as the hue filter output has a label, C<[outv]>,
and hasn't been mapped anywhere.

The command should be modified as follows,
	
	ffmpeg -i A.avi -i B.mp4 -i C.mkv -filter_complex "[1:v]hue=s=0,split=2[outv1][outv2];overlay;aresample" \
	        -map '[outv1]' -an        out1.mp4 \
	                                  out2.mkv \
	        -map '[outv2]' -map 1:a:0 out3.mkv

The video stream from F<B.mp4> is sent to the hue filter, whose output is cloned once using
the split filter, and both outputs labelled. Then a copy each is mapped to the first and third
output files.

The overlay filter, requiring two video inputs, uses the first two unused video streams. Those
are the streams from F<A.avi> and F<C.mkv>. The overlay output isn't labelled, so it is
sent to the first output file F<out1.mp4>, regardless of the presence of the C<-map> option.

The aresample filter is sent the first unused audio stream, that of F<A.avi>. Since this filter
output is also unlabelled, it too is mapped to the first output file. The presence of C<-an>
only suppresses automatic or manual stream selection of audio streams, not outputs sent from
filtergraphs. Both these mapped streams shall be ordered before the mapped stream in F<out1.mp4>.

The video, audio and subtitle streams mapped to C<out2.mkv> are entirely determined by
automatic stream selection.

F<out3.mkv> consists of the cloned video output from the hue filter and the first audio
stream from F<B.mp4>.

=head1 OPTIONS


All the numerical options, if not specified otherwise, accept a string
representing a number as input, which may be followed by one of the SI
unit prefixes, for example: 'K', 'M', or 'G'.

If 'i' is appended to the SI unit prefix, the complete prefix will be
interpreted as a unit prefix for binary multiples, which are based on
powers of 1024 instead of powers of 1000. Appending 'B' to the SI unit
prefix multiplies the value by 8. This allows using, for example:
'KB', 'MiB', 'G' and 'B' as number suffixes.

Options which do not take arguments are boolean options, and set the
corresponding value to true. They can be set to false by prefixing
the option name with "no". For example using "-nofoo"
will set the boolean option with name "foo" to false.

Options that take arguments support a special syntax where the argument given on
the command line is interpreted as a path to the file from which the actual
argument value is loaded. To use this feature, add a forward slash '/'
immediately before the option name (after the leading dash). E.g.
	
	ffmpeg -i INPUT -/filter:v filter.script OUTPUT

will load a filtergraph description from the file named F<filter.script>.



=head2 Stream specifiers

Some options are applied per-stream, e.g. bitrate or codec. Stream specifiers
are used to precisely specify which stream(s) a given option belongs to.

A stream specifier is a string generally appended to the option name and
separated from it by a colon. E.g. C<-codec:a:1 ac3> contains the
C<a:1> stream specifier, which matches the second audio stream. Therefore, it
would select the ac3 codec for the second audio stream.

A stream specifier can match several streams, so that the option is applied to all
of them. E.g. the stream specifier in C<-b:a 128k> matches all audio
streams.

An empty stream specifier matches all streams. For example, C<-codec copy>
or C<-codec: copy> would copy all the streams without reencoding.

Possible forms of stream specifiers are:

=over 4


=item I<stream_index>

Matches the stream with this index. E.g. C<-threads:1 4> would set the
thread count for the second stream to 4. If I<stream_index> is used as an
additional stream specifier (see below), then it selects stream number
I<stream_index> from the matching streams. Stream numbering is based on the
order of the streams as detected by libavformat except when a stream group
specifier or program ID is also specified. In this case it is based on the
ordering of the streams in the group or program.

=item I<stream_type>B<[:>I<additional_stream_specifier>B<]>

I<stream_type> is one of following: 'v' or 'V' for video, 'a' for audio, 's'
for subtitle, 'd' for data, and 't' for attachments. 'v' matches all video
streams, 'V' only matches video streams which are not attached pictures, video
thumbnails or cover arts. If I<additional_stream_specifier> is used, then
it matches streams which both have this type and match the
I<additional_stream_specifier>. Otherwise, it matches all streams of the
specified type.

=item B<g:>I<group_specifier>B<[:>I<additional_stream_specifier>B<]>

Matches streams which are in the group with the specifier I<group_specifier>.
if I<additional_stream_specifier> is used, then it matches streams which both
are part of the group and match the I<additional_stream_specifier>.
I<group_specifier> may be one of the following:

=over 4


=item I<group_index>

Match the stream with this group index.

=item B<#>I<group_id> B<or i:>I<group_id>

Match the stream with this group id.

=back


=item B<p:>I<program_id>B<[:>I<additional_stream_specifier>B<]>

Matches streams which are in the program with the id I<program_id>. If
I<additional_stream_specifier> is used, then it matches streams which both
are part of the program and match the I<additional_stream_specifier>.


=item B<#>I<stream_id> B<or i:>I<stream_id>

Match the stream by stream id (e.g. PID in MPEG-TS container).

=item B<m:>I<key>B<[:>I<value>B<]>

Matches streams with the metadata tag I<key> having the specified value. If
I<value> is not given, matches streams that contain the given tag with any
value. The colon character ':' in I<key> or I<value> needs to be
backslash-escaped.

=item B<disp:>I<dispositions>B<[:>I<additional_stream_specifier>B<]>

Matches streams with the given disposition(s). I<dispositions> is a list of
one or more dispositions (as printed by the B<-dispositions> option)
joined with '+'.

=item B<u>

Matches streams with usable configuration, the codec must be defined and the
essential information such as video dimension or audio sample rate must be present.

Note that in B<ffmpeg>, matching by metadata will only work properly for
input files.

=back



=head2 Generic options


These options are shared amongst the ff* tools.


=over 4



=item B<-L>

Show license.


=item B<-h, -?, -help, --help [>I<arg>B<]>

Show help. An optional parameter may be specified to print help about a specific
item. If no argument is specified, only basic (non advanced) tool
options are shown.

Possible values of I<arg> are:

=over 4


=item B<long>

Print advanced tool options in addition to the basic tool options.


=item B<full>

Print complete list of options, including shared and private options
for encoders, decoders, demuxers, muxers, filters, etc.


=item B<decoder=>I<decoder_name>

Print detailed information about the decoder named I<decoder_name>. Use the
B<-decoders> option to get a list of all decoders.


=item B<encoder=>I<encoder_name>

Print detailed information about the encoder named I<encoder_name>. Use the
B<-encoders> option to get a list of all encoders.


=item B<demuxer=>I<demuxer_name>

Print detailed information about the demuxer named I<demuxer_name>. Use the
B<-formats> option to get a list of all demuxers and muxers.


=item B<muxer=>I<muxer_name>

Print detailed information about the muxer named I<muxer_name>. Use the
B<-formats> option to get a list of all muxers and demuxers.


=item B<filter=>I<filter_name>

Print detailed information about the filter named I<filter_name>. Use the
B<-filters> option to get a list of all filters.


=item B<bsf=>I<bitstream_filter_name>

Print detailed information about the bitstream filter named I<bitstream_filter_name>.
Use the B<-bsfs> option to get a list of all bitstream filters.


=item B<protocol=>I<protocol_name>

Print detailed information about the protocol named I<protocol_name>.
Use the B<-protocols> option to get a list of all protocols.

=back



=item B<-version>

Show version.


=item B<-buildconf>

Show the build configuration, one option per line.


=item B<-formats>

Show available formats (including devices).


=item B<-demuxers>

Show available demuxers.


=item B<-muxers>

Show available muxers.


=item B<-devices>

Show available devices.


=item B<-codecs>

Show all codecs known to libavcodec.

Note that the term 'codec' is used throughout this documentation as a shortcut
for what is more correctly called a media bitstream format.


=item B<-decoders>

Show available decoders.


=item B<-encoders>

Show all available encoders.


=item B<-bsfs>

Show available bitstream filters.


=item B<-protocols>

Show available protocols.


=item B<-filters>

Show available libavfilter filters.


=item B<-pix_fmts>

Show available pixel formats.


=item B<-sample_fmts>

Show available sample formats.


=item B<-layouts>

Show channel names and standard channel layouts.


=item B<-dispositions>

Show stream dispositions.


=item B<-colors>

Show recognized color names.


=item B<-sources> I<device>B<[,>I<opt1>B<=>I<val1>B<[,>I<opt2>B<=>I<val2>B<]...]>

Show autodetected sources of the input device.
Some devices may provide system-dependent source names that cannot be autodetected.
The returned list cannot be assumed to be always complete.
	
	ffmpeg -sources pulse,server=192.168.0.4



=item B<-sinks> I<device>B<[,>I<opt1>B<=>I<val1>B<[,>I<opt2>B<=>I<val2>B<]...]>

Show autodetected sinks of the output device.
Some devices may provide system-dependent sink names that cannot be autodetected.
The returned list cannot be assumed to be always complete.
	
	ffmpeg -sinks pulse,server=192.168.0.4



=item B<-loglevel [>I<flags>B<+]>I<loglevel> B<| -v [>I<flags>B<+]>I<loglevel>

Set logging level and flags used by the library.

The optional I<flags> prefix can consist of the following values:

=over 4


=item B<repeat>

Indicates that repeated log output should not be compressed to the first line
and the "Last message repeated n times" line will be omitted.

=item B<level>

Indicates that log output should add a C<[level]> prefix to each message
line. This can be used as an alternative to log coloring, e.g. when dumping the
log to file.

=item B<time>

Indicates that log lines should be prefixed with time information.

=item B<datetime>

Indicates that log lines should be prefixed with date and time information.

=back

Flags can also be used alone by adding a '+'/'-' prefix to set/reset a single
flag without affecting other I<flags> or changing I<loglevel>. When
setting both I<flags> and I<loglevel>, a '+' separator is expected
between the last I<flags> value and before I<loglevel>.

I<loglevel> is a string or a number containing one of the following values:

=over 4


=item B<quiet, -8>

Show nothing at all; be silent.

=item B<panic, 0>

Only show fatal errors which could lead the process to crash, such as
an assertion failure. This is not currently used for anything.

=item B<fatal, 8>

Only show fatal errors. These are errors after which the process absolutely
cannot continue.

=item B<error, 16>

Show all errors, including ones which can be recovered from.

=item B<warning, 24>

Show all warnings and errors. Any message related to possibly
incorrect or unexpected events will be shown.

=item B<info, 32>

Show informative messages during processing. This is in addition to
warnings and errors. This is the default value.

=item B<verbose, 40>

Same as C<info>, except more verbose.

=item B<debug, 48>

Show everything, including debugging information.

=item B<trace, 56>


=back


For example to enable repeated log output, add the C<level> prefix, and set
I<loglevel> to C<verbose>:
	
	ffmpeg -loglevel repeat+level+verbose -i input output

Another example that enables repeated log output without affecting current
state of C<level> prefix flag or I<loglevel>:
	
	ffmpeg [...] -loglevel +repeat


By default the program logs to stderr. If coloring is supported by the
terminal, colors are used to mark errors and warnings. Log coloring
can be disabled setting the environment variable
B<AV_LOG_FORCE_NOCOLOR>, or can be forced setting
the environment variable B<AV_LOG_FORCE_COLOR>.


=item B<-report>

Dump full command line and log output to a file named
C<I<program>-I<YYYYMMDD>-I<HHMMSS>.log> in the current
directory.
This file can be useful for bug reports.
It also implies C<-loglevel debug>.

Setting the environment variable B<FFREPORT> to any value has the
same effect. If the value is a ':'-separated key=value sequence, these
options will affect the report; option values must be escaped if they
contain special characters or the options delimiter ':' (see the
``Quoting and escaping'' section in the ffmpeg-utils manual).

The following options are recognized:

=over 4


=item B<file>

set the file name to use for the report; C<%p> is expanded to the name
of the program, C<%t> is expanded to a timestamp, C<%%> is expanded
to a plain C<%>

=item B<level>

set the log verbosity level using a numerical value (see C<-loglevel>).

=back


For example, to output a report to a file named F<ffreport.log>
using a log level of C<32> (alias for log level C<info>):

	
	FFREPORT=file=ffreport.log:level=32 ffmpeg -i input output


Errors in parsing the environment variable are not fatal, and will not
appear in the report.


=item B<-hide_banner>

Suppress printing banner.

All FFmpeg tools will normally show a copyright notice, build options
and library versions. This option can be used to suppress printing
this information.


=item B<-cpuflags flags (>I<global>B<)>

Allows setting and clearing cpu flags. This option is intended
for testing. Do not use it unless you know what you're doing.
	
	ffmpeg -cpuflags -sse+mmx ...
	ffmpeg -cpuflags mmx ...
	ffmpeg -cpuflags 0 ...

Possible flags for this option are:

=over 4


=item B<x86>


=over 4


=item B<mmx>


=item B<mmxext>


=item B<sse>


=item B<sse2>


=item B<sse2slow>


=item B<sse3>


=item B<sse3slow>


=item B<ssse3>


=item B<atom>


=item B<sse4.1>


=item B<sse4.2>


=item B<avx>


=item B<avx2>


=item B<xop>


=item B<fma3>


=item B<fma4>


=item B<3dnow>


=item B<3dnowext>


=item B<bmi1>


=item B<bmi2>


=item B<cmov>


=back


=item B<ARM>


=over 4


=item B<armv5te>


=item B<armv6>


=item B<armv6t2>


=item B<vfp>


=item B<vfpv3>


=item B<neon>


=item B<setend>


=back


=item B<AArch64>


=over 4


=item B<armv8>


=item B<vfp>


=item B<neon>


=back


=item B<PowerPC>


=over 4


=item B<altivec>


=back


=item B<Specific Processors>


=over 4


=item B<pentium2>


=item B<pentium3>


=item B<pentium4>


=item B<k6>


=item B<k62>


=item B<athlon>


=item B<athlonxp>


=item B<k8>


=back


=back



=item B<-cpucount> I<count> B<(>I<global>B<)>

Override detection of CPU count. This option is intended
for testing. Do not use it unless you know what you're doing.
	
	ffmpeg -cpucount 2



=item B<-max_alloc> I<bytes>

Set the maximum size limit for allocating a block on the heap by ffmpeg's
family of malloc functions. Exercise B<extreme caution> when using
this option. Don't use if you do not understand the full consequence of doing so.
Default is INT_MAX.

=back



=head2 AVOptions


These options are provided directly by the libavformat, libavdevice and
libavcodec libraries. To see the list of available AVOptions, use the
B<-help> option. They are separated into two categories:

=over 4


=item B<generic>

These options can be set for any container, codec or device. Generic options
are listed under AVFormatContext options for containers/devices and under
AVCodecContext options for codecs.

=item B<private>

These options are specific to the given container, device or codec. Private
options are listed under their corresponding containers/devices/codecs.

=back


For example to write an ID3v2.3 header instead of a default ID3v2.4 to
an MP3 file, use the B<id3v2_version> private option of the MP3
muxer:
	
	ffmpeg -i input.flac -id3v2_version 3 out.mp3


All codec AVOptions are per-stream, and thus a stream specifier
should be attached to them:
	
	ffmpeg -i multichannel.mxf -map 0:v:0 -map 0:a:0 -map 0:a:0 -c:a:0 ac3 -b:a:0 640k -ac:a:1 2 -c:a:1 aac -b:2 128k out.mp4


In the above example, a multichannel audio stream is mapped twice for output.
The first instance is encoded with codec ac3 and bitrate 640k.
The second instance is downmixed to 2 channels and encoded with codec aac. A bitrate of 128k is specified for it using
absolute index of the output stream.

Note: the B<-nooption> syntax cannot be used for boolean
AVOptions, use B<-option 0>/B<-option 1>.

Note: the old undocumented way of specifying per-stream AVOptions by
prepending v/a/s to the options name is now obsolete and will be
removed soon.


=head2 Main options



=over 4



=item B<-f> I<fmt> B<(>I<input/output>B<)>

Force input or output file format. The format is normally auto detected for input
files and guessed from the file extension for output files, so this option is not
needed in most cases.


=item B<-i> I<url> B<(>I<input>B<)>

input file url


=item B<-y (>I<global>B<)>

Overwrite output files without asking.


=item B<-n (>I<global>B<)>

Do not overwrite output files, and exit immediately if a specified
output file already exists.


=item B<-stream_loop> I<number> B<(>I<input>B<)>

Set number of times input stream shall be looped. Loop 0 means no loop,
loop -1 means infinite loop.


=item B<-recast_media (>I<global>B<)>

Allow forcing a decoder of a different media type than the one
detected or designated by the demuxer. Useful for decoding media
data muxed as data streams.


=item B<-c[:>I<stream_specifier>B<]> I<codec> B<(>I<input/output,per-stream>B<)>


=item B<-codec[:>I<stream_specifier>B<]> I<codec> B<(>I<input/output,per-stream>B<)>

Select an encoder (when used before an output file) or a decoder (when used
before an input file) for one or more streams. I<codec> is the name of a
decoder/encoder or a special value C<copy> (output only) to indicate that
the stream is not to be re-encoded.

For example
	
	ffmpeg -i INPUT -map 0 -c:v libx264 -c:a copy OUTPUT

encodes all video streams with libx264 and copies all audio streams.

For each stream, the last matching C<c> option is applied, so
	
	ffmpeg -i INPUT -map 0 -c copy -c:v:1 libx264 -c:a:137 libvorbis OUTPUT

will copy all the streams except the second video, which will be encoded with
libx264, and the 138th audio, which will be encoded with libvorbis.


=item B<-t> I<duration> B<(>I<input/output>B<)>

When used as an input option (before C<-i>), limit the I<duration> of
data read from the input file.

When used as an output option (before an output url), stop writing the
output after its duration reaches I<duration>.

I<duration> must be a time duration specification,
see B<the Time duration section in the ffmpeg-utils(1) manual>.

-to and -t are mutually exclusive and -t has priority.


=item B<-to> I<position> B<(>I<input/output>B<)>

Stop writing the output or reading the input at I<position>.
I<position> must be a time duration specification,
see B<the Time duration section in the ffmpeg-utils(1) manual>.

-to and -t are mutually exclusive and -t has priority.


=item B<-fs> I<limit_size> B<(>I<output>B<)>

Set the file size limit, expressed in bytes. No further chunk of bytes is written
after the limit is exceeded. The size of the output file is slightly more than the
requested file size.


=item B<-ss> I<position> B<(>I<input/output>B<)>

When used as an input option (before C<-i>), seeks in this input file to
I<position>. Note that in most formats it is not possible to seek exactly,
so B<ffmpeg> will seek to the closest seek point before I<position>.
When transcoding and B<-accurate_seek> is enabled (the default), this
extra segment between the seek point and I<position> will be decoded and
discarded. When doing stream copy or when B<-noaccurate_seek> is used, it
will be preserved.

When used as an output option (before an output url), decodes but discards
input until the timestamps reach I<position>.

I<position> must be a time duration specification,
see B<the Time duration section in the ffmpeg-utils(1) manual>.


=item B<-sseof> I<position> B<(>I<input>B<)>


Like the C<-ss> option but relative to the "end of file". That is negative
values are earlier in the file, 0 is at EOF.


=item B<-isync> I<input_index> B<(>I<input>B<)>

Assign an input as a sync source.

This will take the difference between the start times of the target and reference inputs and
offset the timestamps of the target file by that difference. The source timestamps of the two
inputs should derive from the same clock source for expected results. If C<copyts> is set
then C<start_at_zero> must also be set. If either of the inputs has no starting timestamp
then no sync adjustment is made.

Acceptable values are those that refer to a valid ffmpeg input index. If the sync reference is
the target index itself or I<-1>, then no adjustment is made to target timestamps. A sync
reference may not itself be synced to any other input.

Default value is I<-1>.


=item B<-itsoffset> I<offset> B<(>I<input>B<)>

Set the input time offset.

I<offset> must be a time duration specification,
see B<the Time duration section in the ffmpeg-utils(1) manual>.

The offset is added to the timestamps of the input files. Specifying
a positive offset means that the corresponding streams are delayed by
the time duration specified in I<offset>.


=item B<-itsscale> I<scale> B<(>I<input,per-stream>B<)>

Rescale input timestamps. I<scale> should be a floating point number.


=item B<-timestamp> I<date> B<(>I<output>B<)>

Set the recording timestamp in the container.

I<date> must be a date specification,
see B<the Date section in the ffmpeg-utils(1) manual>.


=item B<-metadata[:metadata_specifier]> I<key>B<=>I<value> B<(>I<output,per-metadata>B<)>

Set a metadata key/value pair.

An optional I<metadata_specifier> may be given to set metadata
on streams, chapters or programs. See C<-map_metadata>
documentation for details.

This option overrides metadata set with C<-map_metadata>. It is
also possible to delete metadata by using an empty value.

For example, for setting the title in the output file:
	
	ffmpeg -i in.avi -metadata title="my title" out.flv


To set the language of the first audio stream:
	
	ffmpeg -i INPUT -metadata:s:a:0 language=eng OUTPUT



=item B<-disposition[:stream_specifier]> I<value> B<(>I<output,per-stream>B<)>

Sets the disposition flags for a stream.

Default value: by default, all disposition flags are copied from the input stream,
unless the output stream this option applies to is fed by a complex filtergraph
- in that case no disposition flags are set by default.

I<value> is a sequence of disposition flags separated by '+' or '-'. A '+'
prefix adds the given disposition, '-' removes it. If the first flag is also
prefixed with '+' or '-', the resulting disposition is the default value
updated by I<value>. If the first flag is not prefixed, the resulting
disposition is I<value>. It is also possible to clear the disposition by
setting it to 0.

If no C<-disposition> options were specified for an output file, ffmpeg will
automatically set the 'default' disposition flag on the first stream of each type,
when there are multiple streams of this type in the output file and no stream of
that type is already marked as default.

The C<-dispositions> option lists the known disposition flags.

For example, to make the second audio stream the default stream:
	
	ffmpeg -i in.mkv -c copy -disposition:a:1 default out.mkv


To make the second subtitle stream the default stream and remove the default
disposition from the first subtitle stream:
	
	ffmpeg -i in.mkv -c copy -disposition:s:0 0 -disposition:s:1 default out.mkv


To add an embedded cover/thumbnail:
	
	ffmpeg -i in.mp4 -i IMAGE -map 0 -map 1 -c copy -c:v:1 png -disposition:v:1 attached_pic out.mp4


To add the 'original' and remove the 'comment' disposition flag from the first
audio stream without removing its other disposition flags:
	
	ffmpeg -i in.mkv -c copy -disposition:a:0 +original-comment out.mkv


To remove the 'original' and add the 'comment' disposition flag to the first
audio stream without removing its other disposition flags:
	
	ffmpeg -i in.mkv -c copy -disposition:a:0 -original+comment out.mkv


To set only the 'original' and 'comment' disposition flags on the first audio
stream (and remove its other disposition flags):
	
	ffmpeg -i in.mkv -c copy -disposition:a:0 original+comment out.mkv


To remove all disposition flags from the first audio stream:
	
	ffmpeg -i in.mkv -c copy -disposition:a:0 0 out.mkv


Not all muxers support embedded thumbnails, and those who do, only support a few formats, like JPEG or PNG.


=item B<-program [title=>I<title>B<:][program_num=>I<program_num>B<:]st=>I<stream>B<[:st=>I<stream>B<...] (>I<output>B<)>


Creates a program with the specified I<title>, I<program_num> and adds the specified
I<stream>(s) to it.


=item B<-stream_group [map=>I<input_file_id>B<=>I<stream_group>B<][type=>I<type>B<:]st=>I<stream>B<[:st=>I<stream>B<][:stg=>I<stream_group>B<][:id=>I<stream_group_id>B<...] (>I<output>B<)>


Creates a stream group of the specified I<type> and I<stream_group_id>, or by
I<map>ping an input group, adding the specified I<stream>(s) and/or previously
defined I<stream_group>(s) to it.

I<type> can be one of the following:

=over 4



=item B<iamf_audio_element>

Groups I<stream>s that belong to the same IAMF Audio Element

For this group I<type>, the following options are available

=over 4


=item B<audio_element_type>

The Audio Element type. The following values are supported:


=over 4


=item B<channel>

Scalable channel audio representation

=item B<scene>

Ambisonics representation

=back



=item B<demixing>

Demixing information used to reconstruct a scalable channel audio representation.
This option must be separated from the rest with a ',', and takes the following
key=value options


=over 4


=item B<parameter_id>

An identifier parameters blocks in frames may refer to

=item B<dmixp_mode>

A pre-defined combination of demixing parameters

=back



=item B<recon_gain>

Recon gain information used to reconstruct a scalable channel audio representation.
This option must be separated from the rest with a ',', and takes the following
key=value options


=over 4


=item B<parameter_id>

An identifier parameters blocks in frames may refer to

=back



=item B<layer>

A layer defining a Channel Layout in the Audio Element.
This option must be separated from the rest with a ','. Several ',' separated entries
can be defined, and at least one must be set.

It takes the following ":"-separated key=value options


=over 4


=item B<ch_layout>

The layer's channel layout

=item B<flags>

The following flags are available:


=over 4


=item B<recon_gain>

Whether to signal if recon_gain is present as metadata in parameter blocks within frames

=back



=item B<output_gain>


=item B<output_gain_flags>

Which channels output_gain applies to. The following flags are available:


=over 4


=item B<FL>


=item B<FR>


=item B<BL>


=item B<BR>


=item B<TFL>


=item B<TFR>


=back



=item B<ambisonics_mode>

The ambisonics mode. This has no effect if audio_element_type is set to channel.

The following values are supported:


=over 4


=item B<mono>

Each ambisonics channel is coded as an individual mono stream in the group

=back



=back



=item B<default_w>

Default weight value


=back



=item B<iamf_mix_presentation>

Groups I<stream>s that belong to all IAMF Audio Element the same
IAMF Mix Presentation references

For this group I<type>, the following options are available


=over 4


=item B<submix>

A sub-mix within the Mix Presentation.
This option must be separated from the rest with a ','. Several ',' separated entries
can be defined, and at least one must be set.

It takes the following ":"-separated key=value options


=over 4


=item B<parameter_id>

An identifier parameters blocks in frames may refer to, for post-processing the mixed
audio signal to generate the audio signal for playback

=item B<parameter_rate>

The sample rate duration fields in parameters blocks in frames that refer to this
I<parameter_id> are expressed as

=item B<default_mix_gain>

Default mix gain value to apply when there are no parameter blocks sharing the same
I<parameter_id> for a given frame


=item B<element>

References an Audio Element used in this Mix Presentation to generate the final output
audio signal for playback.
This option must be separated from the rest with a '|'. Several '|' separated entries
can be defined, and at least one must be set.

It takes the following ":"-separated key=value options:


=over 4


=item B<stg>

The I<stream_group_id> for an Audio Element which this sub-mix refers to

=item B<parameter_id>

An identifier parameters blocks in frames may refer to, for applying any processing to
the referenced and rendered Audio Element before being summed with other processed Audio
Elements

=item B<parameter_rate>

The sample rate duration fields in parameters blocks in frames that refer to this
I<parameter_id> are expressed as

=item B<default_mix_gain>

Default mix gain value to apply when there are no parameter blocks sharing the same
I<parameter_id> for a given frame

=item B<annotations>

A key=value string describing the sub-mix element where "key" is a string conforming to
BCP-47 that specifies the language for the "value" string. "key" must be the same as the
one in the mix's I<annotations>

=item B<headphones_rendering_mode>

Indicates whether the input channel-based Audio Element is rendered to stereo loudspeakers
or spatialized with a binaural renderer when played back on headphones.
This has no effect if the referenced Audio Element's I<audio_element_type> is set to
channel.

The following values are supported:


=over 4


=item B<stereo>


=item B<binaural>


=back



=back



=item B<layout>

Specifies the layouts for this sub-mix on which the loudness information was measured.
This option must be separated from the rest with a '|'. Several '|' separated entries
can be defined, and at least one must be set.

It takes the following ":"-separated key=value options:


=over 4


=item B<layout_type>



=over 4


=item B<loudspeakers>

The layout follows the loudspeaker sound system convention of ITU-2051-3.

=item B<binaural>

The layout is binaural.

=back



=item B<sound_system>

Channel layout matching one of Sound Systems A to J of ITU-2051-3, plus 7.1.2 and 3.1.2
This has no effect if I<layout_type> is set to binaural.

=item B<integrated_loudness>

The program integrated loudness information, as defined in ITU-1770-4.

=item B<digital_peak>

The digital (sampled) peak value of the audio signal, as defined in ITU-1770-4.

=item B<true_peak>

The true peak of the audio signal, as defined in ITU-1770-4.

=item B<dialog_anchored_loudness>

The Dialogue loudness information, as defined in ITU-1770-4.

=item B<album_anchored_loudness>

The Album loudness information, as defined in ITU-1770-4.

=back



=back



=item B<annotations>

A key=value string string describing the mix where "key" is a string conforming to BCP-47
that specifies the language for the "value" string. "key" must be the same as the ones in
all sub-mix element's I<annotations>s

=back



=back


E.g. to create an scalable 5.1 IAMF file from several WAV input files
	
	ffmpeg -i front.wav -i back.wav -i center.wav -i lfe.wav
	-map 0:0 -map 1:0 -map 2:0 -map 3:0 -c:a opus
	-stream_group type=iamf_audio_element:id=1:st=0:st=1:st=2:st=3,
	demixing=parameter_id=998,
	recon_gain=parameter_id=101,
	layer=ch_layout=stereo,
	layer=ch_layout=5.1(side),
	-stream_group type=iamf_mix_presentation:id=2:stg=0:annotations=en-us=Mix_Presentation,
	submix=parameter_id=100:parameter_rate=48000|element=stg=0:parameter_id=100:annotations=en-us=Scalable_Submix|layout=sound_system=stereo|layout=sound_system=5.1(side)
	-streamid 0:0 -streamid 1:1 -streamid 2:2 -streamid 3:3 output.iamf


To copy the two stream groups (Audio Element and Mix Presentation) from an input IAMF file with four
streams into an mp4 output
	
	ffmpeg -i input.iamf -c:a copy -stream_group map=0=0:st=0:st=1:st=2:st=3 -stream_group map=0=1:stg=0
	-streamid 0:0 -streamid 1:1 -streamid 2:2 -streamid 3:3 output.mp4



=item B<-target> I<type> B<(>I<output>B<)>

Specify target file type (C<vcd>, C<svcd>, C<dvd>, C<dv>,
C<dv50>). I<type> may be prefixed with C<pal->, C<ntsc-> or
C<film-> to use the corresponding standard. All the format options
(bitrate, codecs, buffer sizes) are then set automatically. You can just type:

	
	ffmpeg -i myfile.avi -target vcd /tmp/vcd.mpg


Nevertheless you can specify additional options as long as you know
they do not conflict with the standard, as in:

	
	ffmpeg -i myfile.avi -target vcd -bf 2 /tmp/vcd.mpg


The parameters set for each target are as follows.

B<VCD>
	
	<pal>:
	-f vcd -muxrate 1411200 -muxpreload 0.44 -packetsize 2324
	-s 352x288 -r 25
	-codec:v mpeg1video -g 15 -b:v 1150k -maxrate:v 1150k -minrate:v 1150k -bufsize:v 327680
	-ar 44100 -ac 2
	-codec:a mp2 -b:a 224k
	
	<ntsc>:
	-f vcd -muxrate 1411200 -muxpreload 0.44 -packetsize 2324
	-s 352x240 -r 30000/1001
	-codec:v mpeg1video -g 18 -b:v 1150k -maxrate:v 1150k -minrate:v 1150k -bufsize:v 327680
	-ar 44100 -ac 2
	-codec:a mp2 -b:a 224k
	
	<film>:
	-f vcd -muxrate 1411200 -muxpreload 0.44 -packetsize 2324
	-s 352x240 -r 24000/1001
	-codec:v mpeg1video -g 18 -b:v 1150k -maxrate:v 1150k -minrate:v 1150k -bufsize:v 327680
	-ar 44100 -ac 2
	-codec:a mp2 -b:a 224k


B<SVCD>
	
	<pal>:
	-f svcd -packetsize 2324
	-s 480x576 -pix_fmt yuv420p -r 25
	-codec:v mpeg2video -g 15 -b:v 2040k -maxrate:v 2516k -minrate:v 0 -bufsize:v 1835008 -scan_offset 1
	-ar 44100
	-codec:a mp2 -b:a 224k
	
	<ntsc>:
	-f svcd -packetsize 2324
	-s 480x480 -pix_fmt yuv420p -r 30000/1001
	-codec:v mpeg2video -g 18 -b:v 2040k -maxrate:v 2516k -minrate:v 0 -bufsize:v 1835008 -scan_offset 1
	-ar 44100
	-codec:a mp2 -b:a 224k
	
	<film>:
	-f svcd -packetsize 2324
	-s 480x480 -pix_fmt yuv420p -r 24000/1001
	-codec:v mpeg2video -g 18 -b:v 2040k -maxrate:v 2516k -minrate:v 0 -bufsize:v 1835008 -scan_offset 1
	-ar 44100
	-codec:a mp2 -b:a 224k


B<DVD>
	
	<pal>:
	-f dvd -muxrate 10080k -packetsize 2048
	-s 720x576 -pix_fmt yuv420p -r 25
	-codec:v mpeg2video -g 15 -b:v 6000k -maxrate:v 9000k -minrate:v 0 -bufsize:v 1835008
	-ar 48000
	-codec:a ac3 -b:a 448k
	
	<ntsc>:
	-f dvd -muxrate 10080k -packetsize 2048
	-s 720x480 -pix_fmt yuv420p -r 30000/1001
	-codec:v mpeg2video -g 18 -b:v 6000k -maxrate:v 9000k -minrate:v 0 -bufsize:v 1835008
	-ar 48000
	-codec:a ac3 -b:a 448k
	
	<film>:
	-f dvd -muxrate 10080k -packetsize 2048
	-s 720x480 -pix_fmt yuv420p -r 24000/1001
	-codec:v mpeg2video -g 18 -b:v 6000k -maxrate:v 9000k -minrate:v 0 -bufsize:v 1835008
	-ar 48000
	-codec:a ac3 -b:a 448k


B<DV>
	
	<pal>:
	-f dv
	-s 720x576 -pix_fmt yuv420p -r 25
	-ar 48000 -ac 2
	
	<ntsc>:
	-f dv
	-s 720x480 -pix_fmt yuv411p -r 30000/1001
	-ar 48000 -ac 2
	
	<film>:
	-f dv
	-s 720x480 -pix_fmt yuv411p -r 24000/1001
	-ar 48000 -ac 2

The C<dv50> target is identical to the C<dv> target except that the pixel format set is C<yuv422p> for all three standards.

Any user-set value for a parameter above will override the target preset value. In that case, the output may
not comply with the target standard.


=item B<-dn (>I<input/output>B<)>

As an input option, blocks all data streams of a file from being filtered or
being automatically selected or mapped for any output. See C<-discard>
option to disable streams individually.

As an output option, disables data recording i.e. automatic selection or
mapping of any data stream. For full manual control see the C<-map>
option.


=item B<-dframes> I<number> B<(>I<output>B<)>

Set the number of data frames to output. This is an obsolete alias for
C<-frames:d>, which you should use instead.


=item B<-frames[:>I<stream_specifier>B<]> I<framecount> B<(>I<output,per-stream>B<)>

Stop writing to the stream after I<framecount> frames.


=item B<-q[:>I<stream_specifier>B<]> I<q> B<(>I<output,per-stream>B<)>


=item B<-qscale[:>I<stream_specifier>B<]> I<q> B<(>I<output,per-stream>B<)>

Use fixed quality scale (VBR). The meaning of I<q>/I<qscale> is
codec-dependent.
If I<qscale> is used without a I<stream_specifier> then it applies only
to the video stream, this is to maintain compatibility with previous behavior
and as specifying the same codec specific value to 2 different codecs that is
audio and video generally is not what is intended when no stream_specifier is
used.



=item B<-filter[:>I<stream_specifier>B<]> I<filtergraph> B<(>I<output,per-stream>B<)>

Create the filtergraph specified by I<filtergraph> and use it to
filter the stream.

I<filtergraph> is a description of the filtergraph to apply to
the stream, and must have a single input and a single output of the
same type of the stream. In the filtergraph, the input is associated
to the label C<in>, and the output to the label C<out>. See
the ffmpeg-filters manual for more information about the filtergraph
syntax.

See the B<-filter_complex option> if you
want to create filtergraphs with multiple inputs and/or outputs.


=item B<-reinit_filter[:>I<stream_specifier>B<]> I<integer> B<(>I<input,per-stream>B<)>

This boolean option determines if the filtergraph(s) to which this stream is fed gets
reinitialized when input frame parameters change mid-stream. This option is enabled by
default as most video and all audio filters cannot handle deviation in input frame properties.
Upon reinitialization, existing filter state is lost, like e.g. the frame count C<n>
reference available in some filters. Any frames buffered at time of reinitialization are lost.
The properties where a change triggers reinitialization are,
for video, frame resolution or pixel format;
for audio, sample format, sample rate, channel count or channel layout.


=item B<-drop_changed[:>I<stream_specifier>B<]> I<integer> B<(>I<input,per-stream>B<)>

This boolean option determines whether a frame with differing frame parameters mid-stream
gets dropped instead of leading to filtergraph reinitialization, as that would lead to loss
of filter state. Generally useful to avoid corrupted yet decodable packets in live streaming
inputs. Default is false.


=item B<-filter_threads> I<nb_threads> B<(>I<global>B<)>

Defines how many threads are used to process a filter pipeline. Each pipeline
will produce a thread pool with this many threads available for parallel processing.
The default is the number of available CPUs.


=item B<-filter_buffered_frames> I<nb_frames> B<(>I<global>B<)>

Defines the maximum number of buffered frames allowed in a filtergraph. Under
normal circumstances, a filtergraph should not buffer more than a few frames,
especially if frames are being fed to it and read from it in a balanced way
(which is the intended behavior in ffmpeg). That said, this option allows you
to limit the total number of frames buffered across all links in a filtergraph.
If more frames are generated, filtering is aborted and an error is returned.
The default value is 0, which means no limit.


=item B<-pre[:>I<stream_specifier>B<]> I<preset_name> B<(>I<output,per-stream>B<)>

Specify the preset for matching stream(s).


=item B<-stats (>I<global>B<)>

Log encoding progress/statistics as "info"-level log (see C<-loglevel>).
It is on by default, to explicitly disable it you need to specify C<-nostats>.


=item B<-stats_period> I<time> B<(>I<global>B<)>

Set period at which encoding progress/statistics are updated. Default is 0.5 seconds.


=item B<-print_graphs (>I<global>B<)>

Prints execution graph details to stderr in the format set via -print_graphs_format.


=item B<-print_graphs_file> I<filename> B<(>I<global>B<)>

Writes execution graph details to the specified file in the format set via -print_graphs_format.


=item B<-print_graphs_format> I<format> B<(>I<global>B<)>

Sets the output format (available formats are: default, compact, csv, flat, ini, json, xml, mermaid, mermaidhtml)
The default format is json.


=item B<-progress> I<url> B<(>I<global>B<)>

Send program-friendly progress information to I<url>.

Progress information is written periodically and at the end of
the encoding process. It is made of "I<key>=I<value>" lines. I<key>
consists of only alphanumeric characters. The last key of a sequence of
progress information is always "progress" with the value "continue" or "end".

The update period is set using C<-stats_period>.

For example, log progress information to stdout:

	
	ffmpeg -progress pipe:1 -i in.mkv out.mkv




=item B<-stdin>

Enable interaction on standard input. On by default unless standard input is
used as an input. To explicitly disable interaction you need to specify
C<-nostdin>.

Disabling interaction on standard input is useful, for example, if
ffmpeg is in the background process group. Roughly the same result can
be achieved with C<ffmpeg ... E<lt> /dev/null> but it requires a
shell.


=item B<-debug_ts (>I<global>B<)>

Print timestamp/latency information. It is off by default. This option is
mostly useful for testing and debugging purposes, and the output
format may change from one version to another, so it should not be
employed by portable scripts.

See also the option C<-fdebug ts>.


=item B<-attach> I<filename> B<(>I<output>B<)>

Add an attachment to the output file. This is supported by a few formats
like Matroska for e.g. fonts used in rendering subtitles. Attachments
are implemented as a specific type of stream, so this option will add
a new stream to the file. It is then possible to use per-stream options
on this stream in the usual way. Attachment streams created with this
option will be created after all the other streams (i.e. those created
with C<-map> or automatic mappings).

Note that for Matroska you also have to set the mimetype metadata tag:
	
	ffmpeg -i INPUT -attach DejaVuSans.ttf -metadata:s:2 mimetype=application/x-truetype-font out.mkv

(assuming that the attachment stream will be third in the output file).


=item B<-dump_attachment[:>I<stream_specifier>B<]> I<filename> B<(>I<input,per-stream>B<)>

Extract the matching attachment stream into a file named I<filename>. If
I<filename> is empty, then the value of the C<filename> metadata tag
will be used.

E.g. to extract the first attachment to a file named 'out.ttf':
	
	ffmpeg -dump_attachment:t:0 out.ttf -i INPUT

To extract all attachments to files determined by the C<filename> tag:
	
	ffmpeg -dump_attachment:t "" -i INPUT


Technical note -- attachments are implemented as codec extradata, so this
option can actually be used to extract extradata from any stream, not just
attachments.

=back



=head2 Video Options



=over 4


=item B<-vframes> I<number> B<(>I<output>B<)>

Set the number of video frames to output. This is an obsolete alias for
C<-frames:v>, which you should use instead.

=item B<-r[:>I<stream_specifier>B<]> I<fps> B<(>I<input/output,per-stream>B<)>

Set frame rate (Hz value, fraction or abbreviation).

As an input option, ignore any timestamps stored in the file and instead
generate timestamps assuming constant frame rate I<fps>.
This is not the same as the B<-framerate> option used for some input formats
like image2 or v4l2 (it used to be the same in older versions of FFmpeg).
If in doubt use B<-framerate> instead of the input option B<-r>.

As an output option:

=over 4


=item B<video encoding>

Duplicate or drop frames right before encoding them to achieve constant output
frame rate I<fps>.


=item B<video streamcopy>

Indicate to the muxer that I<fps> is the stream frame rate. No data is
dropped or duplicated in this case. This may produce invalid files if I<fps>
does not match the actual stream frame rate as determined by packet timestamps.
See also the C<setts> bitstream filter.


=back



=item B<-fpsmax[:>I<stream_specifier>B<]> I<fps> B<(>I<output,per-stream>B<)>

Set maximum frame rate (Hz value, fraction or abbreviation).

Clamps output frame rate when output framerate is auto-set and is higher than this value.
Useful in batch processing or when input framerate is wrongly detected as very high.
It cannot be set together with C<-r>. It is ignored during streamcopy.


=item B<-s[:>I<stream_specifier>B<]> I<size> B<(>I<input/output,per-stream>B<)>

Set frame size.

As an input option, this is a shortcut for the B<video_size> private
option, recognized by some demuxers for which the frame size is either not
stored in the file or is configurable -- e.g. raw video or video grabbers.

As an output option, this inserts the C<scale> video filter to the
I<end> of the corresponding filtergraph. Please use the C<scale> filter
directly to insert it at the beginning or some other place.

The format is B<wxh> (default - same as source).


=item B<-aspect[:>I<stream_specifier>B<]> I<aspect> B<(>I<output,per-stream>B<)>

Set the video display aspect ratio specified by I<aspect>.

I<aspect> can be a floating point number string, or a string of the
form I<num>:I<den>, where I<num> and I<den> are the
numerator and denominator of the aspect ratio. For example "4:3",
"16:9", "1.3333", and "1.7777" are valid argument values.

If used together with B<-vcodec copy>, it will affect the aspect ratio
stored at container level, but not the aspect ratio stored in encoded
frames, if it exists.


=item B<-display_rotation[:>I<stream_specifier>B<]> I<rotation> B<(>I<input,per-stream>B<)>

Set video rotation metadata.

I<rotation> is a decimal number specifying the amount in degree by
which the video should be rotated counter-clockwise before being
displayed.

This option overrides the rotation/display transform metadata stored in
the file, if any. When the video is being transcoded (rather than
copied) and C<-autorotate> is enabled, the video will be rotated at
the filtering stage. Otherwise, the metadata will be written into the
output file if the muxer supports it.

If the C<-display_hflip> and/or C<-display_vflip> options are
given, they are applied after the rotation specified by this option.


=item B<-display_hflip[:>I<stream_specifier>B<] (>I<input,per-stream>B<)>

Set whether on display the image should be horizontally flipped.

See the C<-display_rotation> option for more details.


=item B<-display_vflip[:>I<stream_specifier>B<] (>I<input,per-stream>B<)>

Set whether on display the image should be vertically flipped.

See the C<-display_rotation> option for more details.


=item B<-vn (>I<input/output>B<)>

As an input option, blocks all video streams of a file from being filtered or
being automatically selected or mapped for any output. See C<-discard>
option to disable streams individually.

As an output option, disables video recording i.e. automatic selection or
mapping of any video stream. For full manual control see the C<-map>
option.


=item B<-vcodec> I<codec> B<(>I<output>B<)>

Set the video codec. This is an alias for C<-codec:v>.


=item B<-pass[:>I<stream_specifier>B<]> I<n> B<(>I<output,per-stream>B<)>

Select the pass number (1 or 2). It is used to do two-pass
video encoding. The statistics of the video are recorded in the first
pass into a log file (see also the option -passlogfile),
and in the second pass that log file is used to generate the video
at the exact requested bitrate.
On pass 1, you may just deactivate audio and set output to null,
examples for Windows and Unix:
	
	ffmpeg -i foo.mov -c:v libxvid -pass 1 -an -f rawvideo -y NUL
	ffmpeg -i foo.mov -c:v libxvid -pass 1 -an -f rawvideo -y /dev/null



=item B<-passlogfile[:>I<stream_specifier>B<]> I<prefix> B<(>I<output,per-stream>B<)>

Set two-pass log file name prefix to I<prefix>, the default file name
prefix is ``ffmpeg2pass''. The complete file name will be
F<PREFIX-N.log>, where N is a number specific to the output
stream


=item B<-vf> I<filtergraph> B<(>I<output>B<)>

Create the filtergraph specified by I<filtergraph> and use it to
filter the stream.

This is an alias for C<-filter:v>, see the B<-filter option>.


=item B<-autorotate>

Automatically rotate the video according to file metadata. Enabled by
default, use B<-noautorotate> to disable it.


=item B<-autoscale>

Automatically scale the video according to the resolution of first frame.
Enabled by default, use B<-noautoscale> to disable it. When autoscale is
disabled, all output frames of filter graph might not be in the same resolution
and may be inadequate for some encoder/muxer. Therefore, it is not recommended
to disable it unless you really know what you are doing.
Disable autoscale at your own risk.

=back



=head2 Advanced Video options



=over 4


=item B<-pix_fmt[:>I<stream_specifier>B<]> I<format> B<(>I<input/output,per-stream>B<)>

Set pixel format. Use C<-pix_fmts> to show all the supported
pixel formats.
If the selected pixel format can not be selected, ffmpeg will print a
warning and select the best pixel format supported by the encoder.
If I<pix_fmt> is prefixed by a C<+>, ffmpeg will exit with an error
if the requested pixel format can not be selected, and automatic conversions
inside filtergraphs are disabled.
If I<pix_fmt> is a single C<+>, ffmpeg selects the same pixel format
as the input (or graph output) and automatic conversions are disabled.


=item B<-sws_flags> I<flags> B<(>I<input/output>B<)>

Set default flags for the libswscale library. These flags are used by
automatically inserted C<scale> filters and those within simple
filtergraphs, if not overridden within the filtergraph definition.

See the B<ffmpeg-scaler manual> for a list
of scaler options.


=item B<-rc_override[:>I<stream_specifier>B<]> I<override> B<(>I<output,per-stream>B<)>

Rate control override for specific intervals, formatted as "int,int,int"
list separated with slashes. Two first values are the beginning and
end frame numbers, last one is quantizer to use if positive, or quality
factor if negative.


=item B<-vstats>

Dump video coding statistics to F<vstats_HHMMSS.log>. See the
B<vstats file format> section for the format description.


=item B<-vstats_file> I<file>

Dump video coding statistics to I<file>. See the
B<vstats file format> section for the format description.


=item B<-vstats_version> I<file>

Specify which version of the vstats format to use. Default is C<2>. See the
B<vstats file format> section for the format description.


=item B<-vtag> I<fourcc/tag> B<(>I<output>B<)>

Force video tag/fourcc. This is an alias for C<-tag:v>.


=item B<-force_key_frames[:>I<stream_specifier>B<]> I<time>B<[,>I<time>B<...] (>I<output,per-stream>B<)>


=item B<-force_key_frames[:>I<stream_specifier>B<] expr:>I<expr> B<(>I<output,per-stream>B<)>


=item B<-force_key_frames[:>I<stream_specifier>B<] source (>I<output,per-stream>B<)>


I<force_key_frames> can take arguments of the following form:


=over 4



=item I<time>B<[,>I<time>B<...]>

If the argument consists of timestamps, ffmpeg will round the specified times to the nearest
output timestamp as per the encoder time base and force a keyframe at the first frame having
timestamp equal or greater than the computed timestamp. Note that if the encoder time base is too
coarse, then the keyframes may be forced on frames with timestamps lower than the specified time.
The default encoder time base is the inverse of the output framerate but may be set otherwise
via C<-enc_time_base>.

If one of the times is "C<chapters>[I<delta>]", it is expanded into
the time of the beginning of all chapters in the file, shifted by
I<delta>, expressed as a time in seconds.
This option can be useful to ensure that a seek point is present at a
chapter mark or any other designated place in the output file.

For example, to insert a key frame at 5 minutes, plus key frames 0.1 second
before the beginning of every chapter:
	
	-force_key_frames 0:05:00,chapters-0.1



=item B<expr:>I<expr>

If the argument is prefixed with C<expr:>, the string I<expr>
is interpreted like an expression and is evaluated for each frame. A
key frame is forced in case the evaluation is non-zero.

The expression in I<expr> can contain the following constants:

=over 4


=item B<n>

the number of current processed frame, starting from 0

=item B<n_forced>

the number of forced frames

=item B<prev_forced_n>

the number of the previous forced frame, it is C<NAN> when no
keyframe was forced yet

=item B<prev_forced_t>

the time of the previous forced frame, it is C<NAN> when no
keyframe was forced yet

=item B<t>

the time of the current processed frame

=back


For example to force a key frame every 5 seconds, you can specify:
	
	-force_key_frames expr:gte(t,n_forced*5)


To force a key frame 5 seconds after the time of the last forced one,
starting from second 13:
	
	-force_key_frames expr:if(isnan(prev_forced_t),gte(t,13),gte(t,prev_forced_t+5))



=item B<source>

If the argument is C<source>, ffmpeg will force a key frame if
the current frame being encoded is marked as a key frame in its source.
In cases where this particular source frame has to be dropped,
enforce the next available frame to become a key frame instead.


=back


Note that forcing too many keyframes is very harmful for the lookahead
algorithms of certain encoders: using fixed-GOP options or similar
would be more efficient.


=item B<-apply_cropping[:>I<stream_specifier>B<]> I<source> B<(>I<input,per-stream>B<)>

Automatically crop the video after decoding according to file metadata.
Default is I<all>.


=over 4


=item B<none (0)>

Don't apply any cropping metadata.

=item B<all (1)>

Apply both codec and container level croppping. This is the default mode.

=item B<codec (2)>

Apply codec level croppping.

=item B<container (3)>

Apply container level croppping.

=back



=item B<-copyinkf[:>I<stream_specifier>B<] (>I<output,per-stream>B<)>

When doing stream copy, copy also non-key frames found at the
beginning.


=item B<-init_hw_device> I<type>B<[=>I<name>B<][:>I<device>B<[,>I<key=value>B<...]]>

Initialise a new hardware device of type I<type> called I<name>, using the
given device parameters.
If no name is specified it will receive a default name of the form "I<type>%d".

The meaning of I<device> and the following arguments depends on the
device type:

=over 4



=item B<cuda>

I<device> is the number of the CUDA device.

The following options are recognized:

=over 4


=item B<primary_ctx>

If set to 1, uses the primary device context instead of creating a new one.

=back


Examples:

=over 4


=item I<-init_hw_device cuda:1>

Choose the second device on the system.


=item I<-init_hw_device cuda:0,primary_ctx=1>

Choose the first device and use the primary device context.

=back



=item B<dxva2>

I<device> is the number of the Direct3D 9 display adapter.


=item B<d3d11va>

I<device> is the number of the Direct3D 11 display adapter.
If not specified, it will attempt to use the default Direct3D 11 display adapter
or the first Direct3D 11 display adapter whose hardware VendorId is specified
by B<vendor_id>.

Examples:

=over 4


=item I<-init_hw_device d3d11va>

Create a d3d11va device on the default Direct3D 11 display adapter.


=item I<-init_hw_device d3d11va:1>

Create a d3d11va device on the Direct3D 11 display adapter specified by index 1.


=item I<-init_hw_device d3d11va:,vendor_id=0x8086>

Create a d3d11va device on the first Direct3D 11 display adapter whose hardware VendorId is 0x8086.

=back



=item B<vaapi>

I<device> is either an X11 display name, a DRM render node or a DirectX adapter index.
If not specified, it will attempt to open the default X11 display (I<$DISPLAY>)
and then the first DRM render node (I</dev/dri/renderD128>), or the default
DirectX adapter on Windows.

The following options are recognized:

=over 4


=item B<kernel_driver>

When I<device> is not specified, use this option to specify the name of the kernel
driver associated with the desired device. This option is available only when
the hardware acceleration method I<drm> and I<vaapi> are enabled.

=item B<vendor_id>

When I<device> and I<kernel_driver> are not specified, use this option to specify
the vendor id associated with the desired device. This option is available only when the
hardware acceleration method I<drm> and I<vaapi> are enabled and I<kernel_driver>
is not specified.

=back


Examples:

=over 4


=item I<-init_hw_device vaapi>

Create a vaapi device on the default device.


=item I<-init_hw_device vaapi:/dev/dri/renderD129>

Create a vaapi device on DRM render node F</dev/dri/renderD129>.


=item I<-init_hw_device vaapi:1>

Create a vaapi device on DirectX adapter 1.


=item I<-init_hw_device vaapi:,kernel_driver=i915>

Create a vaapi device on a device associated with kernel driver B<i915>.


=item I<-init_hw_device vaapi:,vendor_id=0x8086>

Create a vaapi device on a device associated with vendor id B<0x8086>.

=back



=item B<vdpau>

I<device> is an X11 display name.
If not specified, it will attempt to open the default X11 display (I<$DISPLAY>).


=item B<qsv>

I<device> selects a value in B<MFX_IMPL_*>. Allowed values are:

=over 4


=item B<auto>


=item B<sw>


=item B<hw>


=item B<auto_any>


=item B<hw_any>


=item B<hw2>


=item B<hw3>


=item B<hw4>


=back

If not specified, B<auto_any> is used.
(Note that it may be easier to achieve the desired result for QSV by creating the
platform-appropriate subdevice (B<dxva2> or B<d3d11va> or B<vaapi>) and then deriving a
QSV device from that.)

The following options are recognized:

=over 4


=item B<child_device>

Specify a DRM render node on Linux or DirectX adapter on Windows.

=item B<child_device_type>

Choose platform-appropriate subdevice type. On Windows B<d3d11va> is used
as default subdevice type when C<--enable-libvpl> is specified at configuration time,
B<dxva2> is used as default subdevice type when C<--enable-libmfx> is specified at
configuration time. On Linux user can use B<vaapi> only as subdevice type.

=back


Examples:

=over 4


=item I<-init_hw_device qsv:hw,child_device=/dev/dri/renderD129>

Create a QSV device with B<MFX_IMPL_HARDWARE> on DRM render node F</dev/dri/renderD129>.


=item I<-init_hw_device qsv:hw,child_device=1>

Create a QSV device with B<MFX_IMPL_HARDWARE> on DirectX adapter 1.


=item I<-init_hw_device qsv:hw,child_device_type=d3d11va>

Choose the GPU subdevice with type B<d3d11va> and create QSV device with B<MFX_IMPL_HARDWARE>.


=item I<-init_hw_device qsv:hw,child_device_type=dxva2>

Choose the GPU subdevice with type B<dxva2> and create QSV device with B<MFX_IMPL_HARDWARE>.


=item I<-init_hw_device qsv:hw,child_device=1,child_device_type=d3d11va>

Create a QSV device with B<MFX_IMPL_HARDWARE> on DirectX adapter 1 with subdevice type B<d3d11va>.


=item I<-init_hw_device vaapi=va:/dev/dri/renderD129 -init_hw_device qsv=hw1@I<va>>

Create a VAAPI device called B<va> on F</dev/dri/renderD129>, then derive a QSV device called B<hw1>
from device B<va>.


=back



=item B<opencl>

I<device> selects the platform and device as I<platform_index.device_index>.

The set of devices can also be filtered using the key-value pairs to find only
devices matching particular platform or device strings.

The strings usable as filters are:

=over 4


=item B<platform_profile>


=item B<platform_version>


=item B<platform_name>


=item B<platform_vendor>


=item B<platform_extensions>


=item B<device_name>


=item B<device_vendor>


=item B<driver_version>


=item B<device_version>


=item B<device_profile>


=item B<device_extensions>


=item B<device_type>


=back


The indices and filters must together uniquely select a device.

Examples:

=over 4


=item I<-init_hw_device opencl:0.1>

Choose the second device on the first platform.


=item I<-init_hw_device opencl:,device_name=Foo9000>

Choose the device with a name containing the string I<Foo9000>.


=item I<-init_hw_device opencl:1,device_type=gpu,device_extensions=cl_khr_fp16>

Choose the GPU device on the second platform supporting the I<cl_khr_fp16>
extension.

=back



=item B<vulkan>

If I<device> is an integer, it selects the device by its index in a
system-dependent list of devices.  If I<device> is any other string, it
selects the first device with a name containing that string as a substring.

The following options are recognized:

=over 4


=item B<debug>

If set to 1, enables the validation layer, if installed.

=item B<linear_images>

If set to 1, images allocated by the hwcontext will be linear and locally mappable.

=item B<instance_extensions>

A plus separated list of additional instance extensions to enable.

=item B<device_extensions>

A plus separated list of additional device extensions to enable.

=back


Examples:

=over 4


=item I<-init_hw_device vulkan:1>

Choose the second device on the system.


=item I<-init_hw_device vulkan:RADV>

Choose the first device with a name containing the string I<RADV>.


=item I<-init_hw_device vulkan:0,instance_extensions=VK_KHR_wayland_surface+VK_KHR_xcb_surface>

Choose the first device and enable the Wayland and XCB instance extensions.

=back



=back



=item B<-init_hw_device> I<type>B<[=>I<name>B<]@>I<source>

Initialise a new hardware device of type I<type> called I<name>,
deriving it from the existing device with the name I<source>.


=item B<-init_hw_device list>

List all hardware device types supported in this build of ffmpeg.


=item B<-filter_hw_device> I<name>

Pass the hardware device called I<name> to all filters in any filter graph.
This can be used to set the device to upload to with the C<hwupload> filter,
or the device to map to with the C<hwmap> filter.  Other filters may also
make use of this parameter when they require a hardware device.  Note that this
is typically only required when the input is not already in hardware frames -
when it is, filters will derive the device they require from the context of the
frames they receive as input.

This is a global setting, so all filters will receive the same device.


=item B<-hwaccel[:>I<stream_specifier>B<]> I<hwaccel> B<(>I<input,per-stream>B<)>

Use hardware acceleration to decode the matching stream(s). The allowed values
of I<hwaccel> are:

=over 4


=item B<none>

Do not use any hardware acceleration (the default).


=item B<auto>

Automatically select the hardware acceleration method.


=item B<vdpau>

Use VDPAU (Video Decode and Presentation API for Unix) hardware acceleration.


=item B<dxva2>

Use DXVA2 (DirectX Video Acceleration) hardware acceleration.


=item B<d3d11va>

Use D3D11VA (DirectX Video Acceleration) hardware acceleration.


=item B<vaapi>

Use VAAPI (Video Acceleration API) hardware acceleration.


=item B<qsv>

Use the Intel QuickSync Video acceleration for video transcoding.

Unlike most other values, this option does not enable accelerated decoding (that
is used automatically whenever a qsv decoder is selected), but accelerated
transcoding, without copying the frames into the system memory.

For it to work, both the decoder and the encoder must support QSV acceleration
and no filters must be used.


=item B<videotoolbox>

Use Video Toolbox hardware acceleration.

=back


This option has no effect if the selected hwaccel is not available or not
supported by the chosen decoder.

Note that most acceleration methods are intended for playback and will not be
faster than software decoding on modern CPUs. Additionally, B<ffmpeg>
will usually need to copy the decoded frames from the GPU memory into the system
memory, resulting in further performance loss. This option is thus mainly
useful for testing.


=item B<-hwaccel_device[:>I<stream_specifier>B<]> I<hwaccel_device> B<(>I<input,per-stream>B<)>

Select a device to use for hardware acceleration.

This option only makes sense when the B<-hwaccel> option is also specified.
It can either refer to an existing device created with B<-init_hw_device>
by name, or it can create a new device as if
B<-init_hw_device> I<type>:I<hwaccel_device>
were called immediately before.


=item B<-hwaccels>

List all hardware acceleration components enabled in this build of ffmpeg.
Actual runtime availability depends on the hardware and its suitable driver
being installed.


=item B<-fix_sub_duration_heartbeat[:>I<stream_specifier>B<]>

Set a specific output video stream as the heartbeat stream according to which
to split and push through currently in-progress subtitle upon receipt of a
random access packet.

This lowers the latency of subtitles for which the end packet or the following
subtitle has not yet been received. As a drawback, this will most likely lead
to duplication of subtitle events in order to cover the full duration, so
when dealing with use cases where latency of when the subtitle event is passed
on to output is not relevant this option should not be utilized.

Requires B<-fix_sub_duration> to be set for the relevant input subtitle
stream for this to have any effect, as well as for the input subtitle stream
having to be directly mapped to the same output in which the heartbeat stream
resides.


=back



=head2 Audio Options



=over 4


=item B<-aframes> I<number> B<(>I<output>B<)>

Set the number of audio frames to output. This is an obsolete alias for
C<-frames:a>, which you should use instead.

=item B<-ar[:>I<stream_specifier>B<]> I<freq> B<(>I<input/output,per-stream>B<)>

Set the audio sampling frequency. For output streams it is set by
default to the frequency of the corresponding input stream. For input
streams this option only makes sense for audio grabbing devices and raw
demuxers and is mapped to the corresponding demuxer options.

=item B<-aq> I<q> B<(>I<output>B<)>

Set the audio quality (codec-specific, VBR). This is an alias for -q:a.

=item B<-ac[:>I<stream_specifier>B<]> I<channels> B<(>I<input/output,per-stream>B<)>

Set the number of audio channels. For output streams it is set by
default to the number of input audio channels. For input streams
this option only makes sense for audio grabbing devices and raw demuxers
and is mapped to the corresponding demuxer options.

=item B<-an (>I<input/output>B<)>

As an input option, blocks all audio streams of a file from being filtered or
being automatically selected or mapped for any output. See C<-discard>
option to disable streams individually.

As an output option, disables audio recording i.e. automatic selection or
mapping of any audio stream. For full manual control see the C<-map>
option.

=item B<-acodec> I<codec> B<(>I<input/output>B<)>

Set the audio codec. This is an alias for C<-codec:a>.

=item B<-sample_fmt[:>I<stream_specifier>B<]> I<sample_fmt> B<(>I<output,per-stream>B<)>

Set the audio sample format. Use C<-sample_fmts> to get a list
of supported sample formats.


=item B<-af> I<filtergraph> B<(>I<output>B<)>

Create the filtergraph specified by I<filtergraph> and use it to
filter the stream.

This is an alias for C<-filter:a>, see the B<-filter option>.

=back



=head2 Advanced Audio options



=over 4


=item B<-atag> I<fourcc/tag> B<(>I<output>B<)>

Force audio tag/fourcc. This is an alias for C<-tag:a>.

=item B<-ch_layout[:>I<stream_specifier>B<]> I<layout> B<(>I<input/output,per-stream>B<)>

Alias for C<-channel_layout>.

=item B<-channel_layout[:>I<stream_specifier>B<]> I<layout> B<(>I<input/output,per-stream>B<)>

Set the audio channel layout. For output streams it is set by default to the
input channel layout. For input streams it overrides the channel layout of the
input. Not all decoders respect the overridden channel layout. This option
also sets the channel layout for audio grabbing devices and raw demuxers
and is mapped to the corresponding demuxer option.

=item B<-guess_layout_max> I<channels> B<(>I<input,per-stream>B<)>

If some input channel layout is not known, try to guess only if it
corresponds to at most the specified number of channels. For example, 2
tells to B<ffmpeg> to recognize 1 channel as mono and 2 channels as
stereo but not 6 channels as 5.1. The default is to always try to guess. Use
0 to disable all guessing. Using the C<-channel_layout> option to
explicitly specify an input layout also disables guessing.

=back



=head2 Subtitle options



=over 4


=item B<-scodec> I<codec> B<(>I<input/output>B<)>

Set the subtitle codec. This is an alias for C<-codec:s>.

=item B<-sn (>I<input/output>B<)>

As an input option, blocks all subtitle streams of a file from being filtered or
being automatically selected or mapped for any output. See C<-discard>
option to disable streams individually.

As an output option, disables subtitle recording i.e. automatic selection or
mapping of any subtitle stream. For full manual control see the C<-map>
option.

=back



=head2 Advanced Subtitle options



=over 4



=item B<-fix_sub_duration>

Fix subtitles durations. For each subtitle, wait for the next packet in the
same stream and adjust the duration of the first to avoid overlap. This is
necessary with some subtitles codecs, especially DVB subtitles, because the
duration in the original packet is only a rough estimate and the end is
actually marked by an empty subtitle frame. Failing to use this option when
necessary can result in exaggerated durations or muxing failures due to
non-monotonic timestamps.

Note that this option will delay the output of all data until the next
subtitle packet is decoded: it may increase memory consumption and latency a
lot.


=item B<-canvas_size> I<size>

Set the size of the canvas used to render subtitles.


=back



=head2 Advanced options



=over 4


=item B<-map [-]>I<input_file_id>B<[:>I<stream_specifier>B<][:>I<view_specifier>B<][:?] |> I<[linklabel]> B<(>I<output>B<)>


Create one or more streams in the output file. This option has two forms for
specifying the data source(s): the first selects one or more streams from some
input file (specified with C<-i>), the second takes an output from some
complex filtergraph (specified with C<-filter_complex>).

In the first form, an output stream is created for every stream from the input
file with the index I<input_file_id>. If I<stream_specifier> is given,
only those streams that match the specifier are used (see the
B<Stream specifiers> section for the I<stream_specifier> syntax).

A C<-> character before the stream identifier creates a "negative" mapping.
It disables matching streams from already created mappings.

An optional I<view_specifier> may be given after the stream specifier, which
for multiview video specifies the view to be used. The view specifier may have
one of the following formats:

=over 4


=item B<view:>I<view_id>

select a view by its ID; I<view_id> may be set to 'all' to use all the views
interleaved into one stream;


=item B<vidx:>I<view_idx>

select a view by its index; i.e. 0 is the base view, 1 is the first non-base
view, etc.


=item B<vpos:>I<position>

select a view by its display position; I<position> may be C<left> or
C<right>

=back

The default for transcoding is to only use the base view, i.e. the equivalent of
C<vidx:0>. For streamcopy, view specifiers are not supported and all views
are always copied.

A trailing C<?> after the stream index will allow the map to be
optional: if the map matches no streams the map will be ignored instead
of failing. Note the map will still fail if an invalid input file index
is used; such as if the map refers to a non-existent input.

An alternative I<[linklabel]> form will map outputs from complex filter
graphs (see the B<-filter_complex> option) to the output file.
I<linklabel> must correspond to a defined output link label in the graph.

This option may be specified multiple times, each adding more streams to the
output file. Any given input stream may also be mapped any number of times as a
source for different output streams, e.g. in order to use different encoding
options and/or filters. The streams are created in the output in the same order
in which the C<-map> options are given on the commandline.

Using this option disables the default mappings for this output file.

Examples:


=over 4



=item I<map everything>

To map ALL streams from the first input file to output
	
	ffmpeg -i INPUT -map 0 output



=item I<select specific stream>

If you have two audio streams in the first input file, these streams are
identified by I<0:0> and I<0:1>. You can use C<-map> to select which
streams to place in an output file. For example:
	
	ffmpeg -i INPUT -map 0:1 out.wav

will map the second input stream in F<INPUT> to the (single) output stream
in F<out.wav>.


=item I<create multiple streams>

To select the stream with index 2 from input file F<a.mov> (specified by the
identifier I<0:2>), and stream with index 6 from input F<b.mov>
(specified by the identifier I<1:6>), and copy them to the output file
F<out.mov>:
	
	ffmpeg -i a.mov -i b.mov -c copy -map 0:2 -map 1:6 out.mov



=item I<create multiple streams 2>

To select all video and the third audio stream from an input file:
	
	ffmpeg -i INPUT -map 0:v -map 0:a:2 OUTPUT



=item I<negative map>

To map all the streams except the second audio, use negative mappings
	
	ffmpeg -i INPUT -map 0 -map -0:a:1 OUTPUT



=item I<optional map>

To map the video and audio streams from the first input, and using the
trailing C<?>, ignore the audio mapping if no audio streams exist in
the first input:
	
	ffmpeg -i INPUT -map 0:v -map 0:a? OUTPUT



=item I<map by language>

To pick the English audio stream:
	
	ffmpeg -i INPUT -map 0:m:language:eng OUTPUT



=back



=item B<-ignore_unknown>

Ignore input streams with unknown type instead of failing if copying
such streams is attempted.


=item B<-copy_unknown>

Allow input streams with unknown type to be copied instead of failing if copying
such streams is attempted.


=item B<-map_metadata[:>I<metadata_spec_out>B<]> I<infile>B<[:>I<metadata_spec_in>B<] (>I<output,per-metadata>B<)>

Set metadata information of the next output file from I<infile>. Note that
those are file indices (zero-based), not filenames.
Optional I<metadata_spec_in/out> parameters specify, which metadata to copy.
A metadata specifier can have the following forms:

=over 4


=item I<g>

global metadata, i.e. metadata that applies to the whole file


=item I<s>B<[:>I<stream_spec>B<]>

per-stream metadata. I<stream_spec> is a stream specifier as described
in the B<Stream specifiers> chapter. In an input metadata specifier, the first
matching stream is copied from. In an output metadata specifier, all matching
streams are copied to.


=item I<c>B<:>I<chapter_index>

per-chapter metadata. I<chapter_index> is the zero-based chapter index.


=item I<p>B<:>I<program_index>

per-program metadata. I<program_index> is the zero-based program index.

=back

If metadata specifier is omitted, it defaults to global.

By default, global metadata is copied from the first input file,
per-stream and per-chapter metadata is copied along with streams/chapters. These
default mappings are disabled by creating any mapping of the relevant type. A negative
file index can be used to create a dummy mapping that just disables automatic copying.

For example to copy metadata from the first stream of the input file to global metadata
of the output file:
	
	ffmpeg -i in.ogg -map_metadata 0:s:0 out.mp3


To do the reverse, i.e. copy global metadata to all audio streams:
	
	ffmpeg -i in.mkv -map_metadata:s:a 0:g out.mkv

Note that simple C<0> would work as well in this example, since global
metadata is assumed by default.


=item B<-map_chapters> I<input_file_index> B<(>I<output>B<)>

Copy chapters from input file with index I<input_file_index> to the next
output file. If no chapter mapping is specified, then chapters are copied from
the first input file with at least one chapter. Use a negative file index to
disable any chapter copying.


=item B<-benchmark (>I<global>B<)>

Show benchmarking information at the end of an encode.
Shows real, system and user time used and maximum memory consumption.
Maximum memory consumption is not supported on all systems,
it will usually display as 0 if not supported.

=item B<-benchmark_all (>I<global>B<)>

Show benchmarking information during the encode.
Shows real, system and user time used in various steps (audio/video encode/decode).

=item B<-timelimit> I<duration> B<(>I<global>B<)>

Exit after ffmpeg has been running for I<duration> seconds in CPU user time.

=item B<-dump (>I<global>B<)>

Dump each input packet to stderr.

=item B<-hex (>I<global>B<)>

When dumping packets, also dump the payload.

=item B<-readrate> I<speed> B<(>I<input>B<)>

Limit input read speed.

Its value is a floating-point positive number which represents the maximum duration of
media, in seconds, that should be ingested in one second of wallclock time.
Default value is zero and represents no imposed limitation on speed of ingestion.
Value C<1> represents real-time speed and is equivalent to C<-re>.

Mainly used to simulate a capture device or live input stream (e.g. when reading from a file).
Should not be used with a low value when input is an actual capture device or live stream as
it may cause packet loss.

It is useful for when flow speed of output packets is important, such as live streaming.

=item B<-re (>I<input>B<)>

Read input at native frame rate. This is equivalent to setting C<-readrate 1>.

=item B<-readrate_initial_burst> I<seconds>

Set an initial read burst time, in seconds, after which B<-re/-readrate>
will be enforced.

=item B<-readrate_catchup> I<speed> B<(>I<input>B<)>

If either the input or output is blocked leading to actual read speed falling behind the
specified readrate, then this rate takes effect till the input catches up with the
specified readrate. Must not be lower than the primary readrate.


=item B<-vsync> I<parameter> B<(>I<global>B<)>


=item B<-fps_mode[:>I<stream_specifier>B<]> I<parameter> B<(>I<output,per-stream>B<)>

Set video sync method / framerate mode. vsync is applied to all output video streams
but can be overridden for a stream by setting fps_mode. vsync is deprecated and will be
removed in the future.

For compatibility reasons some of the values for vsync can be specified as numbers (shown
in parentheses in the following table).


=over 4


=item B<passthrough (0)>

Each frame is passed with its timestamp from the demuxer to the muxer.

=item B<cfr (1)>

Frames will be duplicated and dropped to achieve exactly the requested
constant frame rate.

=item B<vfr (2)>

Frames are passed through with their timestamp or dropped so as to
prevent 2 frames from having the same timestamp.

=item B<auto (-1)>

Chooses between cfr and vfr depending on muxer capabilities. This is the
default method.

=back


Note that the timestamps may be further modified by the muxer, after this.
For example, in the case that the format option B<avoid_negative_ts>
is enabled.

With -map you can select from which stream the timestamps should be
taken. You can leave either video or audio unchanged and sync the
remaining stream(s) to the unchanged one.


=item B<-frame_drop_threshold> I<parameter>

Frame drop threshold, which specifies how much behind video frames can
be before they are dropped. In frame rate units, so 1.0 is one frame.
The default is -1.1. One possible usecase is to avoid framedrops in case
of noisy timestamps or to increase frame drop precision in case of exact
timestamps.


=item B<-apad> I<parameters> B<(>I<output,per-stream>B<)>

Pad the output audio stream(s). This is the same as applying C<-af apad>.
Argument is a string of filter parameters composed the same as with the C<apad> filter.
C<-shortest> must be set for this output for the option to take effect.


=item B<-copyts>

Do not process input timestamps, but keep their values without trying
to sanitize them. In particular, do not remove the initial start time
offset value.

Note that, depending on the B<vsync> option or on specific muxer
processing (e.g. in case the format option B<avoid_negative_ts>
is enabled) the output timestamps may mismatch with the input
timestamps even when this option is selected.


=item B<-start_at_zero>

When used with B<copyts>, shift input timestamps so they start at zero.

This means that using e.g. C<-ss 50> will make output timestamps start at
50 seconds, regardless of what timestamp the input file started at.


=item B<-copytb> I<mode>

Specify how to set the encoder timebase when stream copying.  I<mode> is an
integer numeric value, and can assume one of the following values:


=over 4


=item B<1>

Use the demuxer timebase.

The time base is copied to the output encoder from the corresponding input
demuxer. This is sometimes required to avoid non monotonically increasing
timestamps when copying video streams with variable frame rate.


=item B<0>

Use the decoder timebase.

The time base is copied to the output encoder from the corresponding input
decoder.


=item B<-1>

Try to make the choice automatically, in order to generate a sane output.

=back


Default value is -1.


=item B<-enc_time_base[:>I<stream_specifier>B<]> I<timebase> B<(>I<output,per-stream>B<)>

Set the encoder timebase. I<timebase> can assume one of the following values:


=over 4


=item B<0>

Assign a default value according to the media type.

For video - use 1/framerate, for audio - use 1/samplerate.


=item B<demux>

Use the timebase from the demuxer.


=item B<filter>

Use the timebase from the filtergraph.


=item B<a positive number>

Use the provided number as the timebase.

This field can be provided as a ratio of two integers (e.g. 1:24, 1:48000)
or as a decimal number (e.g. 0.04166, 2.0833e-5)

=back


Default value is 0.


=item B<-bitexact (>I<input/output>B<)>

Enable bitexact mode for (de)muxer and (de/en)coder

=item B<-shortest (>I<output>B<)>

Finish encoding when the shortest output stream ends.

Note that this option may require buffering frames, which introduces extra
latency. The maximum amount of this latency may be controlled with the
C<-shortest_buf_duration> option.


=item B<-shortest_buf_duration> I<duration> B<(>I<output>B<)>

The C<-shortest> option may require buffering potentially large amounts
of data when at least one of the streams is "sparse" (i.e. has large gaps
between frames – this is typically the case for subtitles).

This option controls the maximum duration of buffered frames in seconds.
Larger values may allow the C<-shortest> option to produce more accurate
results, but increase memory use and latency.

The default value is 10 seconds.


=item B<-dts_delta_threshold> I<threshold>

Timestamp discontinuity delta threshold, expressed as a decimal number
of seconds.

The timestamp discontinuity correction enabled by this option is only
applied to input formats accepting timestamp discontinuity (for which
the C<AVFMT_TS_DISCONT> flag is enabled), e.g. MPEG-TS and HLS, and
is automatically disabled when employing the C<-copyts> option
(unless wrapping is detected).

If a timestamp discontinuity is detected whose absolute value is
greater than I<threshold>, ffmpeg will remove the discontinuity by
decreasing/increasing the current DTS and PTS by the corresponding
delta value.

The default value is 10.


=item B<-dts_error_threshold> I<threshold>

Timestamp error delta threshold, expressed as a decimal number of
seconds.

The timestamp correction enabled by this option is only applied to
input formats not accepting timestamp discontinuity (for which the
C<AVFMT_TS_DISCONT> flag is not enabled).

If a timestamp discontinuity is detected whose absolute value is
greater than I<threshold>, ffmpeg will drop the PTS/DTS timestamp
value.

The default value is C<3600*30> (30 hours), which is arbitrarily
picked and quite conservative.


=item B<-muxdelay> I<seconds> B<(>I<output>B<)>

Set the maximum demux-decode delay.

=item B<-muxpreload> I<seconds> B<(>I<output>B<)>

Set the initial demux-decode delay.

=item B<-streamid> I<output-stream-index>B<:>I<new-value> B<(>I<output>B<)>

Assign a new stream-id value to an output stream. This option should be
specified prior to the output filename to which it applies.
For the situation where multiple output files exist, a streamid
may be reassigned to a different value.

For example, to set the stream 0 PID to 33 and the stream 1 PID to 36 for
an output mpegts file:
	
	ffmpeg -i inurl -streamid 0:33 -streamid 1:36 out.ts



=item B<-bsf[:>I<stream_specifier>B<]> I<bitstream_filters> B<(>I<input/output,per-stream>B<)>

Apply bitstream filters to matching streams. The filters are applied to each
packet as it is received from the demuxer (when used as an input option) or
before it is sent to the muxer (when used as an output option).

I<bitstream_filters> is a comma-separated list of bitstream filter
specifications, each of the form
	
	<filter>[=<optname0>=<optval0>:<optname1>=<optval1>:...]

Any of the ',=:' characters that are to be a part of an option value need to be
escaped with a backslash.

Use the C<-bsfs> option to get the list of bitstream filters.

E.g.
	
	ffmpeg -bsf:v h264_mp4toannexb -i h264.mp4 -c:v copy -an out.h264

applies the C<h264_mp4toannexb> bitstream filter (which converts
MP4-encapsulated H.264 stream to Annex B) to the I<input> video stream.

On the other hand,
	
	ffmpeg -i file.mov -an -vn -bsf:s mov2textsub -c:s copy -f rawvideo sub.txt

applies the C<mov2textsub> bitstream filter (which extracts text from MOV
subtitles) to the I<output> subtitle stream. Note, however, that since both
examples use C<-c copy>, it matters little whether the filters are applied
on input or output - that would change if transcoding was happening.


=item B<-tag[:>I<stream_specifier>B<]> I<codec_tag> B<(>I<input/output,per-stream>B<)>

Force a tag/fourcc for matching streams.


=item B<-timecode> I<hh>B<:>I<mm>B<:>I<ss>B<SEP>I<ff>

Specify Timecode for writing. I<SEP> is ':' for non drop timecode and ';'
(or '.') for drop.
	
	ffmpeg -i input.mpg -timecode 01:02:03.04 -r 30000/1001 -s ntsc output.mpg




=item B<-filter_complex> I<filtergraph> B<(>I<global>B<)>

Define a complex filtergraph, i.e. one with arbitrary number of inputs and/or
outputs. For simple graphs -- those with one input and one output of the same
type -- see the B<-filter> options. I<filtergraph> is a description of
the filtergraph, as described in the ``Filtergraph syntax'' section of the
ffmpeg-filters manual. This option may be specified multiple times - each use
creates a new complex filtergraph.

Inputs to a complex filtergraph may come from different source types,
distinguished by the format of the corresponding link label:

=over 4


=item *

To connect an input stream, use C<[file_index:stream_specifier]> (i.e. the
same syntax as B<-map>). If I<stream_specifier> matches multiple
streams, the first one will be used. For multiview video, the stream specifier
may be followed by the view specifier, see documentation for the B<-map>
option for its syntax.


=item *

To connect a loopback decoder use [dec:I<dec_idx>], where I<dec_idx> is
the index of the loopback decoder to be connected to given input. For multiview
video, the decoder index may be followed by the view specifier, see
documentation for the B<-map> option for its syntax.


=item *

To connect an output from another complex filtergraph, use its link label. E.g
the following example:

	
	ffmpeg -i input.mkv \
	  -filter_complex '[0:v]scale=size=hd1080,split=outputs=2[for_enc][orig_scaled]' \
	  -c:v libx264 -map '[for_enc]' output.mkv \
	  -dec 0:0 \
	  -filter_complex '[dec:0][orig_scaled]hstack[stacked]' \
	  -map '[stacked]' -c:v ffv1 comparison.mkv


reads an input video and

=over 4


=item *

(line 2) uses a complex filtergraph with one input and two outputs
to scale the video to 1920x1080 and duplicate the result to both
outputs;


=item *

(line 3) encodes one scaled output with C<libx264> and writes the result to
F<output.mkv>;


=item *

(line 4) decodes this encoded stream with a loopback decoder;


=item *

(line 5) places the output of the loopback decoder (i.e. the
C<libx264>-encoded video) side by side with the scaled original input;


=item *

(line 6) combined video is then losslessly encoded and written into
F<comparison.mkv>.


=back


Note that the two filtergraphs cannot be combined into one, because then there
would be a cycle in the transcoding pipeline (filtergraph output goes to
encoding, from there to decoding, then back to the same graph), and such cycles
are not allowed.


=back


An unlabeled input will be connected to the first unused input stream of the
matching type.

Output link labels are referred to with B<-map>. Unlabeled outputs are
added to the first output file.

Note that with this option it is possible to use only lavfi sources without
normal input files.

For example, to overlay an image over video
	
	ffmpeg -i video.mkv -i image.png -filter_complex '[0:v][1:v]overlay[out]' -map
	'[out]' out.mkv

Here C<[0:v]> refers to the first video stream in the first input file,
which is linked to the first (main) input of the overlay filter. Similarly the
first video stream in the second input is linked to the second (overlay) input
of overlay.

Assuming there is only one video stream in each input file, we can omit input
labels, so the above is equivalent to
	
	ffmpeg -i video.mkv -i image.png -filter_complex 'overlay[out]' -map
	'[out]' out.mkv


Furthermore we can omit the output label and the single output from the filter
graph will be added to the output file automatically, so we can simply write
	
	ffmpeg -i video.mkv -i image.png -filter_complex 'overlay' out.mkv


As a special exception, you can use a bitmap subtitle stream as input: it
will be converted into a video with the same size as the largest video in
the file, or 720x576 if no video is present. Note that this is an
experimental and temporary solution. It will be removed once libavfilter has
proper support for subtitles.

For example, to hardcode subtitles on top of a DVB-T recording stored in
MPEG-TS format, delaying the subtitles by 1 second:
	
	ffmpeg -i input.ts -filter_complex \
	  '[#0x2ef] setpts=PTS+1/TB [sub] ; [#0x2d0] [sub] overlay' \
	  -sn -map '#0x2dc' output.mkv

(0x2d0, 0x2dc and 0x2ef are the MPEG-TS PIDs of respectively the video,
audio and subtitles streams; 0:0, 0:3 and 0:7 would have worked too)

To generate 5 seconds of pure red video using lavfi C<color> source:
	
	ffmpeg -filter_complex 'color=c=red' -t 5 out.mkv



=item B<-filter_complex_threads> I<nb_threads> B<(>I<global>B<)>

Defines how many threads are used to process a filter_complex graph.
Similar to filter_threads but used for C<-filter_complex> graphs only.
The default is the number of available CPUs.


=item B<-lavfi> I<filtergraph> B<(>I<global>B<)>

Define a complex filtergraph, i.e. one with arbitrary number of inputs and/or
outputs. Equivalent to B<-filter_complex>.


=item B<-accurate_seek (>I<input>B<)>

This option enables or disables accurate seeking in input files with the
B<-ss> option. It is enabled by default, so seeking is accurate when
transcoding. Use B<-noaccurate_seek> to disable it, which may be useful
e.g. when copying some streams and transcoding the others.


=item B<-seek_timestamp (>I<input>B<)>

This option enables or disables seeking by timestamp in input files with the
B<-ss> option. It is disabled by default. If enabled, the argument
to the B<-ss> option is considered an actual timestamp, and is not
offset by the start time of the file. This matters only for files which do
not start from timestamp 0, such as transport streams.


=item B<-thread_queue_size> I<size> B<(>I<input/output>B<)>

For input, this option sets the maximum number of queued packets when reading
from the file or device. With low latency / high rate live streams, packets may
be discarded if they are not read in a timely manner; setting this value can
force ffmpeg to use a separate input thread and read packets as soon as they
arrive. By default ffmpeg only does this if multiple inputs are specified.

For output, this option specified the maximum number of packets that may be
queued to each muxing thread.


=item B<-sdp_file> I<file> B<(>I<global>B<)>

Print sdp information for an output stream to I<file>.
This allows dumping sdp information when at least one output isn't an
rtp stream. (Requires at least one of the output formats to be rtp).


=item B<-discard (>I<input>B<)>

Allows discarding specific streams or frames from streams.
Any input stream can be fully discarded, using value C<all> whereas
selective discarding of frames from a stream occurs at the demuxer
and is not supported by all demuxers.


=over 4


=item B<none>

Discard no frame.


=item B<default>

Default, which discards no frames.


=item B<noref>

Discard all non-reference frames.


=item B<bidir>

Discard all bidirectional frames.


=item B<nokey>

Discard all frames excepts keyframes.


=item B<all>

Discard all frames.

=back



=item B<-abort_on> I<flags> B<(>I<global>B<)>

Stop and abort on various conditions. The following flags are available:


=over 4


=item B<empty_output>

No packets were passed to the muxer, the output is empty.

=item B<empty_output_stream>

No packets were passed to the muxer in some of the output streams.

=back



=item B<-max_error_rate (>I<global>B<)>

Set fraction of decoding frame failures across all inputs which when crossed
ffmpeg will return exit code 69. Crossing this threshold does not terminate
processing. Range is a floating-point number between 0 to 1. Default is 2/3.


=item B<-xerror (>I<global>B<)>

Stop and exit on error


=item B<-max_muxing_queue_size> I<packets> B<(>I<output,per-stream>B<)>

When transcoding audio and/or video streams, ffmpeg will not begin writing into
the output until it has one packet for each such stream. While waiting for that
to happen, packets for other streams are buffered. This option sets the size of
this buffer, in packets, for the matching output stream.

The default value of this option should be high enough for most uses, so only
touch this option if you are sure that you need it.


=item B<-muxing_queue_data_threshold> I<bytes> B<(>I<output,per-stream>B<)>

This is a minimum threshold until which the muxing queue size is not taken into
account. Defaults to 50 megabytes per stream, and is based on the overall size
of packets passed to the muxer.


=item B<-auto_conversion_filters (>I<global>B<)>

Enable automatically inserting format conversion filters in all filter
graphs, including those defined by B<-vf>, B<-af>,
B<-filter_complex> and B<-lavfi>. If filter format negotiation
requires a conversion, the initialization of the filters will fail.
Conversions can still be performed by inserting the relevant conversion
filter (scale, aresample) in the graph.
On by default, to explicitly disable it you need to specify
C<-noauto_conversion_filters>.


=item B<-bits_per_raw_sample[:>I<stream_specifier>B<]> I<value> B<(>I<output,per-stream>B<)>

Declare the number of bits per raw sample in the given output stream to be
I<value>. Note that this option sets the information provided to the
encoder/muxer, it does not change the stream to conform to this value. Setting
values that do not match the stream properties may result in encoding failures
or invalid output files.



=item B<-stats_enc_pre[:>I<stream_specifier>B<]> I<path> B<(>I<output,per-stream>B<)>


=item B<-stats_enc_post[:>I<stream_specifier>B<]> I<path> B<(>I<output,per-stream>B<)>


=item B<-stats_mux_pre[:>I<stream_specifier>B<]> I<path> B<(>I<output,per-stream>B<)>

Write per-frame encoding information about the matching streams into the file
given by I<path>.

B<-stats_enc_pre> writes information about raw video or audio frames right
before they are sent for encoding, while B<-stats_enc_post> writes
information about encoded packets as they are received from the encoder.
B<-stats_mux_pre> writes information about packets just as they are about to
be sent to the muxer. Every frame or packet produces one line in the specified
file. The format of this line is controlled by B<-stats_enc_pre_fmt> /
B<-stats_enc_post_fmt> / B<-stats_mux_pre_fmt>.

When stats for multiple streams are written into a single file, the lines
corresponding to different streams will be interleaved. The precise order of
this interleaving is not specified and not guaranteed to remain stable between
different invocations of the program, even with the same options.


=item B<-stats_enc_pre_fmt[:>I<stream_specifier>B<]> I<format_spec> B<(>I<output,per-stream>B<)>


=item B<-stats_enc_post_fmt[:>I<stream_specifier>B<]> I<format_spec> B<(>I<output,per-stream>B<)>


=item B<-stats_mux_pre_fmt[:>I<stream_specifier>B<]> I<format_spec> B<(>I<output,per-stream>B<)>

Specify the format for the lines written with B<-stats_enc_pre> /
B<-stats_enc_post> / B<-stats_mux_pre>.

I<format_spec> is a string that may contain directives of the form
I<{fmt}>. I<format_spec> is backslash-escaped --- use \{, \}, and \\
to write a literal {, }, or \, respectively, into the output.

The directives given with I<fmt> may be one of the following:

=over 4


=item B<fidx>

Index of the output file.


=item B<sidx>

Index of the output stream in the file.


=item B<n>

Frame number. Pre-encoding: number of frames sent to the encoder so far.
Post-encoding: number of packets received from the encoder so far.
Muxing: number of packets submitted to the muxer for this stream so far.


=item B<ni>

Input frame number. Index of the input frame (i.e. output by a decoder) that
corresponds to this output frame or packet. -1 if unavailable.


=item B<tb>

Timebase in which this frame/packet's timestamps are expressed, as a rational
number I<num/den>. Note that encoder and muxer may use different timebases.


=item B<tbi>

Timebase for I<ptsi>, as a rational number I<num/den>. Available when
I<ptsi> is available, I<0/1> otherwise.


=item B<pts>

Presentation timestamp of the frame or packet, as an integer. Should be
multiplied by the timebase to compute presentation time.


=item B<ptsi>

Presentation timestamp of the input frame (see I<ni>), as an integer. Should
be multiplied by I<tbi> to compute presentation time. Printed as
(2^63 - 1 = 9223372036854775807) when not available.


=item B<t>

Presentation time of the frame or packet, as a decimal number. Equal to
I<pts> multiplied by I<tb>.


=item B<ti>

Presentation time of the input frame (see I<ni>), as a decimal number. Equal
to I<ptsi> multiplied by I<tbi>. Printed as inf when not available.


=item B<dts (>I<packet>B<)>

Decoding timestamp of the packet, as an integer. Should be multiplied by the
timebase to compute presentation time.


=item B<dt (>I<packet>B<)>

Decoding time of the frame or packet, as a decimal number. Equal to
I<dts> multiplied by I<tb>.


=item B<sn (>I<frame,audio>B<)>

Number of audio samples sent to the encoder so far.


=item B<samp (>I<frame,audio>B<)>

Number of audio samples in the frame.


=item B<size (>I<packet>B<)>

Size of the encoded packet in bytes.


=item B<br (>I<packet>B<)>

Current bitrate in bits per second.


=item B<abr (>I<packet>B<)>

Average bitrate for the whole stream so far, in bits per second, -1 if it cannot
be determined at this point.


=item B<key (>I<packet>B<)>

Character 'K' if the packet contains a keyframe, character 'N' otherwise.

=back


Directives tagged with I<packet> may only be used with
B<-stats_enc_post_fmt> and B<-stats_mux_pre_fmt>.

Directives tagged with I<frame> may only be used with
B<-stats_enc_pre_fmt>.

Directives tagged with I<audio> may only be used with audio streams.

The default format strings are:

=over 4


=item B<pre-encoding>

{fidx} {sidx} {n} {t}

=item B<post-encoding>

{fidx} {sidx} {n} {t}

=back

In the future, new items may be added to the end of the default formatting
strings. Users who depend on the format staying exactly the same, should
prescribe it manually.

Note that stats for different streams written into the same file may have
different formats.


=back



=head2 Preset files

A preset file contains a sequence of I<option>=I<value> pairs,
one for each line, specifying a sequence of options which would be
awkward to specify on the command line. Lines starting with the hash
('#') character are ignored and are used to provide comments. Check
the F<presets> directory in the FFmpeg source tree for examples.

There are two types of preset files: ffpreset and avpreset files.


=head3 ffpreset files

ffpreset files are specified with the C<vpre>, C<apre>,
C<spre>, and C<fpre> options. The C<fpre> option takes the
filename of the preset instead of a preset name as input and can be
used for any kind of codec. For the C<vpre>, C<apre>, and
C<spre> options, the options specified in a preset file are
applied to the currently selected codec of the same type as the preset
option.

The argument passed to the C<vpre>, C<apre>, and C<spre>
preset options identifies the preset file to use according to the
following rules:

First ffmpeg searches for a file named I<arg>.ffpreset in the
directories F<$FFMPEG_DATADIR> (if set), and F<$HOME/.ffmpeg>, and in
the datadir defined at configuration time (usually F<PREFIX/share/ffmpeg>)
or in a F<ffpresets> folder along the executable on win32,
in that order. For example, if the argument is C<libvpx-1080p>, it will
search for the file F<libvpx-1080p.ffpreset>.

If no such file is found, then ffmpeg will search for a file named
I<codec_name>-I<arg>.ffpreset in the above-mentioned
directories, where I<codec_name> is the name of the codec to which
the preset file options will be applied. For example, if you select
the video codec with C<-vcodec libvpx> and use C<-vpre 1080p>,
then it will search for the file F<libvpx-1080p.ffpreset>.


=head3 avpreset files

avpreset files are specified with the C<pre> option. They work similar to
ffpreset files, but they only allow encoder- specific options. Therefore, an
I<option>=I<value> pair specifying an encoder cannot be used.

When the C<pre> option is specified, ffmpeg will look for files with the
suffix .avpreset in the directories F<$AVCONV_DATADIR> (if set), and
F<$HOME/.avconv>, and in the datadir defined at configuration time (usually
F<PREFIX/share/ffmpeg>), in that order.

First ffmpeg searches for a file named I<codec_name>-I<arg>.avpreset in
the above-mentioned directories, where I<codec_name> is the name of the codec
to which the preset file options will be applied. For example, if you select the
video codec with C<-vcodec libvpx> and use C<-pre 1080p>, then it will
search for the file F<libvpx-1080p.avpreset>.

If no such file is found, then ffmpeg will search for a file named
I<arg>.avpreset in the same directories.



=head2 vstats file format

The C<-vstats> and C<-vstats_file> options enable generation of a file
containing statistics about the generated video outputs.

The C<-vstats_version> option controls the format version of the generated
file.

With version C<1> the format is:
	
	frame= <FRAME> q= <FRAME_QUALITY> PSNR= <PSNR> f_size= <FRAME_SIZE> s_size= <STREAM_SIZE>kB time= <TIMESTAMP> br= <BITRATE>kbits/s avg_br= <AVERAGE_BITRATE>kbits/s


With version C<2> the format is:
	
	out= <OUT_FILE_INDEX> st= <OUT_FILE_STREAM_INDEX> frame= <FRAME_NUMBER> q= <FRAME_QUALITY>f PSNR= <PSNR> f_size= <FRAME_SIZE> s_size= <STREAM_SIZE>kB time= <TIMESTAMP> br= <BITRATE>kbits/s avg_br= <AVERAGE_BITRATE>kbits/s


The value corresponding to each key is described below:

=over 4


=item B<avg_br>

average bitrate expressed in Kbits/s


=item B<br>

bitrate expressed in Kbits/s


=item B<frame>

number of encoded frame


=item B<out>

out file index


=item B<PSNR>

Peak Signal to Noise Ratio


=item B<q>

quality of the frame


=item B<f_size>

encoded packet size expressed as number of bytes


=item B<s_size>

stream size expressed in KiB


=item B<st>

out file stream index


=item B<time>

time of the packet


=item B<type>

picture type

=back


See also the B<-stats_enc options> for an alternative way
to show encoding statistics.



=head1 EXAMPLES



=head2 Video and Audio grabbing


If you specify the input format and device then ffmpeg can grab video
and audio directly.

	
	ffmpeg -f oss -i /dev/dsp -f video4linux2 -i /dev/video0 /tmp/out.mpg


Or with an ALSA audio source (mono input, card id 1) instead of OSS:
	
	ffmpeg -f alsa -ac 1 -i hw:1 -f video4linux2 -i /dev/video0 /tmp/out.mpg


Note that you must activate the right video source and channel before
launching ffmpeg with any TV viewer such as
E<lt>B<http://linux.bytesex.org/xawtv/>E<gt> by Gerd Knorr. You also
have to set the audio recording levels correctly with a
standard mixer.


=head2 X11 grabbing


Grab the X11 display with ffmpeg via

	
	ffmpeg -f x11grab -video_size cif -framerate 25 -i :0.0 /tmp/out.mpg


0.0 is display.screen number of your X11 server, same as
the DISPLAY environment variable.

	
	ffmpeg -f x11grab -video_size cif -framerate 25 -i :0.0+10,20 /tmp/out.mpg


0.0 is display.screen number of your X11 server, same as the DISPLAY environment
variable. 10 is the x-offset and 20 the y-offset for the grabbing.


=head2 Video and Audio file format conversion


Any supported file format and protocol can serve as input to ffmpeg:

Examples:

=over 4


=item *

You can use YUV files as input:

	
	ffmpeg -i /tmp/test%d.Y /tmp/out.mpg


It will use the files:
	
	/tmp/test0.Y, /tmp/test0.U, /tmp/test0.V,
	/tmp/test1.Y, /tmp/test1.U, /tmp/test1.V, etc...


The Y files use twice the resolution of the U and V files. They are
raw files, without header. They can be generated by all decent video
decoders. You must specify the size of the image with the B<-s> option
if ffmpeg cannot guess it.


=item *

You can input from a raw YUV420P file:

	
	ffmpeg -i /tmp/test.yuv /tmp/out.avi


test.yuv is a file containing raw YUV planar data. Each frame is composed
of the Y plane followed by the U and V planes at half vertical and
horizontal resolution.


=item *

You can output to a raw YUV420P file:

	
	ffmpeg -i mydivx.avi hugefile.yuv



=item *

You can set several input files and output files:

	
	ffmpeg -i /tmp/a.wav -s 640x480 -i /tmp/a.yuv /tmp/a.mpg


Converts the audio file a.wav and the raw YUV video file a.yuv
to MPEG file a.mpg.


=item *

You can also do audio and video conversions at the same time:

	
	ffmpeg -i /tmp/a.wav -ar 22050 /tmp/a.mp2


Converts a.wav to MPEG audio at 22050 Hz sample rate.


=item *

You can encode to several formats at the same time and define a
mapping from input stream to output streams:

	
	ffmpeg -i /tmp/a.wav -map 0:a -b:a 64k /tmp/a.mp2 -map 0:a -b:a 128k /tmp/b.mp2


Converts a.wav to a.mp2 at 64 kbits and to b.mp2 at 128 kbits. '-map
file:index' specifies which input stream is used for each output
stream, in the order of the definition of output streams.


=item *

You can transcode decrypted VOBs:

	
	ffmpeg -i snatch_1.vob -f avi -c:v mpeg4 -b:v 800k -g 300 -bf 2 -c:a libmp3lame -b:a 128k snatch.avi


This is a typical DVD ripping example; the input is a VOB file, the
output an AVI file with MPEG-4 video and MP3 audio. Note that in this
command we use B-frames so the MPEG-4 stream is DivX5 compatible, and
GOP size is 300 which means one intra frame every 10 seconds for 29.97fps
input video. Furthermore, the audio stream is MP3-encoded so you need
to enable LAME support by passing C<--enable-libmp3lame> to configure.
The mapping is particularly useful for DVD transcoding
to get the desired audio language.

NOTE: To see the supported input formats, use C<ffmpeg -demuxers>.


=item *

You can extract images from a video, or create a video from many images:

For extracting images from a video:
	
	ffmpeg -i foo.avi -r 1 -s WxH -f image2 foo-%03d.jpeg


This will extract one video frame per second from the video and will
output them in files named F<foo-001.jpeg>, F<foo-002.jpeg>,
etc. Images will be rescaled to fit the new WxH values.

If you want to extract just a limited number of frames, you can use the
above command in combination with the C<-frames:v> or C<-t> option,
or in combination with -ss to start extracting from a certain point in time.

For creating a video from many images:
	
	ffmpeg -f image2 -framerate 12 -i foo-%03d.jpeg -s WxH foo.avi


The syntax C<foo-%03d.jpeg> specifies to use a decimal number
composed of three digits padded with zeroes to express the sequence
number. It is the same syntax supported by the C printf function, but
only formats accepting a normal integer are suitable.

When importing an image sequence, -i also supports expanding
shell-like wildcard patterns (globbing) internally, by selecting the
image2-specific C<-pattern_type glob> option.

For example, for creating a video from filenames matching the glob pattern
C<foo-*.jpeg>:
	
	ffmpeg -f image2 -pattern_type glob -framerate 12 -i 'foo-*.jpeg' -s WxH foo.avi



=item *

You can put many streams of the same type in the output:

	
	ffmpeg -i test1.avi -i test2.avi -map 1:1 -map 1:0 -map 0:1 -map 0:0 -c copy -y test12.nut


The resulting output file F<test12.nut> will contain the first four streams
from the input files in reverse order.


=item *

To force CBR video output:
	
	ffmpeg -i myfile.avi -b 4000k -minrate 4000k -maxrate 4000k -bufsize 1835k out.m2v



=item *

The four options lmin, lmax, mblmin and mblmax use 'lambda' units,
but you may use the QP2LAMBDA constant to easily convert from 'q' units:
	
	ffmpeg -i src.ext -lmax 21*QP2LAMBDA dst.ext



=back




=head1 SEE ALSO



ffmpeg-all(1),
ffplay(1), ffprobe(1),
ffmpeg-utils(1), ffmpeg-scaler(1), ffmpeg-resampler(1),
ffmpeg-codecs(1), ffmpeg-bitstream-filters(1), ffmpeg-formats(1),
ffmpeg-devices(1), ffmpeg-protocols(1), ffmpeg-filters(1)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



