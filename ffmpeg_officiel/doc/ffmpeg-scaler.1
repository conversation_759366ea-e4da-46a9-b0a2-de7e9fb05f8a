.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "FFMPEG-SCALER 1"
.TH FFMPEG-SCALER 1 " " " " " "
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ffmpeg\-scaler \- FFmpeg video scaling and pixel format converter
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The FFmpeg rescaler provides a high-level interface to the libswscale
library image conversion utilities. In particular it allows one to perform
image rescaling and pixel format conversion.
.SH "SCALER OPTIONS"
.IX Header "SCALER OPTIONS"
The video scaler supports the following named options.
.PP
Options may be set by specifying \-\fIoption\fR \fIvalue\fR in the
FFmpeg tools, with a few API-only exceptions noted below.
For programmatic use, they can be set explicitly in the
\&\f(CW\*(C`SwsContext\*(C'\fR options or through the \fIlibavutil/opt.h\fR API.
.IP \fBsws_flags\fR 4
.IX Item "sws_flags"
Set the scaler flags. This is also used to set the scaling
algorithm. Only a single algorithm should be selected. Default
value is \fBbicubic\fR.
.Sp
It accepts the following values:
.RS 4
.IP \fBfast_bilinear\fR 4
.IX Item "fast_bilinear"
Select fast bilinear scaling algorithm.
.IP \fBbilinear\fR 4
.IX Item "bilinear"
Select bilinear scaling algorithm.
.IP \fBbicubic\fR 4
.IX Item "bicubic"
Select bicubic scaling algorithm.
.IP \fBexperimental\fR 4
.IX Item "experimental"
Select experimental scaling algorithm.
.IP \fBneighbor\fR 4
.IX Item "neighbor"
Select nearest neighbor rescaling algorithm.
.IP \fBarea\fR 4
.IX Item "area"
Select averaging area rescaling algorithm.
.IP \fBbicublin\fR 4
.IX Item "bicublin"
Select bicubic scaling algorithm for the luma component, bilinear for
chroma components.
.IP \fBgauss\fR 4
.IX Item "gauss"
Select Gaussian rescaling algorithm.
.IP \fBsinc\fR 4
.IX Item "sinc"
Select sinc rescaling algorithm.
.IP \fBlanczos\fR 4
.IX Item "lanczos"
Select Lanczos rescaling algorithm. The default width (alpha) is 3 and can be
changed by setting \f(CW\*(C`param0\*(C'\fR.
.IP \fBspline\fR 4
.IX Item "spline"
Select natural bicubic spline rescaling algorithm.
.IP \fBprint_info\fR 4
.IX Item "print_info"
Enable printing/debug logging.
.IP \fBaccurate_rnd\fR 4
.IX Item "accurate_rnd"
Enable accurate rounding.
.IP \fBfull_chroma_int\fR 4
.IX Item "full_chroma_int"
Enable full chroma interpolation.
.IP \fBfull_chroma_inp\fR 4
.IX Item "full_chroma_inp"
Select full chroma input.
.IP \fBbitexact\fR 4
.IX Item "bitexact"
Enable bitexact output.
.RE
.RS 4
.RE
.IP "\fBsrcw\fR \fI(API only)\fR" 4
.IX Item "srcw (API only)"
Set source width.
.IP "\fBsrch\fR \fI(API only)\fR" 4
.IX Item "srch (API only)"
Set source height.
.IP "\fBdstw\fR \fI(API only)\fR" 4
.IX Item "dstw (API only)"
Set destination width.
.IP "\fBdsth\fR \fI(API only)\fR" 4
.IX Item "dsth (API only)"
Set destination height.
.IP "\fBsrc_format\fR \fI(API only)\fR" 4
.IX Item "src_format (API only)"
Set source pixel format (must be expressed as an integer).
.IP "\fBdst_format\fR \fI(API only)\fR" 4
.IX Item "dst_format (API only)"
Set destination pixel format (must be expressed as an integer).
.IP "\fBsrc_range\fR \fI(boolean)\fR" 4
.IX Item "src_range (boolean)"
If value is set to \f(CW1\fR, indicates source is full range. Default value is
\&\f(CW0\fR, which indicates source is limited range.
.IP "\fBdst_range\fR \fI(boolean)\fR" 4
.IX Item "dst_range (boolean)"
If value is set to \f(CW1\fR, enable full range for destination. Default value
is \f(CW0\fR, which enables limited range.
.IP "\fBgamma\fR \fI(boolean)\fR" 4
.IX Item "gamma (boolean)"
If value is set to \f(CW1\fR, enable gamma correct scaling. Default value is \f(CW0\fR.
.IP "\fBparam0, param1\fR" 4
.IX Item "param0, param1"
Set scaling algorithm parameters. The specified values are specific of
some scaling algorithms and ignored by others. The specified values
are floating point number values.
.IP \fBsws_dither\fR 4
.IX Item "sws_dither"
Set the dithering algorithm. Accepts one of the following
values. Default value is \fBauto\fR.
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
automatic choice
.IP \fBnone\fR 4
.IX Item "none"
no dithering
.IP \fBbayer\fR 4
.IX Item "bayer"
bayer dither
.IP \fBed\fR 4
.IX Item "ed"
error diffusion dither
.IP \fBa_dither\fR 4
.IX Item "a_dither"
arithmetic dither, based using addition
.IP \fBx_dither\fR 4
.IX Item "x_dither"
arithmetic dither, based using xor (more random/less apparent patterning that
a_dither).
.RE
.RS 4
.RE
.IP \fBalphablend\fR 4
.IX Item "alphablend"
Set the alpha blending to use when the input has alpha but the output does not.
Default value is \fBnone\fR.
.RS 4
.IP \fBuniform_color\fR 4
.IX Item "uniform_color"
Blend onto a uniform background color
.IP \fBcheckerboard\fR 4
.IX Item "checkerboard"
Blend onto a checkerboard
.IP \fBnone\fR 4
.IX Item "none"
No blending
.RE
.RS 4
.RE
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBffmpeg\fR\|(1), \fBffplay\fR\|(1), \fBffprobe\fR\|(1), \fBlibswscale\fR\|(3)
.SH AUTHORS
.IX Header "AUTHORS"
The FFmpeg developers.
.PP
For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
\&\fBgit log\fR in the FFmpeg source directory, or browsing the
online repository at <\fBhttps://git.ffmpeg.org/ffmpeg\fR>.
.PP
Maintainers for the specific components are listed in the file
\&\fIMAINTAINERS\fR in the source code tree.
