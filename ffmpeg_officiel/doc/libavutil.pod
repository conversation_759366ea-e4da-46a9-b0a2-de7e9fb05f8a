=encoding utf8

=head1 NAME

libavutil - multimedia-biased utility library

=head1 DESCRIPTION


The libavutil library is a utility library to aid portable
multimedia programming. It contains safe portable string functions,
random number generators, data structures, additional mathematics
functions, cryptography and multimedia related functionality (like
enumerations for pixel and sample formats). It is not a library for
code needed by both libavcodec and libavformat.

The goals for this library is to be:


=over 4


=item B<Modular>

It should have few interdependencies and the possibility of disabling individual
parts during B<./configure>.


=item B<Small>

Both sources and objects should be small.


=item B<Efficient>

It should have low CPU and memory usage.


=item B<Useful>

It should avoid useless features that almost no one needs.

=back




=head1 SEE ALSO



ffmpeg(1), ffplay(1), ffprobe(1),
ffmpeg-utils(1)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



