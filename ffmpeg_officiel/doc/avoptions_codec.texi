@c DO NOT EDIT THIS FILE!
@c It was generated by print_options.

@section Codec AVOptions
@table @option
@item -b[:stream_specifier] @var{integer} (@emph{output,audio,video})
set bitrate (in bits/s)
@item -ab[:stream_specifier] @var{integer} (@emph{output,audio})
set bitrate (in bits/s)
@item -bt[:stream_specifier] @var{integer} (@emph{output,audio,video})
Set video bitrate tolerance (in bits/s). In 1-pass mode, bitrate tolerance specifies how far ratecontrol is willing to deviate from the target average bitrate value. This is not related to minimum/maximum bitrate. Lowering tolerance too much has an adverse effect on quality.
@item -flags[:stream_specifier] @var{flags} (@emph{input/output,audio,video,subtitles})

Possible values:
@table @samp
@item unaligned
allow decoders to produce unaligned output
@item mv4
use four motion vectors per macroblock (MPEG-4)
@item qpel
use 1/4-pel motion compensation
@item loop
use loop filter
@item qscale
use fixed qscale
@item recon_frame
export reconstructed frames
@item copy_opaque
propagate opaque values
@item frame_duration
use frame durations
@item pass1
use internal 2-pass ratecontrol in first  pass mode
@item pass2
use internal 2-pass ratecontrol in second pass mode
@item gray
only decode/encode grayscale
@item psnr
error[?] variables will be set during encoding
@item ildct
use interlaced DCT
@item low_delay
force low delay
@item global_header
place global headers in extradata instead of every keyframe
@item bitexact
use only bitexact functions (except (I)DCT)
@item aic
H.263 advanced intra coding / MPEG-4 AC prediction
@item ilme
interlaced motion estimation
@item cgop
closed GOP
@item output_corrupt
Output even potentially corrupted frames
@end table
@item -flags2[:stream_specifier] @var{flags} (@emph{input/output,audio,video,subtitles})

Possible values:
@table @samp
@item fast
allow non-spec-compliant speedup tricks
@item noout
skip bitstream encoding
@item ignorecrop
ignore cropping information from sps
@item local_header
place global headers at every keyframe instead of in extradata
@item chunks
Frame data might be split into multiple chunks
@item showall
Show all frames before the first keyframe
@item export_mvs
export motion vectors through frame side data
@item skip_manual
do not skip samples and export skip information as frame side data
@item ass_ro_flush_noop
do not reset ASS ReadOrder field on flush
@item icc_profiles
generate/parse embedded ICC profiles from/to colorimetry tags
@end table
@item -export_side_data[:stream_specifier] @var{flags} (@emph{input/output,audio,video,subtitles})
Export metadata as side data

Possible values:
@table @samp
@item mvs
export motion vectors through frame side data
@item prft
export Producer Reference Time through packet side data
@item venc_params
export video encoding parameters through frame side data
@item film_grain
export film grain parameters through frame side data
@item enhancements
export picture enhancement metadata through frame side data
@end table
@item -g[:stream_specifier] @var{integer} (@emph{output,video})
set the group of picture (GOP) size
@item -ar[:stream_specifier] @var{integer} (@emph{input/output,audio})
set audio sampling rate (in Hz)
@item -cutoff[:stream_specifier] @var{integer} (@emph{output,audio})
set cutoff bandwidth
@item -frame_size[:stream_specifier] @var{integer} (@emph{output,audio})
@item -qcomp[:stream_specifier] @var{float} (@emph{output,video})
video quantizer scale compression (VBR). Constant of ratecontrol equation. Recommended range for default rc_eq: 0.0-1.0
@item -qblur[:stream_specifier] @var{float} (@emph{output,video})
video quantizer scale blur (VBR)
@item -qmin[:stream_specifier] @var{integer} (@emph{output,video})
minimum video quantizer scale (VBR)
@item -qmax[:stream_specifier] @var{integer} (@emph{output,video})
maximum video quantizer scale (VBR)
@item -qdiff[:stream_specifier] @var{integer} (@emph{output,video})
maximum difference between the quantizer scales (VBR)
@item -bf[:stream_specifier] @var{integer} (@emph{output,video})
set maximum number of B-frames between non-B-frames
@item -b_qfactor[:stream_specifier] @var{float} (@emph{output,video})
QP factor between P- and B-frames
@item -bug[:stream_specifier] @var{flags} (@emph{input,video})
work around not autodetected encoder bugs

Possible values:
@table @samp
@item autodetect

@item xvid_ilace
Xvid interlacing bug (autodetected if FOURCC == XVIX)
@item ump4
(autodetected if FOURCC == UMP4)
@item no_padding
padding bug (autodetected)
@item amv

@item qpel_chroma

@item std_qpel
old standard qpel (autodetected per FOURCC/version)
@item qpel_chroma2

@item direct_blocksize
direct-qpel-blocksize bug (autodetected per FOURCC/version)
@item edge
edge padding bug (autodetected per FOURCC/version)
@item hpel_chroma

@item dc_clip

@item ms
work around various bugs in Microsoft's broken decoders
@item trunc
truncated frames
@item iedge

@end table
@item -strict[:stream_specifier] @var{integer} (@emph{input/output,audio,video})
how strictly to follow the standards

Possible values:
@table @samp
@item very
strictly conform to a older more strict version of the spec or reference software
@item strict
strictly conform to all the things in the spec no matter what the consequences
@item normal

@item unofficial
allow unofficial extensions
@item experimental
allow non-standardized experimental things
@end table
@item -b_qoffset[:stream_specifier] @var{float} (@emph{output,video})
QP offset between P- and B-frames
@item -err_detect[:stream_specifier] @var{flags} (@emph{input/output,audio,video,subtitles})
set error detection flags

Possible values:
@table @samp
@item crccheck
verify embedded CRCs
@item bitstream
detect bitstream specification deviations
@item buffer
detect improper bitstream length
@item explode
abort decoding on minor error detection
@item ignore_err
ignore errors
@item careful
consider things that violate the spec, are fast to check and have not been seen in the wild as errors
@item compliant
consider all spec non compliancies as errors
@item aggressive
consider things that a sane encoder should not do as an error
@end table
@item -maxrate[:stream_specifier] @var{integer} (@emph{output,audio,video})
maximum bitrate (in bits/s). Used for VBV together with bufsize.
@item -minrate[:stream_specifier] @var{integer} (@emph{output,audio,video})
minimum bitrate (in bits/s). Most useful in setting up a CBR encode. It is of little use otherwise.
@item -bufsize[:stream_specifier] @var{integer} (@emph{output,audio,video})
set ratecontrol buffer size (in bits)
@item -i_qfactor[:stream_specifier] @var{float} (@emph{output,video})
QP factor between P- and I-frames
@item -i_qoffset[:stream_specifier] @var{float} (@emph{output,video})
QP offset between P- and I-frames
@item -dct[:stream_specifier] @var{integer} (@emph{output,video})
DCT algorithm

Possible values:
@table @samp
@item auto
autoselect a good one
@item fastint
fast integer
@item int
accurate integer
@item mmx

@item altivec

@item faan
floating point AAN DCT
@item neon

@end table
@item -lumi_mask[:stream_specifier] @var{float} (@emph{output,video})
compresses bright areas stronger than medium ones
@item -tcplx_mask[:stream_specifier] @var{float} (@emph{output,video})
temporal complexity masking
@item -scplx_mask[:stream_specifier] @var{float} (@emph{output,video})
spatial complexity masking
@item -p_mask[:stream_specifier] @var{float} (@emph{output,video})
inter masking
@item -dark_mask[:stream_specifier] @var{float} (@emph{output,video})
compresses dark areas stronger than medium ones
@item -idct[:stream_specifier] @var{integer} (@emph{input/output,video})
select IDCT implementation

Possible values:
@table @samp
@item auto

@item int

@item simple

@item simplemmx

@item arm

@item altivec

@item simplearm

@item simplearmv5te

@item simplearmv6

@item simpleneon

@item xvid

@item xvidmmx
deprecated, for compatibility only
@item faani
floating point AAN IDCT
@item simpleauto

@end table
@item -ec[:stream_specifier] @var{flags} (@emph{input,video})
set error concealment strategy

Possible values:
@table @samp
@item guess_mvs
iterative motion vector (MV) search (slow)
@item deblock
use strong deblock filter for damaged MBs
@item favor_inter
favor predicting from the previous frame
@end table
@item -aspect[:stream_specifier] @var{rational number} (@emph{output,video})
sample aspect ratio
@item -sar[:stream_specifier] @var{rational number} (@emph{output,video})
sample aspect ratio
@item -debug[:stream_specifier] @var{flags} (@emph{input/output,audio,video,subtitles})
print specific debug info

Possible values:
@table @samp
@item pict
picture info
@item rc
rate control
@item bitstream

@item mb_type
macroblock (MB) type
@item qp
per-block quantization parameter (QP)
@item dct_coeff

@item green_metadata

@item skip

@item startcode

@item er
error recognition
@item mmco
memory management control operations (H.264)
@item bugs

@item buffers
picture buffer allocations
@item thread_ops
threading operations
@item nomc
skip motion compensation
@end table
@item -dia_size[:stream_specifier] @var{integer} (@emph{output,video})
diamond type & size for motion estimation
@item -last_pred[:stream_specifier] @var{integer} (@emph{output,video})
amount of motion predictors from the previous frame
@item -pre_dia_size[:stream_specifier] @var{integer} (@emph{output,video})
diamond type & size for motion estimation pre-pass
@item -subq[:stream_specifier] @var{integer} (@emph{output,video})
sub-pel motion estimation quality
@item -me_range[:stream_specifier] @var{integer} (@emph{output,video})
limit motion vectors range (1023 for DivX player)
@item -global_quality[:stream_specifier] @var{integer} (@emph{output,audio,video})
@item -mbd[:stream_specifier] @var{integer} (@emph{output,video})
macroblock decision algorithm (high quality mode)

Possible values:
@table @samp
@item simple
use mbcmp
@item bits
use fewest bits
@item rd
use best rate distortion
@end table
@item -rc_init_occupancy[:stream_specifier] @var{integer} (@emph{output,video})
number of bits which should be loaded into the rc buffer before decoding starts
@item -threads[:stream_specifier] @var{integer} (@emph{input/output,audio,video})
set the number of threads

Possible values:
@table @samp
@item auto
autodetect a suitable number of threads to use
@end table
@item -dc[:stream_specifier] @var{integer} (@emph{output,video})
intra_dc_precision
@item -nssew[:stream_specifier] @var{integer} (@emph{output,video})
nsse weight
@item -skip_top[:stream_specifier] @var{integer} (@emph{input,video})
number of macroblock rows at the top which are skipped
@item -skip_bottom[:stream_specifier] @var{integer} (@emph{input,video})
number of macroblock rows at the bottom which are skipped
@item -profile[:stream_specifier] @var{integer} (@emph{output,audio,video})

Possible values:
@table @samp
@item unknown

@item main10

@end table
@item -level[:stream_specifier] @var{integer} (@emph{output,audio,video})
encoding level, usually corresponding to the profile level, codec-specific

Possible values:
@table @samp
@item unknown

@end table
@item -lowres[:stream_specifier] @var{integer} (@emph{input,audio,video})
decode at 1= 1/2, 2=1/4, 3=1/8 resolutions
@item -cmp[:stream_specifier] @var{integer} (@emph{output,video})
full-pel ME compare function

Possible values:
@table @samp
@item sad
sum of absolute differences, fast
@item sse
sum of squared errors
@item satd
sum of absolute Hadamard transformed differences
@item dct
sum of absolute DCT transformed differences
@item psnr
sum of squared quantization errors (avoid, low quality)
@item bit
number of bits needed for the block
@item rd
rate distortion optimal, slow
@item zero
0
@item vsad
sum of absolute vertical differences
@item vsse
sum of squared vertical differences
@item nsse
noise preserving sum of squared differences
@item w53
5/3 wavelet, only used in snow
@item w97
9/7 wavelet, only used in snow
@item dctmax

@item chroma

@item msad
sum of absolute differences, median predicted
@end table
@item -subcmp[:stream_specifier] @var{integer} (@emph{output,video})
sub-pel ME compare function

Possible values:
@table @samp
@item sad
sum of absolute differences, fast
@item sse
sum of squared errors
@item satd
sum of absolute Hadamard transformed differences
@item dct
sum of absolute DCT transformed differences
@item psnr
sum of squared quantization errors (avoid, low quality)
@item bit
number of bits needed for the block
@item rd
rate distortion optimal, slow
@item zero
0
@item vsad
sum of absolute vertical differences
@item vsse
sum of squared vertical differences
@item nsse
noise preserving sum of squared differences
@item w53
5/3 wavelet, only used in snow
@item w97
9/7 wavelet, only used in snow
@item dctmax

@item chroma

@item msad
sum of absolute differences, median predicted
@end table
@item -mbcmp[:stream_specifier] @var{integer} (@emph{output,video})
macroblock compare function

Possible values:
@table @samp
@item sad
sum of absolute differences, fast
@item sse
sum of squared errors
@item satd
sum of absolute Hadamard transformed differences
@item dct
sum of absolute DCT transformed differences
@item psnr
sum of squared quantization errors (avoid, low quality)
@item bit
number of bits needed for the block
@item rd
rate distortion optimal, slow
@item zero
0
@item vsad
sum of absolute vertical differences
@item vsse
sum of squared vertical differences
@item nsse
noise preserving sum of squared differences
@item w53
5/3 wavelet, only used in snow
@item w97
9/7 wavelet, only used in snow
@item dctmax

@item chroma

@item msad
sum of absolute differences, median predicted
@end table
@item -ildctcmp[:stream_specifier] @var{integer} (@emph{output,video})
interlaced DCT compare function

Possible values:
@table @samp
@item sad
sum of absolute differences, fast
@item sse
sum of squared errors
@item satd
sum of absolute Hadamard transformed differences
@item dct
sum of absolute DCT transformed differences
@item psnr
sum of squared quantization errors (avoid, low quality)
@item bit
number of bits needed for the block
@item rd
rate distortion optimal, slow
@item zero
0
@item vsad
sum of absolute vertical differences
@item vsse
sum of squared vertical differences
@item nsse
noise preserving sum of squared differences
@item w53
5/3 wavelet, only used in snow
@item w97
9/7 wavelet, only used in snow
@item dctmax

@item chroma

@item msad
sum of absolute differences, median predicted
@end table
@item -precmp[:stream_specifier] @var{integer} (@emph{output,video})
pre motion estimation compare function

Possible values:
@table @samp
@item sad
sum of absolute differences, fast
@item sse
sum of squared errors
@item satd
sum of absolute Hadamard transformed differences
@item dct
sum of absolute DCT transformed differences
@item psnr
sum of squared quantization errors (avoid, low quality)
@item bit
number of bits needed for the block
@item rd
rate distortion optimal, slow
@item zero
0
@item vsad
sum of absolute vertical differences
@item vsse
sum of squared vertical differences
@item nsse
noise preserving sum of squared differences
@item w53
5/3 wavelet, only used in snow
@item w97
9/7 wavelet, only used in snow
@item dctmax

@item chroma

@item msad
sum of absolute differences, median predicted
@end table
@item -mblmin[:stream_specifier] @var{integer} (@emph{output,video})
minimum macroblock Lagrange factor (VBR)
@item -mblmax[:stream_specifier] @var{integer} (@emph{output,video})
maximum macroblock Lagrange factor (VBR)
@item -skip_loop_filter[:stream_specifier] @var{integer} (@emph{input,video})
skip loop filtering process for the selected frames

Possible values:
@table @samp
@item none
discard no frame
@item default
discard useless frames
@item noref
discard all non-reference frames
@item bidir
discard all bidirectional frames
@item nointra
discard all frames except I frames
@item nokey
discard all frames except keyframes
@item all
discard all frames
@end table
@item -skip_idct[:stream_specifier] @var{integer} (@emph{input,video})
skip IDCT/dequantization for the selected frames

Possible values:
@table @samp
@item none
discard no frame
@item default
discard useless frames
@item noref
discard all non-reference frames
@item bidir
discard all bidirectional frames
@item nointra
discard all frames except I frames
@item nokey
discard all frames except keyframes
@item all
discard all frames
@end table
@item -skip_frame[:stream_specifier] @var{integer} (@emph{input,video})
skip decoding for the selected frames

Possible values:
@table @samp
@item none
discard no frame
@item default
discard useless frames
@item noref
discard all non-reference frames
@item bidir
discard all bidirectional frames
@item nointra
discard all frames except I frames
@item nokey
discard all frames except keyframes
@item all
discard all frames
@end table
@item -bidir_refine[:stream_specifier] @var{integer} (@emph{output,video})
refine the two motion vectors used in bidirectional macroblocks
@item -keyint_min[:stream_specifier] @var{integer} (@emph{output,video})
minimum interval between IDR-frames
@item -refs[:stream_specifier] @var{integer} (@emph{output,video})
reference frames to consider for motion compensation
@item -trellis[:stream_specifier] @var{integer} (@emph{output,audio,video})
rate-distortion optimal quantization
@item -mv0_threshold[:stream_specifier] @var{integer} (@emph{output,video})
@item -compression_level[:stream_specifier] @var{integer} (@emph{output,audio,video})
@item -ch_layout[:stream_specifier] @var{value} (@emph{input/output,audio})

Possible values:
@table @samp
@end table
@item -rc_max_vbv_use[:stream_specifier] @var{float} (@emph{output,video})
@item -rc_min_vbv_use[:stream_specifier] @var{float} (@emph{output,video})
@item -color_primaries[:stream_specifier] @var{integer} (@emph{input/output,video})
color primaries

Possible values:
@table @samp
@item bt709
BT.709
@item unknown
Unspecified
@item bt470m
BT.470 M
@item bt470bg
BT.470 BG
@item smpte170m
SMPTE 170 M
@item smpte240m
SMPTE 240 M
@item film
Film
@item bt2020
BT.2020
@item smpte428
SMPTE 428-1
@item smpte428_1
SMPTE 428-1
@item smpte431
SMPTE 431-2
@item smpte432
SMPTE 422-1
@item jedec-p22
JEDEC P22
@item ebu3213
EBU 3213-E
@item unspecified
Unspecified
@end table
@item -color_trc[:stream_specifier] @var{integer} (@emph{input/output,video})
color transfer characteristics

Possible values:
@table @samp
@item bt709
BT.709
@item unknown
Unspecified
@item gamma22
BT.470 M
@item gamma28
BT.470 BG
@item smpte170m
SMPTE 170 M
@item smpte240m
SMPTE 240 M
@item linear
Linear
@item log100
Log
@item log316
Log square root
@item iec61966-2-4
IEC 61966-2-4
@item bt1361e
BT.1361
@item iec61966-2-1
IEC 61966-2-1
@item bt2020-10
BT.2020 - 10 bit
@item bt2020-12
BT.2020 - 12 bit
@item smpte2084
SMPTE 2084
@item smpte428
SMPTE 428-1
@item arib-std-b67
ARIB STD-B67
@item unspecified
Unspecified
@item log
Log
@item log_sqrt
Log square root
@item iec61966_2_4
IEC 61966-2-4
@item bt1361
BT.1361
@item iec61966_2_1
IEC 61966-2-1
@item bt2020_10bit
BT.2020 - 10 bit
@item bt2020_12bit
BT.2020 - 12 bit
@item smpte428_1
SMPTE 428-1
@end table
@item -colorspace[:stream_specifier] @var{integer} (@emph{input/output,video})
color space

Possible values:
@table @samp
@item rgb
RGB
@item bt709
BT.709
@item unknown
Unspecified
@item fcc
FCC
@item bt470bg
BT.470 BG
@item smpte170m
SMPTE 170 M
@item smpte240m
SMPTE 240 M
@item ycgco
YCGCO
@item bt2020nc
BT.2020 NCL
@item bt2020c
BT.2020 CL
@item smpte2085
SMPTE 2085
@item chroma-derived-nc
Chroma-derived NCL
@item chroma-derived-c
Chroma-derived CL
@item ictcp
ICtCp
@item ipt-c2
IPT-C2
@item unspecified
Unspecified
@item ycocg
YCGCO
@item ycgco-re
YCgCo-R, even add.
@item ycgco-ro
YCgCo-R, odd add.
@item bt2020_ncl
BT.2020 NCL
@item bt2020_cl
BT.2020 CL
@end table
@item -color_range[:stream_specifier] @var{integer} (@emph{input/output,video})
color range

Possible values:
@table @samp
@item unknown
Unspecified
@item tv
MPEG (219*2^(n-8))
@item pc
JPEG (2^n-1)
@item unspecified
Unspecified
@item mpeg
MPEG (219*2^(n-8))
@item jpeg
JPEG (2^n-1)
@item limited
MPEG (219*2^(n-8))
@item full
JPEG (2^n-1)
@end table
@item -chroma_sample_location[:stream_specifier] @var{integer} (@emph{input/output,video})
chroma sample location

Possible values:
@table @samp
@item unknown
Unspecified
@item left
Left
@item center
Center
@item topleft
Top-left
@item top
Top
@item bottomleft
Bottom-left
@item bottom
Bottom
@item unspecified
Unspecified
@end table
@item -slices[:stream_specifier] @var{integer} (@emph{output,video})
set the number of slices, used in parallelized encoding
@item -thread_type[:stream_specifier] @var{flags} (@emph{input/output,audio,video})
select multithreading type

Possible values:
@table @samp
@item slice

@item frame

@end table
@item -audio_service_type[:stream_specifier] @var{integer} (@emph{output,audio})
audio service type

Possible values:
@table @samp
@item ma
Main Audio Service
@item ef
Effects
@item vi
Visually Impaired
@item hi
Hearing Impaired
@item di
Dialogue
@item co
Commentary
@item em
Emergency
@item vo
Voice Over
@item ka
Karaoke
@end table
@item -request_sample_fmt[:stream_specifier] @var{value} (@emph{input,audio})
sample format audio decoders should prefer

Possible values:
@table @samp
@end table
@item -sub_charenc[:stream_specifier] @var{string} (@emph{input,subtitles})
set input text subtitles character encoding
@item -sub_charenc_mode[:stream_specifier] @var{flags} (@emph{input,subtitles})
set input text subtitles character encoding mode

Possible values:
@table @samp
@item do_nothing

@item auto

@item pre_decoder

@item ignore

@end table
@item -apply_cropping[:stream_specifier] @var{value} (@emph{input,video})
@item -skip_alpha[:stream_specifier] @var{value} (@emph{input,video})
Skip processing alpha
@item -field_order[:stream_specifier] @var{integer} (@emph{input/output,video})
Field order

Possible values:
@table @samp
@item progressive

@item tt

@item bb

@item tb

@item bt

@end table
@item -dump_separator[:stream_specifier] @var{string} (@emph{input/output,audio,video,subtitles})
set information dump field separator
@item -codec_whitelist[:stream_specifier] @var{string} (@emph{input,audio,video,subtitles})
List of decoders that are allowed to be used
@item -max_pixels[:stream_specifier] @var{integer} (@emph{input/output,audio,video,subtitles})
Maximum number of pixels
@item -max_samples[:stream_specifier] @var{integer} (@emph{input/output,audio})
Maximum number of samples
@item -hwaccel_flags[:stream_specifier] @var{flags} (@emph{input,video})

Possible values:
@table @samp
@item ignore_level
ignore level even if the codec level used is unknown or higher than the maximum supported level reported by the hardware driver
@item allow_high_depth
allow to output YUV pixel formats with a different chroma sampling than 4:2:0 and/or other than 8 bits per component
@item allow_profile_mismatch
attempt to decode anyway if HW accelerated decoder's supported profiles do not exactly match the stream
@item unsafe_output
allow potentially unsafe hwaccel frame output that might require special care to process successfully
@end table
@item -extra_hw_frames[:stream_specifier] @var{integer} (@emph{input,video})
Number of extra hardware frames to allocate for the user
@item -discard_damaged_percentage[:stream_specifier] @var{integer} (@emph{input,video})
Percentage of damaged samples to discard a frame
@item -side_data_prefer_packet[:stream_specifier] @var{value} (@emph{input,audio,video,subtitles})
Comma-separated list of side data types for which user-supplied (container) data is preferred over coded bytestream

Possible values:
@table @samp
@item replaygain

@item displaymatrix

@item spherical

@item stereo3d

@item audio_service_type

@item mastering_display_metadata

@item content_light_level

@item icc_profile

@end table
@end table
