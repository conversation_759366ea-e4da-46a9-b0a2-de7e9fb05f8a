=encoding utf8

=head1 NAME

libavformat - multimedia muxing and demuxing library

=head1 DESCRIPTION


The libavformat library provides a generic framework for multiplexing
and demultiplexing (muxing and demuxing) audio, video and subtitle
streams. It encompasses multiple muxers and demuxers for multimedia
container formats.

It also supports several input and output protocols to access a media
resource.



=head1 SEE ALSO



ffmpeg(1), ffplay(1), ffprobe(1),
ffmpeg-formats(1), ffmpeg-protocols(1),
libavutil(3), libavcodec(3)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



