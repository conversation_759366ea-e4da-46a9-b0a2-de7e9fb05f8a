=encoding utf8

=head1 NAME

ffmpeg-utils - FFmpeg utilities

=head1 DESCRIPTION


This document describes some generic features and utilities provided
by the libavutil library.



=head1 SYNTAX


This section documents the syntax and formats employed by the FFmpeg
libraries and tools.



=head2 Quoting and escaping


FFmpeg adopts the following quoting and escaping mechanism, unless
explicitly specified. The following rules are applied:


=over 4


=item *

B<'> and B<\> are special characters (respectively used for
quoting and escaping). In addition to them, there might be other
special characters depending on the specific syntax where the escaping
and quoting are employed.


=item *

A special character is escaped by prefixing it with a B<\>.


=item *

All characters enclosed between B<''> are included literally in the
parsed string. The quote character B<'> itself cannot be quoted,
so you may need to close the quote and escape it.


=item *

Leading and trailing whitespaces, unless escaped or quoted, are
removed from the parsed string.

=back


Note that you may need to add a second level of escaping when using
the command line or a script, which depends on the syntax of the
adopted shell language.

The function C<av_get_token> defined in
F<libavutil/avstring.h> can be used to parse a token quoted or
escaped according to the rules defined above.

The tool F<tools/ffescape> in the FFmpeg source tree can be used
to automatically quote or escape a string in a script.


=head3 Examples



=over 4


=item *

Escape the string C<Crime d'Amour> containing the C<'> special
character:
	
	Crime d\'Amour



=item *

The string above contains a quote, so the C<'> needs to be escaped
when quoting it:
	
	'Crime d'\''Amour'



=item *

Include leading or trailing whitespaces using quoting:
	
	'  this string starts and ends with whitespaces  '



=item *

Escaping and quoting can be mixed together:
	
	' The string '\'string\'' is a string '



=item *

To include a literal B<\> you can use either escaping or quoting:
	
	'c:\foo' can be written as c:\\foo


=back




=head2 Date


The accepted syntax is:
	
	[(YYYY-MM-DD|YYYYMMDD)[T|t| ]]((HH:MM:SS[.m...]]])|(HHMMSS[.m...]]]))[Z]
	now


If the value is "now" it takes the current time.

Time is local time unless Z is appended, in which case it is
interpreted as UTC.
If the year-month-day part is not specified it takes the current
year-month-day.



=head2 Time duration


There are two accepted syntaxes for expressing time duration.

	
	[-][<HH>:]<MM>:<SS>[.<m>...]


I<HH> expresses the number of hours, I<MM> the number of minutes
for a maximum of 2 digits, and I<SS> the number of seconds for a
maximum of 2 digits. The I<m> at the end expresses decimal value for
I<SS>.

I<or>

	
	[-]<S>+[.<m>...][s|ms|us]


I<S> expresses the number of seconds, with the optional decimal part
I<m>.  The optional literal suffixes B<s>, B<ms> or B<us>
indicate to interpret the value as seconds, milliseconds or microseconds,
respectively.

In both expressions, the optional B<-> indicates negative duration.


=head3 Examples


The following examples are all valid time duration:


=over 4


=item B<55>

55 seconds


=item B<0.2>

0.2 seconds


=item B<200ms>

200 milliseconds, that's 0.2s


=item B<200000us>

200000 microseconds, that's 0.2s


=item B<12:03:45>

12 hours, 03 minutes and 45 seconds


=item B<23.189>

23.189 seconds

=back




=head2 Video size

Specify the size of the sourced video, it may be a string of the form
I<width>xI<height>, or the name of a size abbreviation.

The following abbreviations are recognized:

=over 4


=item B<ntsc>

720x480

=item B<pal>

720x576

=item B<qntsc>

352x240

=item B<qpal>

352x288

=item B<sntsc>

640x480

=item B<spal>

768x576

=item B<film>

352x240

=item B<ntsc-film>

352x240

=item B<sqcif>

128x96

=item B<qcif>

176x144

=item B<cif>

352x288

=item B<4cif>

704x576

=item B<16cif>

1408x1152

=item B<qqvga>

160x120

=item B<qvga>

320x240

=item B<vga>

640x480

=item B<svga>

800x600

=item B<xga>

1024x768

=item B<uxga>

1600x1200

=item B<qxga>

2048x1536

=item B<sxga>

1280x1024

=item B<qsxga>

2560x2048

=item B<hsxga>

5120x4096

=item B<wvga>

852x480

=item B<wxga>

1366x768

=item B<wsxga>

1600x1024

=item B<wuxga>

1920x1200

=item B<woxga>

2560x1600

=item B<wqsxga>

3200x2048

=item B<wquxga>

3840x2400

=item B<whsxga>

6400x4096

=item B<whuxga>

7680x4800

=item B<cga>

320x200

=item B<ega>

640x350

=item B<hd480>

852x480

=item B<hd720>

1280x720

=item B<hd1080>

1920x1080

=item B<2k>

2048x1080

=item B<2kflat>

1998x1080

=item B<2kscope>

2048x858

=item B<4k>

4096x2160

=item B<4kflat>

3996x2160

=item B<4kscope>

4096x1716

=item B<nhd>

640x360

=item B<hqvga>

240x160

=item B<wqvga>

400x240

=item B<fwqvga>

432x240

=item B<hvga>

480x320

=item B<qhd>

960x540

=item B<2kdci>

2048x1080

=item B<4kdci>

4096x2160

=item B<uhd2160>

3840x2160

=item B<uhd4320>

7680x4320

=back




=head2 Video rate


Specify the frame rate of a video, expressed as the number of frames
generated per second. It has to be a string in the format
I<frame_rate_num>/I<frame_rate_den>, an integer number, a float
number or a valid video frame rate abbreviation.

The following abbreviations are recognized:

=over 4


=item B<ntsc>

30000/1001

=item B<pal>

25/1

=item B<qntsc>

30000/1001

=item B<qpal>

25/1

=item B<sntsc>

30000/1001

=item B<spal>

25/1

=item B<film>

24/1

=item B<ntsc-film>

24000/1001

=back




=head2 Ratio


A ratio can be expressed as an expression, or in the form
I<numerator>:I<denominator>.

Note that a ratio with infinite (1/0) or negative value is
considered valid, so you should check on the returned value if you
want to exclude those values.

The undefined value can be expressed using the "0:0" string.



=head2 Color


It can be the name of a color as defined below (case insensitive match) or a
C<[0x|#]RRGGBB[AA]> sequence, possibly followed by @ and a string
representing the alpha component.

The alpha component may be a string composed by "0x" followed by an
hexadecimal number or a decimal number between 0.0 and 1.0, which
represents the opacity value (B<0x00> or B<0.0> means completely
transparent, B<0xff> or B<1.0> completely opaque). If the alpha
component is not specified then B<0xff> is assumed.

The string B<random> will result in a random color.

The following names of colors are recognized:

=over 4


=item B<AliceBlue>

0xF0F8FF

=item B<AntiqueWhite>

0xFAEBD7

=item B<Aqua>

0x00FFFF

=item B<Aquamarine>

0x7FFFD4

=item B<Azure>

0xF0FFFF

=item B<Beige>

0xF5F5DC

=item B<Bisque>

0xFFE4C4

=item B<Black>

0x000000

=item B<BlanchedAlmond>

0xFFEBCD

=item B<Blue>

0x0000FF

=item B<BlueViolet>

0x8A2BE2

=item B<Brown>

0xA52A2A

=item B<BurlyWood>

0xDEB887

=item B<CadetBlue>

0x5F9EA0

=item B<Chartreuse>

0x7FFF00

=item B<Chocolate>

0xD2691E

=item B<Coral>

0xFF7F50

=item B<CornflowerBlue>

0x6495ED

=item B<Cornsilk>

0xFFF8DC

=item B<Crimson>

0xDC143C

=item B<Cyan>

0x00FFFF

=item B<DarkBlue>

0x00008B

=item B<DarkCyan>

0x008B8B

=item B<DarkGoldenRod>

0xB8860B

=item B<DarkGray>

0xA9A9A9

=item B<DarkGreen>

0x006400

=item B<DarkKhaki>

0xBDB76B

=item B<DarkMagenta>

0x8B008B

=item B<DarkOliveGreen>

0x556B2F

=item B<Darkorange>

0xFF8C00

=item B<DarkOrchid>

0x9932CC

=item B<DarkRed>

0x8B0000

=item B<DarkSalmon>

0xE9967A

=item B<DarkSeaGreen>

0x8FBC8F

=item B<DarkSlateBlue>

0x483D8B

=item B<DarkSlateGray>

0x2F4F4F

=item B<DarkTurquoise>

0x00CED1

=item B<DarkViolet>

0x9400D3

=item B<DeepPink>

0xFF1493

=item B<DeepSkyBlue>

0x00BFFF

=item B<DimGray>

0x696969

=item B<DodgerBlue>

0x1E90FF

=item B<FireBrick>

0xB22222

=item B<FloralWhite>

0xFFFAF0

=item B<ForestGreen>

0x228B22

=item B<Fuchsia>

0xFF00FF

=item B<Gainsboro>

0xDCDCDC

=item B<GhostWhite>

0xF8F8FF

=item B<Gold>

0xFFD700

=item B<GoldenRod>

0xDAA520

=item B<Gray>

0x808080

=item B<Green>

0x008000

=item B<GreenYellow>

0xADFF2F

=item B<HoneyDew>

0xF0FFF0

=item B<HotPink>

0xFF69B4

=item B<IndianRed>

0xCD5C5C

=item B<Indigo>

0x4B0082

=item B<Ivory>

0xFFFFF0

=item B<Khaki>

0xF0E68C

=item B<Lavender>

0xE6E6FA

=item B<LavenderBlush>

0xFFF0F5

=item B<LawnGreen>

0x7CFC00

=item B<LemonChiffon>

0xFFFACD

=item B<LightBlue>

0xADD8E6

=item B<LightCoral>

0xF08080

=item B<LightCyan>

0xE0FFFF

=item B<LightGoldenRodYellow>

0xFAFAD2

=item B<LightGreen>

0x90EE90

=item B<LightGrey>

0xD3D3D3

=item B<LightPink>

0xFFB6C1

=item B<LightSalmon>

0xFFA07A

=item B<LightSeaGreen>

0x20B2AA

=item B<LightSkyBlue>

0x87CEFA

=item B<LightSlateGray>

0x778899

=item B<LightSteelBlue>

0xB0C4DE

=item B<LightYellow>

0xFFFFE0

=item B<Lime>

0x00FF00

=item B<LimeGreen>

0x32CD32

=item B<Linen>

0xFAF0E6

=item B<Magenta>

0xFF00FF

=item B<Maroon>

0x800000

=item B<MediumAquaMarine>

0x66CDAA

=item B<MediumBlue>

0x0000CD

=item B<MediumOrchid>

0xBA55D3

=item B<MediumPurple>

0x9370D8

=item B<MediumSeaGreen>

0x3CB371

=item B<MediumSlateBlue>

0x7B68EE

=item B<MediumSpringGreen>

0x00FA9A

=item B<MediumTurquoise>

0x48D1CC

=item B<MediumVioletRed>

0xC71585

=item B<MidnightBlue>

0x191970

=item B<MintCream>

0xF5FFFA

=item B<MistyRose>

0xFFE4E1

=item B<Moccasin>

0xFFE4B5

=item B<NavajoWhite>

0xFFDEAD

=item B<Navy>

0x000080

=item B<OldLace>

0xFDF5E6

=item B<Olive>

0x808000

=item B<OliveDrab>

0x6B8E23

=item B<Orange>

0xFFA500

=item B<OrangeRed>

0xFF4500

=item B<Orchid>

0xDA70D6

=item B<PaleGoldenRod>

0xEEE8AA

=item B<PaleGreen>

0x98FB98

=item B<PaleTurquoise>

0xAFEEEE

=item B<PaleVioletRed>

0xD87093

=item B<PapayaWhip>

0xFFEFD5

=item B<PeachPuff>

0xFFDAB9

=item B<Peru>

0xCD853F

=item B<Pink>

0xFFC0CB

=item B<Plum>

0xDDA0DD

=item B<PowderBlue>

0xB0E0E6

=item B<Purple>

0x800080

=item B<Red>

0xFF0000

=item B<RosyBrown>

0xBC8F8F

=item B<RoyalBlue>

0x4169E1

=item B<SaddleBrown>

0x8B4513

=item B<Salmon>

0xFA8072

=item B<SandyBrown>

0xF4A460

=item B<SeaGreen>

0x2E8B57

=item B<SeaShell>

0xFFF5EE

=item B<Sienna>

0xA0522D

=item B<Silver>

0xC0C0C0

=item B<SkyBlue>

0x87CEEB

=item B<SlateBlue>

0x6A5ACD

=item B<SlateGray>

0x708090

=item B<Snow>

0xFFFAFA

=item B<SpringGreen>

0x00FF7F

=item B<SteelBlue>

0x4682B4

=item B<Tan>

0xD2B48C

=item B<Teal>

0x008080

=item B<Thistle>

0xD8BFD8

=item B<Tomato>

0xFF6347

=item B<Turquoise>

0x40E0D0

=item B<Violet>

0xEE82EE

=item B<Wheat>

0xF5DEB3

=item B<White>

0xFFFFFF

=item B<WhiteSmoke>

0xF5F5F5

=item B<Yellow>

0xFFFF00

=item B<YellowGreen>

0x9ACD32

=back




=head2 Channel Layout


A channel layout specifies the spatial disposition of the channels in
a multi-channel audio stream. To specify a channel layout, FFmpeg
makes use of a special syntax.

Individual channels are identified by an id, as given by the table
below:

=over 4


=item B<FL>

front left

=item B<FR>

front right

=item B<FC>

front center

=item B<LFE>

low frequency

=item B<BL>

back left

=item B<BR>

back right

=item B<FLC>

front left-of-center

=item B<FRC>

front right-of-center

=item B<BC>

back center

=item B<SL>

side left

=item B<SR>

side right

=item B<TC>

top center

=item B<TFL>

top front left

=item B<TFC>

top front center

=item B<TFR>

top front right

=item B<TBL>

top back left

=item B<TBC>

top back center

=item B<TBR>

top back right

=item B<DL>

downmix left

=item B<DR>

downmix right

=item B<WL>

wide left

=item B<WR>

wide right

=item B<SDL>

surround direct left

=item B<SDR>

surround direct right

=item B<LFE2>

low frequency 2

=back


Standard channel layout compositions can be specified by using the
following identifiers:

=over 4


=item B<mono>

FC

=item B<stereo>

FL+FR

=item B<2.1>

FL+FR+LFE

=item B<3.0>

FL+FR+FC

=item B<3.0(back)>

FL+FR+BC

=item B<4.0>

FL+FR+FC+BC

=item B<quad>

FL+FR+BL+BR

=item B<quad(side)>

FL+FR+SL+SR

=item B<3.1>

FL+FR+FC+LFE

=item B<5.0>

FL+FR+FC+BL+BR

=item B<5.0(side)>

FL+FR+FC+SL+SR

=item B<4.1>

FL+FR+FC+LFE+BC

=item B<5.1>

FL+FR+FC+LFE+BL+BR

=item B<5.1(side)>

FL+FR+FC+LFE+SL+SR

=item B<6.0>

FL+FR+FC+BC+SL+SR

=item B<6.0(front)>

FL+FR+FLC+FRC+SL+SR

=item B<3.1.2>

FL+FR+FC+LFE+TFL+TFR

=item B<hexagonal>

FL+FR+FC+BL+BR+BC

=item B<6.1>

FL+FR+FC+LFE+BC+SL+SR

=item B<6.1>

FL+FR+FC+LFE+BL+BR+BC

=item B<6.1(front)>

FL+FR+LFE+FLC+FRC+SL+SR

=item B<7.0>

FL+FR+FC+BL+BR+SL+SR

=item B<7.0(front)>

FL+FR+FC+FLC+FRC+SL+SR

=item B<7.1>

FL+FR+FC+LFE+BL+BR+SL+SR

=item B<7.1(wide)>

FL+FR+FC+LFE+BL+BR+FLC+FRC

=item B<7.1(wide-side)>

FL+FR+FC+LFE+FLC+FRC+SL+SR

=item B<5.1.2>

FL+FR+FC+LFE+BL+BR+TFL+TFR

=item B<octagonal>

FL+FR+FC+BL+BR+BC+SL+SR

=item B<cube>

FL+FR+BL+BR+TFL+TFR+TBL+TBR

=item B<5.1.4>

FL+FR+FC+LFE+BL+BR+TFL+TFR+TBL+TBR

=item B<7.1.2>

FL+FR+FC+LFE+BL+BR+SL+SR+TFL+TFR

=item B<7.1.4>

FL+FR+FC+LFE+BL+BR+SL+SR+TFL+TFR+TBL+TBR

=item B<7.2.3>

FL+FR+FC+LFE+BL+BR+SL+SR+TFL+TFR+TBC+LFE2

=item B<9.1.4>

FL+FR+FC+LFE+BL+BR+FLC+FRC+SL+SR+TFL+TFR+TBL+TBR

=item B<9.1.6>

FL+FR+FC+LFE+BL+BR+FLC+FRC+SL+SR+TFL+TFR+TBL+TBR+TSL+TSR

=item B<hexadecagonal>

FL+FR+FC+BL+BR+BC+SL+SR+WL+WR+TBL+TBR+TBC+TFC+TFL+TFR

=item B<binaural>

BIL+BIR

=item B<downmix>

DL+DR

=item B<22.2>

FL+FR+FC+LFE+BL+BR+FLC+FRC+BC+SL+SR+TC+TFL+TFC+TFR+TBL+TBC+TBR+LFE2+TSL+TSR+BFC+BFL+BFR

=back


A custom channel layout can be specified as a sequence of terms, separated by '+'.
Each term can be:

=over 4


=item *

the name of a single channel (e.g. B<FL>, B<FR>, B<FC>, B<LFE>, etc.),
each optionally containing a custom name after a '@', (e.g. B<FL@Left>,
B<FR@Right>, B<FC@Center>, B<LFE@Low_Frequency>, etc.)

=back


A standard channel layout can be specified by the following:

=over 4


=item *

the name of a single channel (e.g. B<FL>, B<FR>, B<FC>, B<LFE>, etc.)


=item *

the name of a standard channel layout (e.g. B<mono>,
B<stereo>, B<4.0>, B<quad>, B<5.0>, etc.)


=item *

a number of channels, in decimal, followed by 'c', yielding the default channel
layout for that number of channels (see the function
C<av_channel_layout_default>). Note that not all channel counts have a
default layout.


=item *

a number of channels, in decimal, followed by 'C', yielding an unknown channel
layout with the specified number of channels. Note that not all channel layout
specification strings support unknown channel layouts.


=item *

a channel layout mask, in hexadecimal starting with "0x" (see the
C<AV_CH_*> macros in F<libavutil/channel_layout.h>.

=back


Before libavutil version 53 the trailing character "c" to specify a number of
channels was optional, but now it is required, while a channel layout mask can
also be specified as a decimal number (if and only if not followed by "c" or "C").

See also the function C<av_channel_layout_from_string> defined in
F<libavutil/channel_layout.h>.


=head1 EXPRESSION EVALUATION


When evaluating an arithmetic expression, FFmpeg uses an internal
formula evaluator, implemented through the F<libavutil/eval.h>
interface.

An expression may contain unary, binary operators, constants, and
functions.

Two expressions I<expr1> and I<expr2> can be combined to form
another expression "I<expr1>;I<expr2>".
I<expr1> and I<expr2> are evaluated in turn, and the new
expression evaluates to the value of I<expr2>.

The following binary operators are available: C<+>, C<->,
C<*>, C</>, C<^>.

The following unary operators are available: C<+>, C<->.

Some internal variables can be used to store and load intermediary
results. They can be accessed using the C<ld> and C<st>
functions with an index argument varying from 0 to 9 to specify which
internal variable to access.

The following functions are available:

=over 4


=item B<abs(x)>

Compute absolute value of I<x>.


=item B<acos(x)>

Compute arccosine of I<x>.


=item B<asin(x)>

Compute arcsine of I<x>.


=item B<atan(x)>

Compute arctangent of I<x>.


=item B<atan2(y, x)>

Compute principal value of the arc tangent of I<y>/I<x>.


=item B<between(x, min, max)>

Return 1 if I<x> is greater than or equal to I<min> and lesser than or
equal to I<max>, 0 otherwise.


=item B<bitand(x, y)>


=item B<bitor(x, y)>

Compute bitwise and/or operation on I<x> and I<y>.

The results of the evaluation of I<x> and I<y> are converted to
integers before executing the bitwise operation.

Note that both the conversion to integer and the conversion back to
floating point can lose precision. Beware of unexpected results for
large numbers (usually 2^53 and larger).


=item B<ceil(expr)>

Round the value of expression I<expr> upwards to the nearest
integer. For example, "ceil(1.5)" is "2.0".


=item B<clip(x, min, max)>

Return the value of I<x> clipped between I<min> and I<max>.


=item B<cos(x)>

Compute cosine of I<x>.


=item B<cosh(x)>

Compute hyperbolic cosine of I<x>.


=item B<eq(x, y)>

Return 1 if I<x> and I<y> are equivalent, 0 otherwise.


=item B<exp(x)>

Compute exponential of I<x> (with base C<e>, the Euler's number).


=item B<floor(expr)>

Round the value of expression I<expr> downwards to the nearest
integer. For example, "floor(-1.5)" is "-2.0".


=item B<gauss(x)>

Compute Gauss function of I<x>, corresponding to
C<exp(-x*x/2) / sqrt(2*PI)>.


=item B<gcd(x, y)>

Return the greatest common divisor of I<x> and I<y>. If both I<x> and
I<y> are 0 or either or both are less than zero then behavior is undefined.


=item B<gt(x, y)>

Return 1 if I<x> is greater than I<y>, 0 otherwise.


=item B<gte(x, y)>

Return 1 if I<x> is greater than or equal to I<y>, 0 otherwise.


=item B<hypot(x, y)>

This function is similar to the C function with the same name; it returns
"sqrt(I<x>*I<x> + I<y>*I<y>)", the length of the hypotenuse of a
right triangle with sides of length I<x> and I<y>, or the distance of the
point (I<x>, I<y>) from the origin.


=item B<if(x, y)>

Evaluate I<x>, and if the result is non-zero return the result of
the evaluation of I<y>, return 0 otherwise.


=item B<if(x, y, z)>

Evaluate I<x>, and if the result is non-zero return the evaluation
result of I<y>, otherwise the evaluation result of I<z>.


=item B<ifnot(x, y)>

Evaluate I<x>, and if the result is zero return the result of the
evaluation of I<y>, return 0 otherwise.


=item B<ifnot(x, y, z)>

Evaluate I<x>, and if the result is zero return the evaluation
result of I<y>, otherwise the evaluation result of I<z>.


=item B<isinf(x)>

Return 1.0 if I<x> is +/-INFINITY, 0.0 otherwise.


=item B<isnan(x)>

Return 1.0 if I<x> is NAN, 0.0 otherwise.


=item B<ld(idx)>

Load the value of the internal variable with index I<idx>, which was
previously stored with st(I<idx>, I<expr>).
The function returns the loaded value.


=item B<lerp(x, y, z)>

Return linear interpolation between I<x> and I<y> by amount of I<z>.


=item B<log(x)>

Compute natural logarithm of I<x>.


=item B<lt(x, y)>

Return 1 if I<x> is lesser than I<y>, 0 otherwise.


=item B<lte(x, y)>

Return 1 if I<x> is lesser than or equal to I<y>, 0 otherwise.


=item B<max(x, y)>

Return the maximum between I<x> and I<y>.


=item B<min(x, y)>

Return the minimum between I<x> and I<y>.


=item B<mod(x, y)>

Compute the remainder of division of I<x> by I<y>.


=item B<not(expr)>

Return 1.0 if I<expr> is zero, 0.0 otherwise.


=item B<pow(x, y)>

Compute the power of I<x> elevated I<y>, it is equivalent to
"(I<x>)^(I<y>)".


=item B<print(t)>


=item B<print(t, l)>

Print the value of expression I<t> with loglevel I<l>. If I<l> is not
specified then a default log level is used.
Return the value of the expression printed.


=item B<random(idx)>

Return a pseudo random value between 0.0 and 1.0. I<idx> is the
index of the internal variable used to save the seed/state, which can be
previously stored with C<st(idx)>.

To initialize the seed, you need to store the seed value as a 64-bit
unsigned integer in the internal variable with index I<idx>.

For example, to store the seed with value C<42> in the internal
variable with index C<0> and print a few random values:
	
	st(0,42); print(random(0)); print(random(0)); print(random(0))



=item B<randomi(idx, min, max)>

Return a pseudo random value in the interval between I<min> and
I<max>. I<idx> is the index of the internal variable which will be used to
save the seed/state, which can be previously stored with C<st(idx)>.

To initialize the seed, you need to store the seed value as a 64-bit
unsigned integer in the internal variable with index I<idx>.


=item B<root(expr, max)>

Find an input value for which the function represented by I<expr>
with argument I<ld(0)> is 0 in the interval 0..I<max>.

The expression in I<expr> must denote a continuous function or the
result is undefined.

I<ld(0)> is used to represent the function input value, which means that the
given expression will be evaluated multiple times with various input values that
the expression can access through C<ld(0)>. When the expression evaluates to
0 then the corresponding input value will be returned.


=item B<round(expr)>

Round the value of expression I<expr> to the nearest integer. For example,
"round(1.5)" is "2.0".


=item B<sgn(x)>

Compute sign of I<x>.


=item B<sin(x)>

Compute sine of I<x>.


=item B<sinh(x)>

Compute hyperbolic sine of I<x>.


=item B<sqrt(expr)>

Compute the square root of I<expr>. This is equivalent to
"(I<expr>)^.5".


=item B<squish(x)>

Compute expression C<1/(1 + exp(4*x))>.


=item B<st(idx, expr)>

Store the value of the expression I<expr> in an internal
variable. I<idx> specifies the index of the variable where to store
the value, and it is a value ranging from 0 to 9. The function returns
the value stored in the internal variable.

The stored value can be retrieved with C<ld(var)>.

Note: variables are currently not shared between expressions.


=item B<tan(x)>

Compute tangent of I<x>.


=item B<tanh(x)>

Compute hyperbolic tangent of I<x>.


=item B<taylor(expr, x)>


=item B<taylor(expr, x, idx)>

Evaluate a Taylor series at I<x>, given an expression representing
the C<ld(idx)>-th derivative of a function at 0.

When the series does not converge the result is undefined.

I<ld(idx)> is used to represent the derivative order in I<expr>,
which means that the given expression will be evaluated multiple times
with various input values that the expression can access through
C<ld(idx)>. If I<idx> is not specified then 0 is assumed.

Note, when you have the derivatives at y instead of 0,
C<taylor(expr, x-y)> can be used.


=item B<time(0)>

Return the current (wallclock) time in seconds.


=item B<trunc(expr)>

Round the value of expression I<expr> towards zero to the nearest
integer. For example, "trunc(-1.5)" is "-1.0".


=item B<while(cond, expr)>

Evaluate expression I<expr> while the expression I<cond> is
non-zero, and returns the value of the last I<expr> evaluation, or
NAN if I<cond> was always false.

=back


The following constants are available:

=over 4


=item B<PI>

area of the unit disc, approximately 3.14

=item B<E>

exp(1) (Euler's number), approximately 2.718

=item B<PHI>

golden ratio (1+sqrt(5))/2, approximately 1.618

=back


Assuming that an expression is considered "true" if it has a non-zero
value, note that:

C<*> works like AND

C<+> works like OR

For example the construct:
	
	if (A AND B) then C

is equivalent to:
	
	if(A*B, C)


In your C code, you can extend the list of unary and binary functions,
and define recognized constants, so that they are available for your
expressions.

The evaluator also recognizes the International System unit prefixes.
If 'i' is appended after the prefix, binary prefixes are used, which
are based on powers of 1024 instead of powers of 1000.
The 'B' postfix multiplies the value by 8, and can be appended after a
unit prefix or used alone. This allows using for example 'KB', 'MiB',
'G' and 'B' as number postfix.

The list of available International System prefixes follows, with
indication of the corresponding powers of 10 and of 2.

=over 4


=item B<y>

10^-24 / 2^-80

=item B<z>

10^-21 / 2^-70

=item B<a>

10^-18 / 2^-60

=item B<f>

10^-15 / 2^-50

=item B<p>

10^-12 / 2^-40

=item B<n>

10^-9 / 2^-30

=item B<u>

10^-6 / 2^-20

=item B<m>

10^-3 / 2^-10

=item B<c>

10^-2

=item B<d>

10^-1

=item B<h>

10^2

=item B<k>

10^3 / 2^10

=item B<K>

10^3 / 2^10

=item B<M>

10^6 / 2^20

=item B<G>

10^9 / 2^30

=item B<T>

10^12 / 2^40

=item B<P>

10^15 / 2^50

=item B<E>

10^18 / 2^60

=item B<Z>

10^21 / 2^70

=item B<Y>

10^24 / 2^80

=back




=head1 SEE ALSO



ffmpeg(1), ffplay(1), ffprobe(1), libavutil(3)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



