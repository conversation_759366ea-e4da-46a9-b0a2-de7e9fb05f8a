=encoding utf8

=head1 NAME

libavdevice - multimedia device handling library

=head1 DESCRIPTION


The libavdevice library provides a generic framework for grabbing from
and rendering to many common multimedia input/output devices, and
supports several input and output devices, including Video4Linux2,
VfW, DShow, and ALSA.



=head1 SEE ALSO



ffmpeg(1), ffplay(1), ffprobe(1),
ffmpeg-devices(1),
libavutil(3), libavcodec(3), libavformat(3)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



