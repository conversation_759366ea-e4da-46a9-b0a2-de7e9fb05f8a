=encoding utf8

=head1 NAME

libavcodec - media streams decoding and encoding library

=head1 DESCRIPTION


The libavcodec library provides a generic encoding/decoding framework
and contains multiple decoders and encoders for audio, video and
subtitle streams, and several bitstream filters.

The shared architecture provides various services ranging from bit
stream I/O to DSP optimizations, and makes it suitable for
implementing robust and fast codecs as well as for experimentation.



=head1 SEE ALSO



ffmpeg(1), ffplay(1), ffprobe(1),
ffmpeg-codecs(1), ffmpeg-bitstream-filters(1),
libavutil(3)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



