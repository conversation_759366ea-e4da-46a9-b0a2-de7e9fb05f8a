=encoding utf8

=head1 NAME

libavfilter - multimedia filtering library

=head1 DESCRIPTION


The libavfilter library provides a generic audio/video filtering
framework containing several filters, sources and sinks.



=head1 SEE ALSO



ffmpeg(1), ffplay(1), ffprobe(1),
ffmpeg-filters(1),
libavutil(3), libswscale(3), libswresample(3), libavcodec(3), libavformat(3), libavdevice(3)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



