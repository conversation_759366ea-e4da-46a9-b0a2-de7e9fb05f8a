=encoding utf8

=head1 NAME

ffmpeg-bitstream-filters - FFmpeg bitstream filters

=head1 DESCRIPTION


This document describes the bitstream filters provided by the
libavcodec library.

A bitstream filter operates on the encoded stream data, and performs
bitstream level modifications without performing decoding.



=head1 BITSTREAM FILTERS


When you configure your FFmpeg build, all the supported bitstream
filters are enabled by default. You can list all available ones using
the configure option C<--list-bsfs>.

You can disable all the bitstream filters using the configure option
C<--disable-bsfs>, and selectively enable any bitstream filter using
the option C<--enable-bsf=BSF>, or you can disable a particular
bitstream filter using the option C<--disable-bsf=BSF>.

The option C<-bsfs> of the ff* tools will display the list of
all the supported bitstream filters included in your build.

The ff* tools have a -bsf option applied per stream, taking a
comma-separated list of filters, whose parameters follow the filter
name after a '='.

	
	ffmpeg -i INPUT -c:v copy -bsf:v filter1[=opt1=str1:opt2=str2][,filter2] OUTPUT


Below is a description of the currently available bitstream filters,
with their parameters, if any.


=head2 aac_adtstoasc


Convert MPEG-2/4 AAC ADTS to an MPEG-4 Audio Specific Configuration
bitstream.

This filter creates an MPEG-4 AudioSpecificConfig from an MPEG-2/4
ADTS header and removes the ADTS header.

This filter is required for example when copying an AAC stream from a
raw ADTS AAC or an MPEG-TS container to MP4A-LATM, to an FLV file, or
to MOV/MP4 files and related formats such as 3GP or M4A. Please note
that it is auto-inserted for MP4A-LATM and MOV/MP4 and related formats.


=head2 av1_metadata


Modify metadata embedded in an AV1 stream.


=over 4


=item B<td>

Insert or remove temporal delimiter OBUs in all temporal units of the
stream.


=over 4


=item B<insert>

Insert a TD at the beginning of every TU which does not already have one.

=item B<remove>

Remove the TD from the beginning of every TU which has one.

=back



=item B<color_primaries>


=item B<transfer_characteristics>


=item B<matrix_coefficients>

Set the color description fields in the stream (see AV1 section 6.4.2).


=item B<color_range>

Set the color range in the stream (see AV1 section 6.4.2; note that
this cannot be set for streams using BT.709 primaries, sRGB transfer
characteristic and identity (RGB) matrix coefficients).

=over 4


=item B<tv>

Limited range.

=item B<pc>

Full range.

=back



=item B<chroma_sample_position>

Set the chroma sample location in the stream (see AV1 section 6.4.2).
This can only be set for 4:2:0 streams.


=over 4


=item B<vertical>

Left position (matching the default in MPEG-2 and H.264).

=item B<colocated>

Top-left position.

=back



=item B<tick_rate>

Set the tick rate (I<time_scale / num_units_in_display_tick>) in
the timing info in the sequence header.

=item B<num_ticks_per_picture>

Set the number of ticks in each picture, to indicate that the stream
has a fixed framerate.  Ignored if B<tick_rate> is not also set.


=item B<delete_padding>

Deletes Padding OBUs.


=back



=head2 chomp


Remove zero padding at the end of a packet.


=head2 dca_core


Extract the core from a DCA/DTS stream, dropping extensions such as
DTS-HD.


=head2 dovi_rpu


Manipulate Dolby Vision metadata in a HEVC/AV1 bitstream, optionally enabling
metadata compression.


=over 4


=item B<strip>

If enabled, strip all Dolby Vision metadata (configuration record + RPU data
blocks) from the stream.

=item B<compression>

Which compression level to enable.

=over 4


=item B<none>

No metadata compression.

=item B<limited>

Limited metadata compression scheme. Should be compatible with most devices.
This is the default.

=item B<extended>

Extended metadata compression. Devices are not required to support this. Note
that this level currently behaves the same as B<limited> in libavcodec.

=back


=back



=head2 dump_extra


Add extradata to the beginning of the filtered packets except when
said packets already exactly begin with the extradata that is intended
to be added.


=over 4


=item B<freq>

The additional argument specifies which packets should be filtered.
It accepts the values:

=over 4


=item B<k>


=item B<keyframe>

add extradata to all key packets


=item B<e>


=item B<all>

add extradata to all packets

=back


=back


If not specified it is assumed B<k>.

For example the following B<ffmpeg> command forces a global
header (thus disabling individual packet headers) in the H.264 packets
generated by the C<libx264> encoder, but corrects them by adding
the header stored in extradata to the key packets:
	
	ffmpeg -i INPUT -map 0 -flags:v +global_header -c:v libx264 -bsf:v dump_extra out.ts



=head2 dv_error_marker


Blocks in DV which are marked as damaged are replaced by blocks of the specified color.


=over 4


=item B<color>

The color to replace damaged blocks by

=item B<sta>

A 16 bit mask which specifies which of the 16 possible error status values are
to be replaced by colored blocks. 0xFFFE is the default which replaces all non 0
error status values.

=over 4


=item B<ok>

No error, no concealment

=item B<err>

Error, No concealment

=item B<res>

Reserved

=item B<notok>

Error or concealment

=item B<notres>

Not reserved

=item B<Aa, Ba, Ca, Ab, Bb, Cb, A, B, C, a, b, erri, erru>

The specific error status code

=back

see page 44-46 or section 5.5 of
E<lt>B<http://web.archive.org/web/20060927044735/http://www.smpte.org/smpte_store/standards/pdf/s314m.pdf>E<gt>


=back



=head2 eac3_core


Extract the core from a E-AC-3 stream, dropping extra channels.


=head2 eia608_to_smpte436m


Convert from a C<EIA_608> stream to a C<SMPTE_436M_ANC> data stream, wrapping the closed captions in CTA-708 CDP VANC packets.


=over 4


=item B<line_number>

Choose which line number the generated VANC packets should go on. You generally want either line 9 (the default) or 11.

=item B<wrapping_type>

Choose the SMPTE 436M wrapping type, defaults to B<vanc_frame>.
It accepts the values:

=over 4


=item B<vanc_frame>

VANC frame (interlaced or segmented progressive frame)

=item B<vanc_field_1>


=item B<vanc_field_2>


=item B<vanc_progressive_frame>


=back


=item B<sample_coding>

Choose the SMPTE 436M sample coding, defaults to B<8bit_luma>.
It accepts the values:

=over 4


=item B<8bit_luma>

8-bit component luma samples

=item B<8bit_color_diff>

8-bit component color difference samples

=item B<8bit_luma_and_color_diff>

8-bit component luma and color difference samples

=item B<10bit_luma>

10-bit component luma samples

=item B<10bit_color_diff>

10-bit component color difference samples

=item B<10bit_luma_and_color_diff>

10-bit component luma and color difference samples

=item B<8bit_luma_parity_error>

8-bit component luma samples with parity error

=item B<8bit_color_diff_parity_error>

8-bit component color difference samples with parity error

=item B<8bit_luma_and_color_diff_parity_error>

8-bit component luma and color difference samples with parity error

=back


=item B<initial_cdp_sequence_cntr>

The initial value of the CDP's 16-bit unsigned integer C<cdp_hdr_sequence_cntr> and C<cdp_ftr_sequence_cntr> fields. Defaults to 0.

=item B<cdp_frame_rate>

Set the CDP's C<cdp_frame_rate> field. This doesn't actually change the timing of the data stream, it just changes the values inserted in that field in the generated CDP packets. Defaults to B<30000/1001>.

=back



=head2 extract_extradata


Extract the in-band extradata.

Certain codecs allow the long-term headers (e.g. MPEG-2 sequence headers,
or H.264/HEVC (VPS/)SPS/PPS) to be transmitted either "in-band" (i.e. as a part
of the bitstream containing the coded frames) or "out of band" (e.g. on the
container level). This latter form is called "extradata" in FFmpeg terminology.

This bitstream filter detects the in-band headers and makes them available as
extradata.


=over 4


=item B<remove>

When this option is enabled, the long-term headers are removed from the
bitstream after extraction.

=back



=head2 filter_units


Remove units with types in or not in a given set from the stream.


=over 4


=item B<pass_types>

List of unit types or ranges of unit types to pass through while removing
all others.  This is specified as a '|'-separated list of unit type values
or ranges of values with '-'.


=item B<remove_types>

Identical to B<pass_types>, except the units in the given set
removed and all others passed through.

=back


The types used by pass_types and remove_types correspond to NAL unit types
(nal_unit_type) in H.264, HEVC and H.266 (see Table 7-1 in the H.264
and HEVC specifications or Table 5 in the H.266 specification), to
marker values for JPEG (without 0xFF prefix) and to start codes without
start code prefix (i.e. the byte following the 0x000001) for MPEG-2.
For VP8 and VP9, every unit has type zero.

Extradata is unchanged by this transformation, but note that if the stream
contains inline parameter sets then the output may be unusable if they are
removed.

For example, to remove all non-VCL NAL units from an H.264 stream:
	
	ffmpeg -i INPUT -c:v copy -bsf:v 'filter_units=pass_types=1-5' OUTPUT


To remove all AUDs, SEI and filler from an H.265 stream:
	
	ffmpeg -i INPUT -c:v copy -bsf:v 'filter_units=remove_types=35|38-40' OUTPUT


To remove all user data from a MPEG-2 stream, including Closed Captions:
	
	ffmpeg -i INPUT -c:v copy -bsf:v 'filter_units=remove_types=178' OUTPUT


To remove all SEI from a H264 stream, including Closed Captions:
	
	ffmpeg -i INPUT -c:v copy -bsf:v 'filter_units=remove_types=6' OUTPUT


To remove all prefix and suffix SEI from a HEVC stream, including Closed Captions and dynamic HDR:
	
	ffmpeg -i INPUT -c:v copy -bsf:v 'filter_units=remove_types=39|40' OUTPUT



=head2 hapqa_extract


Extract Rgb or Alpha part of an HAPQA file, without recompression, in order to create an HAPQ or an HAPAlphaOnly file.


=over 4


=item B<texture>

Specifies the texture to keep.


=over 4


=item B<color>


=item B<alpha>


=back



=back


Convert HAPQA to HAPQ
	
	ffmpeg -i hapqa_inputfile.mov -c copy -bsf:v hapqa_extract=texture=color -tag:v HapY -metadata:s:v:0 encoder="HAPQ" hapq_file.mov


Convert HAPQA to HAPAlphaOnly
	
	ffmpeg -i hapqa_inputfile.mov -c copy -bsf:v hapqa_extract=texture=alpha -tag:v HapA -metadata:s:v:0 encoder="HAPAlpha Only" hapalphaonly_file.mov



=head2 h264_metadata


Modify metadata embedded in an H.264 stream.


=over 4


=item B<aud>

Insert or remove AUD NAL units in all access units of the stream.


=over 4


=item B<pass>


=item B<insert>


=item B<remove>


=back


Default is pass.


=item B<sample_aspect_ratio>

Set the sample aspect ratio of the stream in the VUI parameters.
See H.264 table E-1.


=item B<overscan_appropriate_flag>

Set whether the stream is suitable for display using overscan
or not (see H.264 section E.2.1).


=item B<video_format>


=item B<video_full_range_flag>

Set the video format in the stream (see H.264 section E.2.1 and
table E-2).


=item B<colour_primaries>


=item B<transfer_characteristics>


=item B<matrix_coefficients>

Set the colour description in the stream (see H.264 section E.2.1
and tables E-3, E-4 and E-5).


=item B<chroma_sample_loc_type>

Set the chroma sample location in the stream (see H.264 section
E.2.1 and figure E-1).


=item B<tick_rate>

Set the tick rate (time_scale / num_units_in_tick) in the VUI
parameters.  This is the smallest time unit representable in the
stream, and in many cases represents the field rate of the stream
(double the frame rate).

=item B<fixed_frame_rate_flag>

Set whether the stream has fixed framerate - typically this indicates
that the framerate is exactly half the tick rate, but the exact
meaning is dependent on interlacing and the picture structure (see
H.264 section E.2.1 and table E-6).

=item B<zero_new_constraint_set_flags>

Zero constraint_set4_flag and constraint_set5_flag in the SPS. These
bits were reserved in a previous version of the H.264 spec, and thus
some hardware decoders require these to be zero. The result of zeroing
this is still a valid bitstream.


=item B<crop_left>


=item B<crop_right>


=item B<crop_top>


=item B<crop_bottom>

Set the frame cropping offsets in the SPS.  These values will replace
the current ones if the stream is already cropped.

These fields are set in pixels.  Note that some sizes may not be
representable if the chroma is subsampled or the stream is interlaced
(see H.264 section 7.4.2.1.1).


=item B<sei_user_data>

Insert a string as SEI unregistered user data.  The argument must
be of the form I<UUID+string>, where the UUID is as hex digits
possibly separated by hyphens, and the string can be anything.

For example, B<086f3693-b7b3-4f2c-9653-21492feee5b8+hello> will
insert the string ``hello'' associated with the given UUID.


=item B<delete_filler>

Deletes both filler NAL units and filler SEI messages.


=item B<display_orientation>

Insert, extract or remove Display orientation SEI messages.
See H.264 section D.1.27 and D.2.27 for syntax and semantics.


=over 4


=item B<pass>


=item B<insert>


=item B<remove>


=item B<extract>


=back


Default is pass.

Insert mode works in conjunction with C<rotate> and C<flip> options.
Any pre-existing Display orientation messages will be removed in insert or remove mode.
Extract mode attaches the display matrix to the packet as side data.


=item B<rotate>

Set rotation in display orientation SEI (anticlockwise angle in degrees).
Range is -360 to +360. Default is NaN.


=item B<flip>

Set flip in display orientation SEI.


=over 4


=item B<horizontal>


=item B<vertical>


=back


Default is unset.


=item B<level>

Set the level in the SPS.  Refer to H.264 section A.3 and tables A-1
to A-5.

The argument must be the name of a level (for example, B<4.2>), a
level_idc value (for example, B<42>), or the special name B<auto>
indicating that the filter should attempt to guess the level from the
input stream properties.


=back



=head2 h264_mp4toannexb


Convert an H.264 bitstream from length prefixed mode to start code
prefixed mode (as defined in the Annex B of the ITU-T H.264
specification).

This is required by some streaming formats, typically the MPEG-2
transport stream format (muxer C<mpegts>).

For example to remux an MP4 file containing an H.264 stream to mpegts
format with B<ffmpeg>, you can use the command:

	
	ffmpeg -i INPUT.mp4 -codec copy -bsf:v h264_mp4toannexb OUTPUT.ts


Please note that this filter is auto-inserted for MPEG-TS (muxer
C<mpegts>) and raw H.264 (muxer C<h264>) output formats.


=head2 h264_redundant_pps


This applies a specific fixup to some Blu-ray BDMV H264 streams
which contain redundant PPSs. The PPSs modify irrelevant parameters
of the stream, confusing other transformations which require
the correct extradata.

The encoder used on these impacted streams adds extra PPSs throughout
the stream, varying the initial QP and whether weighted prediction
was enabled. This causes issues after copying the stream into
a global header container, as the starting PPS is not suitable
for the rest of the stream. One side effect, for example,
is seeking will return garbled output until a new PPS appears.

This BSF removes the extra PPSs and rewrites the slice headers
such that the stream uses a single leading PPS in the global header,
which resolves the issue.


=head2 hevc_metadata


Modify metadata embedded in an HEVC stream.


=over 4


=item B<aud>

Insert or remove AUD NAL units in all access units of the stream.


=over 4


=item B<insert>


=item B<remove>


=back



=item B<sample_aspect_ratio>

Set the sample aspect ratio in the stream in the VUI parameters.


=item B<video_format>


=item B<video_full_range_flag>

Set the video format in the stream (see H.265 section E.3.1 and
table E.2).


=item B<colour_primaries>


=item B<transfer_characteristics>


=item B<matrix_coefficients>

Set the colour description in the stream (see H.265 section E.3.1
and tables E.3, E.4 and E.5).


=item B<chroma_sample_loc_type>

Set the chroma sample location in the stream (see H.265 section
E.3.1 and figure E.1).


=item B<tick_rate>

Set the tick rate in the VPS and VUI parameters (time_scale /
num_units_in_tick). Combined with B<num_ticks_poc_diff_one>, this can
set a constant framerate in the stream.  Note that it is likely to be
overridden by container parameters when the stream is in a container.


=item B<num_ticks_poc_diff_one>

Set poc_proportional_to_timing_flag in VPS and VUI and use this value
to set num_ticks_poc_diff_one_minus1 (see H.265 sections ******* and
E.3.1).  Ignored if B<tick_rate> is not also set.


=item B<crop_left>


=item B<crop_right>


=item B<crop_top>


=item B<crop_bottom>

Set the conformance window cropping offsets in the SPS.  These values
will replace the current ones if the stream is already cropped.

These fields are set in pixels.  Note that some sizes may not be
representable if the chroma is subsampled (H.265 section 7.4.3.2.1).


=item B<width>


=item B<height>

Set width and height after crop.


=item B<level>

Set the level in the VPS and SPS.  See H.265 section A.4 and tables
A.6 and A.7.

The argument must be the name of a level (for example, B<5.1>), a
I<general_level_idc> value (for example, B<153> for level 5.1),
or the special name B<auto> indicating that the filter should
attempt to guess the level from the input stream properties.


=back



=head2 hevc_mp4toannexb


Convert an HEVC/H.265 bitstream from length prefixed mode to start code
prefixed mode (as defined in the Annex B of the ITU-T H.265
specification).

This is required by some streaming formats, typically the MPEG-2
transport stream format (muxer C<mpegts>).

For example to remux an MP4 file containing an HEVC stream to mpegts
format with B<ffmpeg>, you can use the command:

	
	ffmpeg -i INPUT.mp4 -codec copy -bsf:v hevc_mp4toannexb OUTPUT.ts


Please note that this filter is auto-inserted for MPEG-TS (muxer
C<mpegts>) and raw HEVC/H.265 (muxer C<h265> or
C<hevc>) output formats.


=head2 imxdump


Modifies the bitstream to fit in MOV and to be usable by the Final Cut
Pro decoder. This filter only applies to the mpeg2video codec, and is
likely not needed for Final Cut Pro 7 and newer with the appropriate
B<-tag:v>.

For example, to remux 30 MB/sec NTSC IMX to MOV:

	
	ffmpeg -i input.mxf -c copy -bsf:v imxdump -tag:v mx3n output.mov



=head2 mjpeg2jpeg


Convert MJPEG/AVI1 packets to full JPEG/JFIF packets.

MJPEG is a video codec wherein each video frame is essentially a
JPEG image. The individual frames can be extracted without loss,
e.g. by

	
	ffmpeg -i ../some_mjpeg.avi -c:v copy frames_%d.jpg


Unfortunately, these chunks are incomplete JPEG images, because
they lack the DHT segment required for decoding. Quoting from
E<lt>B<http://www.digitalpreservation.gov/formats/fdd/fdd000063.shtml>E<gt>:

Avery Lee, writing in the rec.video.desktop newsgroup in 2001,
commented that "MJPEG, or at least the MJPEG in AVIs having the
MJPG fourcc, is restricted JPEG with a fixed -- and *omitted* --
Huffman table. The JPEG must be YCbCr colorspace, it must be 4:2:2,
and it must use basic Huffman encoding, not arithmetic or
progressive. . . . You can indeed extract the MJPEG frames and
decode them with a regular JPEG decoder, but you have to prepend
the DHT segment to them, or else the decoder won't have any idea
how to decompress the data. The exact table necessary is given in
the OpenDML spec."

This bitstream filter patches the header of frames extracted from an MJPEG
stream (carrying the AVI1 header ID and lacking a DHT segment) to
produce fully qualified JPEG images.

	
	ffmpeg -i mjpeg-movie.avi -c:v copy -bsf:v mjpeg2jpeg frame_%d.jpg
	exiftran -i -9 frame*.jpg
	ffmpeg -i frame_%d.jpg -c:v copy rotated.avi



=head2 mjpegadump


Add an MJPEG A header to the bitstream, to enable decoding by
Quicktime.



=head2 mov2textsub


Extract a representable text file from MOV subtitles, stripping the
metadata header from each subtitle packet.

See also the B<text2movsub> filter.


=head2 mpeg2_metadata


Modify metadata embedded in an MPEG-2 stream.


=over 4


=item B<display_aspect_ratio>

Set the display aspect ratio in the stream.

The following fixed values are supported:

=over 4


=item B<4/3>


=item B<16/9>


=item B<221/100>


=back

Any other value will result in square pixels being signalled instead
(see H.262 section 6.3.3 and table 6-3).


=item B<frame_rate>

Set the frame rate in the stream.  This is constructed from a table
of known values combined with a small multiplier and divisor - if
the supplied value is not exactly representable, the nearest
representable value will be used instead (see H.262 section 6.3.3
and table 6-4).


=item B<video_format>

Set the video format in the stream (see H.262 section 6.3.6 and
table 6-6).


=item B<colour_primaries>


=item B<transfer_characteristics>


=item B<matrix_coefficients>

Set the colour description in the stream (see H.262 section 6.3.6
and tables 6-7, 6-8 and 6-9).


=back



=head2 mpeg4_unpack_bframes


Unpack DivX-style packed B-frames.

DivX-style packed B-frames are not valid MPEG-4 and were only a
workaround for the broken Video for Windows subsystem.
They use more space, can cause minor AV sync issues, require more
CPU power to decode (unless the player has some decoded picture queue
to compensate the 2,0,2,0 frame per packet style) and cause
trouble if copied into a standard container like mp4 or mpeg-ps/ts,
because MPEG-4 decoders may not be able to decode them, since they are
not valid MPEG-4.

For example to fix an AVI file containing an MPEG-4 stream with
DivX-style packed B-frames using B<ffmpeg>, you can use the command:

	
	ffmpeg -i INPUT.avi -codec copy -bsf:v mpeg4_unpack_bframes OUTPUT.avi



=head2 noise


Damages the contents of packets or simply drops them without damaging the
container. Can be used for fuzzing or testing error resilience/concealment.

Parameters:

=over 4


=item B<amount>

Accepts an expression whose evaluation per-packet determines how often bytes in that
packet will be modified. A value below 0 will result in a variable frequency.
Default is 0 which results in no modification. However, if neither amount nor drop is specified,
amount will be set to I<-1>. See below for accepted variables.

=item B<drop>

Accepts an expression evaluated per-packet whose value determines whether that packet is dropped.
Evaluation to a positive value results in the packet being dropped. Evaluation to a negative
value results in a variable chance of it being dropped, roughly inverse in proportion to the magnitude
of the value. Default is 0 which results in no drops. See below for accepted variables.

=item B<dropamount>

Accepts a non-negative integer, which assigns a variable chance of it being dropped, roughly inverse
in proportion to the value. Default is 0 which results in no drops. This option is kept for backwards
compatibility and is equivalent to setting drop to a negative value with the same magnitude
i.e. C<dropamount=4> is the same as C<drop=-4>. Ignored if drop is also specified.

=back


Both C<amount> and C<drop> accept expressions containing the following variables:


=over 4


=item B<n>

The index of the packet, starting from zero.

=item B<tb>

The timebase for packet timestamps.

=item B<pts>

Packet presentation timestamp.

=item B<dts>

Packet decoding timestamp.

=item B<nopts>

Constant representing AV_NOPTS_VALUE.

=item B<startpts>

First non-AV_NOPTS_VALUE PTS seen in the stream.

=item B<startdts>

First non-AV_NOPTS_VALUE DTS seen in the stream.

=item B<duration>


=item B<d>

Packet duration, in timebase units.

=item B<pos>

Packet position in input; may be -1 when unknown or not set.

=item B<size>

Packet size, in bytes.

=item B<key>

Whether packet is marked as a keyframe.

=item B<state>

A pseudo random integer, primarily derived from the content of packet payload.

=back



=head3 Examples

Apply modification to every byte but don't drop any packets.
	
	ffmpeg -i INPUT -c copy -bsf noise=1 output.mkv


Drop every video packet not marked as a keyframe after timestamp 30s but do not
modify any of the remaining packets.
	
	ffmpeg -i INPUT -c copy -bsf:v noise=drop='gt(pts*tb\,30)*not(key)' output.mkv


Drop one second of audio every 10 seconds and add some random noise to the rest.
	
	ffmpeg -i INPUT -c copy -bsf:a noise=amount=-1:drop='between(mod(pts*tb\,10)\,9\,10)' output.mkv



=head2 null

This bitstream filter passes the packets through unchanged.


=head2 pcm_rechunk


Repacketize PCM audio to a fixed number of samples per packet or a fixed packet
rate per second. This is similar to the B<asetnsamples audio
filter> but works on audio packets instead of audio frames.


=over 4


=item B<nb_out_samples, n>

Set the number of samples per each output audio packet. The number is intended
as the number of samples I<per each channel>. Default value is 1024.


=item B<pad, p>

If set to 1, the filter will pad the last audio packet with silence, so that it
will contain the same number of samples (or roughly the same number of samples,
see B<frame_rate>) as the previous ones. Default value is 1.


=item B<frame_rate, r>

This option makes the filter output a fixed number of packets per second instead
of a fixed number of samples per packet. If the audio sample rate is not
divisible by the frame rate then the number of samples will not be constant but
will vary slightly so that each packet will start as close to the frame
boundary as possible. Using this option has precedence over B<nb_out_samples>.

=back


You can generate the well known 1602-1601-1602-1601-1602 pattern of 48kHz audio
for NTSC frame rate using the B<frame_rate> option.
	
	ffmpeg -f lavfi -i sine=r=48000:d=1 -c pcm_s16le -bsf pcm_rechunk=r=30000/1001 -f framecrc -



=head2 pgs_frame_merge


Merge a sequence of PGS Subtitle segments ending with an "end of display set"
segment into a single packet.

This is required by some containers that support PGS subtitles
(muxer C<matroska>).


=head2 prores_metadata


Modify color property metadata embedded in prores stream.


=over 4


=item B<color_primaries>

Set the color primaries.
Available values are:


=over 4


=item B<auto>

Keep the same color primaries property (default).


=item B<unknown>


=item B<bt709>


=item B<bt470bg>

BT601 625


=item B<smpte170m>

BT601 525


=item B<bt2020>


=item B<smpte431>

DCI P3


=item B<smpte432>

P3 D65


=back



=item B<transfer_characteristics>

Set the color transfer.
Available values are:


=over 4


=item B<auto>

Keep the same transfer characteristics property (default).


=item B<unknown>


=item B<bt709>

BT 601, BT 709, BT 2020

=item B<smpte2084>

SMPTE ST 2084

=item B<arib-std-b67>

ARIB STD-B67

=back




=item B<matrix_coefficients>

Set the matrix coefficient.
Available values are:


=over 4


=item B<auto>

Keep the same colorspace property (default).


=item B<unknown>


=item B<bt709>


=item B<smpte170m>

BT 601


=item B<bt2020nc>


=back


=back


Set Rec709 colorspace for each frame of the file
	
	ffmpeg -i INPUT -c copy -bsf:v prores_metadata=color_primaries=bt709:color_trc=bt709:colorspace=bt709 output.mov


Set Hybrid Log-Gamma parameters for each frame of the file
	
	ffmpeg -i INPUT -c copy -bsf:v prores_metadata=color_primaries=bt2020:color_trc=arib-std-b67:colorspace=bt2020nc output.mov



=head2 remove_extra


Remove extradata from packets.

It accepts the following parameter:

=over 4


=item B<freq>

Set which frame types to remove extradata from.


=over 4


=item B<k>

Remove extradata from non-keyframes only.


=item B<keyframe>

Remove extradata from keyframes only.


=item B<e, all>

Remove extradata from all frames.


=back


=back



=head2 setts

Set PTS and DTS in packets.

It accepts the following parameters:

=over 4


=item B<ts>


=item B<pts>


=item B<dts>

Set expressions for PTS, DTS or both.

=item B<duration>

Set expression for duration.

=item B<time_base>

Set output time base.

=back


The expressions are evaluated through the eval API and can contain the following
constants:


=over 4


=item B<N>

The count of the input packet. Starting from 0.


=item B<TS>

The demux timestamp in input in case of C<ts> or C<dts> option or presentation
timestamp in case of C<pts> option.


=item B<POS>

The original position in the file of the packet, or undefined if undefined
for the current packet


=item B<DTS>

The demux timestamp in input.


=item B<PTS>

The presentation timestamp in input.


=item B<DURATION>

The duration in input.


=item B<STARTDTS>

The DTS of the first packet.


=item B<STARTPTS>

The PTS of the first packet.


=item B<PREV_INDTS>

The previous input DTS.


=item B<PREV_INPTS>

The previous input PTS.


=item B<PREV_INDURATION>

The previous input duration.


=item B<PREV_OUTDTS>

The previous output DTS.


=item B<PREV_OUTPTS>

The previous output PTS.


=item B<PREV_OUTDURATION>

The previous output duration.


=item B<NEXT_DTS>

The next input DTS.


=item B<NEXT_PTS>

The next input PTS.


=item B<NEXT_DURATION>

The next input duration.


=item B<TB>

The timebase of stream packet belongs.


=item B<TB_OUT>

The output timebase.


=item B<SR>

The sample rate of stream packet belongs.


=item B<NOPTS>

The AV_NOPTS_VALUE constant.

=back


For example, to set PTS equal to DTS (not recommended if B-frames are involved):
	
	ffmpeg -i INPUT -c:a copy -bsf:a setts=pts=DTS out.mkv



=head2 showinfo

Log basic packet information. Mainly useful for testing, debugging,
and development.


=head2 smpte436m_to_eia608


Convert from a C<SMPTE_436M_ANC> data stream to a C<EIA_608> stream,
extracting the closed captions from CTA-708 CDP VANC packets, and ignoring all other data.



=head2 text2movsub


Convert text subtitles to MOV subtitles (as used by the C<mov_text>
codec) with metadata headers.

See also the B<mov2textsub> filter.


=head2 trace_headers


Log trace output containing all syntax elements in the coded stream
headers (everything above the level of individual coded blocks).
This can be useful for debugging low-level stream issues.

Supports AV1, H.264, H.265, (M)JPEG, MPEG-2 and VP9, but depending
on the build only a subset of these may be available.


=head2 truehd_core


Extract the core from a TrueHD stream, dropping ATMOS data.


=head2 vp9_metadata


Modify metadata embedded in a VP9 stream.


=over 4


=item B<color_space>

Set the color space value in the frame header.  Note that any frame
set to RGB will be implicitly set to PC range and that RGB is
incompatible with profiles 0 and 2.

=over 4


=item B<unknown>


=item B<bt601>


=item B<bt709>


=item B<smpte170>


=item B<smpte240>


=item B<bt2020>


=item B<rgb>


=back



=item B<color_range>

Set the color range value in the frame header.  Note that any value
imposed by the color space will take precedence over this value.

=over 4


=item B<tv>


=item B<pc>


=back


=back



=head2 vp9_superframe


Merge VP9 invisible (alt-ref) frames back into VP9 superframes. This
fixes merging of split/segmented VP9 streams where the alt-ref frame
was split from its visible counterpart.


=head2 vp9_superframe_split


Split VP9 superframes into single frames.


=head2 vp9_raw_reorder


Given a VP9 stream with correct timestamps but possibly out of order,
insert additional show-existing-frame packets to correct the ordering.



=head1 SEE ALSO



ffmpeg(1), ffplay(1), ffprobe(1), libavcodec(3)


=head1 AUTHORS


The FFmpeg developers.

For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
B<git log> in the FFmpeg source directory, or browsing the
online repository at E<lt>B<https://git.ffmpeg.org/ffmpeg>E<gt>.

Maintainers for the specific components are listed in the file
F<MAINTAINERS> in the source code tree.



