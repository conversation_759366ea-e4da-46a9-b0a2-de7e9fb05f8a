@c DO NOT EDIT THIS FILE!
@c It was generated by print_options.

@section Format AVOptions
@table @option
@item -avioflags @var{flags} (@emph{input/output})

Possible values:
@table @samp
@item direct
reduce buffering
@end table
@item -probesize @var{integer} (@emph{input})
set probing size
@item -formatprobesize @var{integer} (@emph{input})
number of bytes to probe file format
@item -packetsize @var{integer} (@emph{output})
set packet size
@item -fflags @var{flags} (@emph{input/output})

Possible values:
@table @samp
@item flush_packets
reduce the latency by flushing out packets immediately
@item ignidx
ignore index
@item genpts
generate pts
@item nofillin
do not fill in missing values that can be exactly calculated
@item noparse
disable AVParsers, this needs nofillin too
@item igndts
ignore dts
@item discardcorrupt
discard corrupted frames
@item sortdts
try to interleave outputted packets by dts
@item fastseek
fast but inaccurate seeks
@item nobuffer
reduce the latency introduced by optional buffering
@item bitexact
do not write random/volatile data
@item autobsf
add needed bsfs automatically
@end table
@item -seek2any @var{value} (@emph{input})
allow seeking to non-keyframes on demuxer level when supported
@item -analyzeduration @var{integer} (@emph{input})
specify how many microseconds are analyzed to probe the input
@item -cryptokey @var{hexadecimal string} (@emph{input})
decryption key
@item -indexmem @var{integer} (@emph{input})
max memory used for timestamp index (per stream)
@item -rtbufsize @var{integer} (@emph{input})
max memory used for buffering real-time frames
@item -fdebug @var{flags} (@emph{input/output})
print specific debug info

Possible values:
@table @samp
@item ts

@end table
@item -max_delay @var{integer} (@emph{input/output})
maximum muxing or demuxing delay in microseconds
@item -start_time_realtime @var{integer} (@emph{output})
wall-clock time when stream begins (PTS==0)
@item -fpsprobesize @var{integer} (@emph{input})
number of frames used to probe fps
@item -audio_preload @var{integer} (@emph{output})
microseconds by which audio packets should be interleaved earlier
@item -chunk_duration @var{integer} (@emph{output})
microseconds for each chunk
@item -chunk_size @var{integer} (@emph{output})
size in bytes for each chunk
@item -f_err_detect @var{flags} (@emph{input})
set error detection flags (deprecated; use err_detect, save via avconv)

Possible values:
@table @samp
@item crccheck
verify embedded CRCs
@item bitstream
detect bitstream specification deviations
@item buffer
detect improper bitstream length
@item explode
abort decoding on minor error detection
@item ignore_err
ignore errors
@item careful
consider things that violate the spec, are fast to check and have not been seen in the wild as errors
@item compliant
consider all spec non compliancies as errors
@item aggressive
consider things that a sane encoder shouldn't do as an error
@end table
@item -err_detect @var{flags} (@emph{input})
set error detection flags

Possible values:
@table @samp
@item crccheck
verify embedded CRCs
@item bitstream
detect bitstream specification deviations
@item buffer
detect improper bitstream length
@item explode
abort decoding on minor error detection
@item ignore_err
ignore errors
@item careful
consider things that violate the spec, are fast to check and have not been seen in the wild as errors
@item compliant
consider all spec non compliancies as errors
@item aggressive
consider things that a sane encoder shouldn't do as an error
@end table
@item -use_wallclock_as_timestamps @var{value} (@emph{input})
use wallclock as timestamps
@item -skip_initial_bytes @var{integer} (@emph{input})
set number of bytes to skip before reading header and frames
@item -correct_ts_overflow @var{value} (@emph{input})
correct single timestamp overflows
@item -flush_packets @var{integer} (@emph{output})
enable flushing of the I/O context after each packet
@item -metadata_header_padding @var{integer} (@emph{output})
set number of bytes to be written as padding in a metadata header
@item -output_ts_offset @var{value} (@emph{output})
set output timestamp offset
@item -max_interleave_delta @var{integer} (@emph{output})
maximum buffering duration for interleaving
@item -f_strict @var{integer} (@emph{input/output})
how strictly to follow the standards (deprecated; use strict, save via avconv)

Possible values:
@table @samp
@item very
strictly conform to a older more strict version of the spec or reference software
@item strict
strictly conform to all the things in the spec no matter what the consequences
@item normal

@item unofficial
allow unofficial extensions
@item experimental
allow non-standardized experimental variants
@end table
@item -strict @var{integer} (@emph{input/output})
how strictly to follow the standards

Possible values:
@table @samp
@item very
strictly conform to a older more strict version of the spec or reference software
@item strict
strictly conform to all the things in the spec no matter what the consequences
@item normal

@item unofficial
allow unofficial extensions
@item experimental
allow non-standardized experimental variants
@end table
@item -max_ts_probe @var{integer} (@emph{input})
maximum number of packets to read while waiting for the first timestamp
@item -avoid_negative_ts @var{integer} (@emph{output})
shift timestamps so they start at 0

Possible values:
@table @samp
@item auto
enabled when required by target format
@item disabled
do not change timestamps
@item make_non_negative
shift timestamps so they are non negative
@item make_zero
shift timestamps so they start at 0
@end table
@item -dump_separator @var{string} (@emph{input/output})
set information dump field separator
@item -codec_whitelist @var{string} (@emph{input})
List of decoders that are allowed to be used
@item -format_whitelist @var{string} (@emph{input})
List of demuxers that are allowed to be used
@item -protocol_whitelist @var{string} (@emph{input})
List of protocols that are allowed to be used
@item -protocol_blacklist @var{string} (@emph{input})
List of protocols that are not allowed to be used
@item -max_streams @var{integer} (@emph{input})
maximum number of streams
@item -skip_estimate_duration_from_pts @var{value} (@emph{input})
skip duration calculation in estimate_timings_from_pts
@item -max_probe_packets @var{integer} (@emph{input})
Maximum number of packets to probe a codec
@item -duration_probesize @var{integer} (@emph{input})
Maximum number of bytes to probe the durations of the streams in estimate_timings_from_pts
@end table
