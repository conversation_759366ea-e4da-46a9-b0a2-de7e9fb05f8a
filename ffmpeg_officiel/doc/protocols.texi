@chapter Protocol Options
@c man begin PROTOCOL OPTIONS

The libavformat library provides some generic global options, which
can be set on all the protocols. In addition each protocol may support
so-called private options, which are specific for that component.

Options may be set by specifying -@var{option} @var{value} in the
FFmpeg tools, or by setting the value explicitly in the
@code{AVFormatContext} options or using the @file{libavutil/opt.h} API
for programmatic use.

The list of supported options follows:

@table @option
@item protocol_whitelist @var{list} (@emph{input})
Set a ","-separated list of allowed protocols. "ALL" matches all protocols. Protocols
prefixed by "-" are disabled.
All protocols are allowed by default but protocols used by an another
protocol (nested protocols) are restricted to a per protocol subset.
@end table

@c man end PROTOCOL OPTIONS

@chapter Protocols
@c man begin PROTOCOLS

Protocols are configured elements in FFmpeg that enable access to
resources that require specific protocols.

When you configure your FFmpeg build, all the supported protocols are
enabled by default. You can list all available ones using the
configure option "--list-protocols".

You can disable all the protocols using the configure option
"--disable-protocols", and selectively enable a protocol using the
option "--enable-protocol=@var{PROTOCOL}", or you can disable a
particular protocol using the option
"--disable-protocol=@var{PROTOCOL}".

The option "-protocols" of the ff* tools will display the list of
supported protocols.

All protocols accept the following options:

@table @option
@item rw_timeout
Maximum time to wait for (network) read/write operations to complete,
in microseconds.
@end table

A description of the currently available protocols follows.

@section amqp

Advanced Message Queueing Protocol (AMQP) version 0-9-1 is a broker based
publish-subscribe communication protocol.

FFmpeg must be compiled with --enable-librabbitmq to support AMQP. A separate
AMQP broker must also be run. An example open-source AMQP broker is RabbitMQ.

After starting the broker, an FFmpeg client may stream data to the broker using
the command:

@example
ffmpeg -re -i input -f mpegts amqp://[[user]:[password]@@]hostname[:port][/vhost]
@end example

Where hostname and port (default is 5672) is the address of the broker. The
client may also set a user/password for authentication. The default for both
fields is "guest". Name of virtual host on broker can be set with vhost. The
default value is "/".

Multiple subscribers may stream from the broker using the command:
@example
ffplay amqp://[[user]:[password]@@]hostname[:port][/vhost]
@end example

In RabbitMQ all data published to the broker flows through a specific exchange,
and each subscribing client has an assigned queue/buffer. When a packet arrives
at an exchange, it may be copied to a client's queue depending on the exchange
and routing_key fields.

The following options are supported:

@table @option

@item exchange
Sets the exchange to use on the broker. RabbitMQ has several predefined
exchanges: "amq.direct" is the default exchange, where the publisher and
subscriber must have a matching routing_key; "amq.fanout" is the same as a
broadcast operation (i.e. the data is forwarded to all queues on the fanout
exchange independent of the routing_key); and "amq.topic" is similar to
"amq.direct", but allows for more complex pattern matching (refer to the RabbitMQ
documentation).

@item routing_key
Sets the routing key. The default value is "amqp". The routing key is used on
the "amq.direct" and "amq.topic" exchanges to decide whether packets are written
to the queue of a subscriber.

@item pkt_size
Maximum size of each packet sent/received to the broker. Default is 131072.
Minimum is 4096 and max is any large value (representable by an int). When
receiving packets, this sets an internal buffer size in FFmpeg. It should be
equal to or greater than the size of the published packets to the broker. Otherwise
the received message may be truncated causing decoding errors.

@item connection_timeout
The timeout in seconds during the initial connection to the broker. The
default value is rw_timeout, or 5 seconds if rw_timeout is not set.

@item delivery_mode @var{mode}
Sets the delivery mode of each message sent to broker.
The following values are accepted:
@table @samp
@item persistent
Delivery mode set to "persistent" (2). This is the default value.
Messages may be written to the broker's disk depending on its setup.

@item non-persistent
Delivery mode set to "non-persistent" (1).
Messages will stay in broker's memory unless the broker is under memory
pressure.

@end table

@end table

@section async

Asynchronous data filling wrapper for input stream.

Fill data in a background thread, to decouple I/O operation from demux thread.

@example
async:@var{URL}
async:http://host/resource
async:cache:http://host/resource
@end example

@section bluray

Read BluRay playlist.

The accepted options are:
@table @option

@item angle
BluRay angle

@item chapter
Start chapter (1...N)

@item playlist
Playlist to read (BDMV/PLAYLIST/?????.mpls)

@end table

Examples:

Read longest playlist from BluRay mounted to /mnt/bluray:
@example
bluray:/mnt/bluray
@end example

Read angle 2 of playlist 4 from BluRay mounted to /mnt/bluray, start from chapter 2:
@example
-playlist 4 -angle 2 -chapter 2 bluray:/mnt/bluray
@end example

@section cache

Caching wrapper for input stream.

Cache the input stream to temporary file. It brings seeking capability to live streams.

The accepted options are:
@table @option

@item read_ahead_limit
Amount in bytes that may be read ahead when seeking isn't supported. Range is -1 to INT_MAX.
-1 for unlimited. Default is 65536.

@end table

URL Syntax is
@example
cache:@var{URL}
@end example

@section concat

Physical concatenation protocol.

Read and seek from many resources in sequence as if they were
a unique resource.

A URL accepted by this protocol has the syntax:
@example
concat:@var{URL1}|@var{URL2}|...|@var{URLN}
@end example

where @var{URL1}, @var{URL2}, ..., @var{URLN} are the urls of the
resource to be concatenated, each one possibly specifying a distinct
protocol.

For example to read a sequence of files @file{split1.mpeg},
@file{split2.mpeg}, @file{split3.mpeg} with @command{ffplay} use the
command:
@example
ffplay concat:split1.mpeg\|split2.mpeg\|split3.mpeg
@end example

Note that you may need to escape the character "|" which is special for
many shells.

@section concatf

Physical concatenation protocol using a line break delimited list of
resources.

Read and seek from many resources in sequence as if they were
a unique resource.

A URL accepted by this protocol has the syntax:
@example
concatf:@var{URL}
@end example

where @var{URL} is the url containing a line break delimited list of
resources to be concatenated, each one possibly specifying a distinct
protocol. Special characters must be escaped with backslash or single
quotes. See @ref{quoting_and_escaping,,the "Quoting and escaping"
section in the ffmpeg-utils(1) manual,ffmpeg-utils}.

For example to read a sequence of files @file{split1.mpeg},
@file{split2.mpeg}, @file{split3.mpeg} listed in separate lines within
a file @file{split.txt} with @command{ffplay} use the command:
@example
ffplay concatf:split.txt
@end example
Where @file{split.txt} contains the lines:
@example
split1.mpeg
split2.mpeg
split3.mpeg
@end example

@section crypto

AES-encrypted stream reading protocol.

The accepted options are:
@table @option
@item key
Set the AES decryption key binary block from given hexadecimal representation.

@item iv
Set the AES decryption initialization vector binary block from given hexadecimal representation.
@end table

Accepted URL formats:
@example
crypto:@var{URL}
crypto+@var{URL}
@end example

@section data

Data in-line in the URI. See @url{http://en.wikipedia.org/wiki/Data_URI_scheme}.

For example, to convert a GIF file given inline with @command{ffmpeg}:
@example
ffmpeg -i "data:image/gif;base64,R0lGODdhCAAIAMIEAAAAAAAA//8AAP//AP///////////////ywAAAAACAAIAAADF0gEDLojDgdGiJdJqUX02iB4E8Q9jUMkADs=" smiley.png
@end example

@section fd

File descriptor access protocol.

The accepted syntax is:
@example
fd: -fd @var{file_descriptor}
@end example

If @option{fd} is not specified, by default the stdout file descriptor will be
used for writing, stdin for reading. Unlike the pipe protocol, fd protocol has
seek support if it corresponding to a regular file. fd protocol doesn't support
pass file descriptor via URL for security.

This protocol accepts the following options:

@table @option
@item blocksize
Set I/O operation maximum block size, in bytes. Default value is
@code{INT_MAX}, which results in not limiting the requested block size.
Setting this value reasonably low improves user termination request reaction
time, which is valuable if data transmission is slow.

@item fd
Set file descriptor.
@end table

@section file

File access protocol.

Read from or write to a file.

A file URL can have the form:
@example
file:@var{filename}
@end example

where @var{filename} is the path of the file to read.

An URL that does not have a protocol prefix will be assumed to be a
file URL. Depending on the build, an URL that looks like a Windows
path with the drive letter at the beginning will also be assumed to be
a file URL (usually not the case in builds for unix-like systems).

For example to read from a file @file{input.mpeg} with @command{ffmpeg}
use the command:
@example
ffmpeg -i file:input.mpeg output.mpeg
@end example

This protocol accepts the following options:

@table @option
@item truncate
Truncate existing files on write, if set to 1. A value of 0 prevents
truncating. Default value is 1.

@item blocksize
Set I/O operation maximum block size, in bytes. Default value is
@code{INT_MAX}, which results in not limiting the requested block size.
Setting this value reasonably low improves user termination request reaction
time, which is valuable for files on slow medium.

@item follow
If set to 1, the protocol will retry reading at the end of the file, allowing
reading files that still are being written. In order for this to terminate,
you either need to use the rw_timeout option, or use the interrupt callback
(for API users).

@item seekable
Controls if seekability is advertised on the file. 0 means non-seekable, -1
means auto (seekable for normal files, non-seekable for named pipes).

Many demuxers handle seekable and non-seekable resources differently,
overriding this might speed up opening certain files at the cost of losing some
features (e.g. accurate seeking).
@end table

@section ftp

FTP (File Transfer Protocol).

Read from or write to remote resources using FTP protocol.

Following syntax is required.
@example
ftp://[user[:password]@@]server[:port]/path/to/remote/resource.mpeg
@end example

This protocol accepts the following options.

@table @option
@item timeout
Set timeout in microseconds of socket I/O operations used by the underlying low level
operation. By default it is set to -1, which means that the timeout is
not specified.

@item ftp-user
Set a user to be used for authenticating to the FTP server. This is overridden by the
user in the FTP URL.

@item ftp-password
Set a password to be used for authenticating to the FTP server. This is overridden by
the password in the FTP URL, or by @option{ftp-anonymous-password} if no user is set.

@item ftp-anonymous-password
Password used when login as anonymous user. Typically an e-mail address
should be used.

@item ftp-write-seekable
Control seekability of connection during encoding. If set to 1 the
resource is supposed to be seekable, if set to 0 it is assumed not
to be seekable. Default value is 0.
@end table

NOTE: Protocol can be used as output, but it is recommended to not do
it, unless special care is taken (tests, customized server configuration
etc.). Different FTP servers behave in different way during seek
operation. ff* tools may produce incomplete content due to server limitations.

@section gopher

Gopher protocol.

@section gophers

Gophers protocol.

The Gopher protocol with TLS encapsulation.

@section hls

Read Apple HTTP Live Streaming compliant segmented stream as
a uniform one. The M3U8 playlists describing the segments can be
remote HTTP resources or local files, accessed using the standard
file protocol.
The nested protocol is declared by specifying
"+@var{proto}" after the hls URI scheme name, where @var{proto}
is either "file" or "http".

@example
hls+http://host/path/to/remote/resource.m3u8
hls+file://path/to/local/resource.m3u8
@end example

Using this protocol is discouraged - the hls demuxer should work
just as well (if not, please report the issues) and is more complete.
To use the hls demuxer instead, simply use the direct URLs to the
m3u8 files.

@section http

HTTP (Hyper Text Transfer Protocol).

This protocol accepts the following options:

@table @option
@item seekable
Control seekability of connection. If set to 1 the resource is
supposed to be seekable, if set to 0 it is assumed not to be seekable,
if set to -1 it will try to autodetect if it is seekable. Default
value is -1.

@item chunked_post
If set to 1 use chunked Transfer-Encoding for posts, default is 1.

@item http_proxy
set HTTP proxy to tunnel through e.g. http://example.com:1234

@item headers
Set custom HTTP headers, can override built in default headers. The
value must be a string encoding the headers.

@item content_type
Set a specific content type for the POST messages or for listen mode.

@item user_agent
Override the User-Agent header. If not specified the protocol will use a
string describing the libavformat build. ("Lavf/<version>")

@item referer
Set the Referer header. Include 'Referer: URL' header in HTTP request.

@item multiple_requests
Use persistent connections if set to 1, default is 0.

@item post_data
Set custom HTTP post data.

@item mime_type
Export the MIME type.

@item http_version
Exports the HTTP response version number. Usually "1.0" or "1.1".

@item cookies
Set the cookies to be sent in future requests. The format of each cookie is the
same as the value of a Set-Cookie HTTP response field. Multiple cookies can be
delimited by a newline character.

@item icy
If set to 1 request ICY (SHOUTcast) metadata from the server. If the server
supports this, the metadata has to be retrieved by the application by reading
the @option{icy_metadata_headers} and @option{icy_metadata_packet} options.
The default is 1.

@item icy_metadata_headers
If the server supports ICY metadata, this contains the ICY-specific HTTP reply
headers, separated by newline characters.

@item icy_metadata_packet
If the server supports ICY metadata, and @option{icy} was set to 1, this
contains the last non-empty metadata packet sent by the server. It should be
polled in regular intervals by applications interested in mid-stream metadata
updates.

@item metadata
Set an exported dictionary containing Icecast metadata from the bitstream, if present.
Only useful with the C API.

@item auth_type

Set HTTP authentication type. No option for Digest, since this method requires
getting nonce parameters from the server first and can't be used straight away like
Basic.

@table @option
@item none
Choose the HTTP authentication type automatically. This is the default.
@item basic

Choose the HTTP basic authentication.

Basic authentication sends a Base64-encoded string that contains a user name and password
for the client. Base64 is not a form of encryption and should be considered the same as
sending the user name and password in clear text (Base64 is a reversible encoding).
If a resource needs to be protected, strongly consider using an authentication scheme
other than basic authentication. HTTPS/TLS should be used with basic authentication.
Without these additional security enhancements, basic authentication should not be used
to protect sensitive or valuable information.
@end table

@item send_expect_100
Send an Expect: 100-continue header for POST. If set to 1 it will send, if set
to 0 it won't, if set to -1 it will try to send if it is applicable. Default
value is -1.

@item location
An exported dictionary containing the content location. Only useful with the C
API.

@item offset
Set initial byte offset.

@item end_offset
Try to limit the request to bytes preceding this offset.

@item method
When used as a client option it sets the HTTP method for the request.

When used as a server option it sets the HTTP method that is going to be
expected from the client(s).
If the expected and the received HTTP method do not match the client will
be given a Bad Request response.
When unset the HTTP method is not checked for now. This will be replaced by
autodetection in the future.

@item reconnect
Reconnect automatically when disconnected before EOF is hit.

@item reconnect_at_eof
If set then eof is treated like an error and causes reconnection, this is useful
for live / endless streams.

@item reconnect_on_network_error
Reconnect automatically in case of TCP/TLS errors during connect.

@item reconnect_on_http_error
A comma separated list of HTTP status codes to reconnect on. The list can
include specific status codes (e.g. '503') or the strings '4xx' / '5xx'.

@item reconnect_streamed
If set then even streamed/non seekable streams will be reconnected on errors.

@item reconnect_delay_max
Set the maximum delay in seconds after which to give up reconnecting.

@item reconnect_max_retries
Set the maximum number of times to retry a connection. Default unset.

@item reconnect_delay_total_max
Set the maximum total delay in seconds after which to give up reconnecting.

@item respect_retry_after
If enabled, and a Retry-After header is encountered, its requested reconnection
delay will be honored, rather than using exponential backoff. Useful for 429 and
503 errors. Default enabled.

@item listen
If set to 1 enables experimental HTTP server. This can be used to send data when
used as an output option, or read data from a client with HTTP POST when used as
an input option.
If set to 2 enables experimental multi-client HTTP server. This is not yet implemented
in ffmpeg.c and thus must not be used as a command line option.
@example
# Server side (sending):
ffmpeg -i somefile.ogg -c copy -listen 1 -f ogg http://@var{server}:@var{port}

# Client side (receiving):
ffmpeg -i http://@var{server}:@var{port} -c copy somefile.ogg

# Client can also be done with wget:
wget http://@var{server}:@var{port} -O somefile.ogg

# Server side (receiving):
ffmpeg -listen 1 -i http://@var{server}:@var{port} -c copy somefile.ogg

# Client side (sending):
ffmpeg -i somefile.ogg -chunked_post 0 -c copy -f ogg http://@var{server}:@var{port}

# Client can also be done with wget:
wget --post-file=somefile.ogg http://@var{server}:@var{port}
@end example

@item resource
The resource requested by a client, when the experimental HTTP server is in use.

@item reply_code
The HTTP code returned to the client, when the experimental HTTP server is in use.

@item short_seek_size
Set the threshold, in bytes, for when a readahead should be preferred over a seek and
new HTTP request. This is useful, for example, to make sure the same connection
is used for reading large video packets with small audio packets in between.

@end table

@subsection HTTP Cookies

Some HTTP requests will be denied unless cookie values are passed in with the
request. The @option{cookies} option allows these cookies to be specified. At
the very least, each cookie must specify a value along with a path and domain.
HTTP requests that match both the domain and path will automatically include the
cookie value in the HTTP Cookie header field. Multiple cookies can be delimited
by a newline.

The required syntax to play a stream specifying a cookie is:
@example
ffplay -cookies "nlqptid=nltid=tsn; path=/; domain=somedomain.com;" http://somedomain.com/somestream.m3u8
@end example

@section Icecast

Icecast protocol (stream to Icecast servers)

This protocol accepts the following options:

@table @option
@item ice_genre
Set the stream genre.

@item ice_name
Set the stream name.

@item ice_description
Set the stream description.

@item ice_url
Set the stream website URL.

@item ice_public
Set if the stream should be public.
The default is 0 (not public).

@item user_agent
Override the User-Agent header. If not specified a string of the form
"Lavf/<version>" will be used.

@item password
Set the Icecast mountpoint password.

@item content_type
Set the stream content type. This must be set if it is different from
audio/mpeg.

@item legacy_icecast
This enables support for Icecast versions < 2.4.0, that do not support the
HTTP PUT method but the SOURCE method.

@item tls
Establish a TLS (HTTPS) connection to Icecast.

@end table

@example
icecast://[@var{username}[:@var{password}]@@]@var{server}:@var{port}/@var{mountpoint}
@end example

@section ipfs

InterPlanetary File System (IPFS) protocol support. One can access files stored
on the IPFS network through so-called gateways. These are http(s) endpoints.
This protocol wraps the IPFS native protocols (ipfs:// and ipns://) to be sent
to such a gateway. Users can (and should) host their own node which means this
protocol will use one's local gateway to access files on the IPFS network.

This protocol accepts the following options:

@table @option

@item gateway
Defines the gateway to use. When not set, the protocol will first try
locating the local gateway by looking at @code{$IPFS_GATEWAY}, @code{$IPFS_PATH}
and @code{$HOME/.ipfs/}, in that order.

@end table

One can use this protocol in 2 ways. Using IPFS:
@example
ffplay ipfs://<hash>
@end example

Or the IPNS protocol (IPNS is mutable IPFS):
@example
ffplay ipns://<hash>
@end example

@section mmst

MMS (Microsoft Media Server) protocol over TCP.

@section mmsh

MMS (Microsoft Media Server) protocol over HTTP.

The required syntax is:
@example
mmsh://@var{server}[:@var{port}][/@var{app}][/@var{playpath}]
@end example

@section md5

MD5 output protocol.

Computes the MD5 hash of the data to be written, and on close writes
this to the designated output or stdout if none is specified. It can
be used to test muxers without writing an actual file.

Some examples follow.
@example
# Write the MD5 hash of the encoded AVI file to the file output.avi.md5.
ffmpeg -i input.flv -f avi -y md5:output.avi.md5

# Write the MD5 hash of the encoded AVI file to stdout.
ffmpeg -i input.flv -f avi -y md5:
@end example

Note that some formats (typically MOV) require the output protocol to
be seekable, so they will fail with the MD5 output protocol.

@section pipe

UNIX pipe access protocol.

Read and write from UNIX pipes.

The accepted syntax is:
@example
pipe:[@var{number}]
@end example

If @option{fd} isn't specified, @var{number} is the number corresponding to the file descriptor of the
pipe (e.g. 0 for stdin, 1 for stdout, 2 for stderr).  If @var{number}
is not specified, by default the stdout file descriptor will be used
for writing, stdin for reading.

For example to read from stdin with @command{ffmpeg}:
@example
cat test.wav | ffmpeg -i pipe:0
# ...this is the same as...
cat test.wav | ffmpeg -i pipe:
@end example

For writing to stdout with @command{ffmpeg}:
@example
ffmpeg -i test.wav -f avi pipe:1 | cat > test.avi
# ...this is the same as...
ffmpeg -i test.wav -f avi pipe: | cat > test.avi
@end example

This protocol accepts the following options:

@table @option
@item blocksize
Set I/O operation maximum block size, in bytes. Default value is
@code{INT_MAX}, which results in not limiting the requested block size.
Setting this value reasonably low improves user termination request reaction
time, which is valuable if data transmission is slow.
@item fd
Set file descriptor.
@end table

Note that some formats (typically MOV), require the output protocol to
be seekable, so they will fail with the pipe output protocol.

@section prompeg

Pro-MPEG Code of Practice #3 Release 2 FEC protocol.

The Pro-MPEG CoP#3 FEC is a 2D parity-check forward error correction mechanism
for MPEG-2 Transport Streams sent over RTP.

This protocol must be used in conjunction with the @code{rtp_mpegts} muxer and
the @code{rtp} protocol.

The required syntax is:
@example
-f rtp_mpegts -fec prompeg=@var{option}=@var{val}... rtp://@var{hostname}:@var{port}
@end example

The destination UDP ports are @code{port + 2} for the column FEC stream
and @code{port + 4} for the row FEC stream.

This protocol accepts the following options:
@table @option

@item l=@var{n}
The number of columns (4-20, LxD <= 100)

@item d=@var{n}
The number of rows (4-20, LxD <= 100)

@end table

Example usage:

@example
-f rtp_mpegts -fec prompeg=l=8:d=4 rtp://@var{hostname}:@var{port}
@end example

@section rist

Reliable Internet Streaming Transport protocol

The accepted options are:
@table @option
@item rist_profile
Supported values:
@table @samp
@item simple
@item main
This one is default.
@item advanced
@end table

@item buffer_size
Set internal RIST buffer size in milliseconds for retransmission of data.
Default value is 0 which means the librist default (1 sec). Maximum value is 30
seconds.

@item fifo_size
Size of the librist receiver output fifo in number of packets. This must be a
power of 2.
Defaults to 8192 (vs the librist default of 1024).

@item overrun_nonfatal=@var{1|0}
Survive in case of librist fifo buffer overrun. Default value is 0.

@item pkt_size
Set maximum packet size for sending data. 1316 by default.

@item log_level
Set loglevel for RIST logging messages. You only need to set this if you
explicitly want to enable debug level messages or packet loss simulation,
otherwise the regular loglevel is respected.

@item secret
Set override of encryption secret, by default is unset.

@item encryption
Set encryption type, by default is disabled.
Acceptable values are 128 and 256.
@end table

@section rtmp

Real-Time Messaging Protocol.

The Real-Time Messaging Protocol (RTMP) is used for streaming multimedia
content across a TCP/IP network.

The required syntax is:
@example
rtmp://[@var{username}:@var{password}@@]@var{server}[:@var{port}][/@var{app}][/@var{instance}][/@var{playpath}]
@end example

The accepted parameters are:
@table @option

@item username
An optional username (mostly for publishing).

@item password
An optional password (mostly for publishing).

@item server
The address of the RTMP server.

@item port
The number of the TCP port to use (by default is 1935).

@item app
It is the name of the application to access. It usually corresponds to
the path where the application is installed on the RTMP server
(e.g. @file{/ondemand/}, @file{/flash/live/}, etc.). You can override
the value parsed from the URI through the @code{rtmp_app} option, too.

@item playpath
It is the path or name of the resource to play with reference to the
application specified in @var{app}, may be prefixed by "mp4:". You
can override the value parsed from the URI through the @code{rtmp_playpath}
option, too.

@item listen
Act as a server, listening for an incoming connection.

@item timeout
Maximum time to wait for the incoming connection. Implies listen.
@end table

Additionally, the following parameters can be set via command line options
(or in code via @code{AVOption}s):
@table @option

@item rtmp_app
Name of application to connect on the RTMP server. This option
overrides the parameter specified in the URI.

@item rtmp_buffer
Set the client buffer time in milliseconds. The default is 3000.

@item rtmp_conn
Extra arbitrary AMF connection parameters, parsed from a string,
e.g. like @code{B:1 S:authMe O:1 NN:code:1.23 NS:flag:ok O:0}.
Each value is prefixed by a single character denoting the type,
B for Boolean, N for number, S for string, O for object, or Z for null,
followed by a colon. For Booleans the data must be either 0 or 1 for
FALSE or TRUE, respectively.  Likewise for Objects the data must be 0 or
1 to end or begin an object, respectively. Data items in subobjects may
be named, by prefixing the type with 'N' and specifying the name before
the value (i.e. @code{NB:myFlag:1}). This option may be used multiple
times to construct arbitrary AMF sequences.

@item rtmp_enhanced_codecs
Specify the list of codecs the client advertises to support in an
enhanced RTMP stream. This option should be set to a comma separated
list of fourcc values, like @code{hvc1,av01,vp09} for multiple codecs
or @code{hvc1} for only one codec. The specified list will be presented
in the "fourCcLive" property of the Connect Command Message.

@item rtmp_flashver
Version of the Flash plugin used to run the SWF player. The default
is LNX 9,0,124,2. (When publishing, the default is FMLE/3.0 (compatible;
<libavformat version>).)

@item rtmp_flush_interval
Number of packets flushed in the same request (RTMPT only). The default
is 10.

@item rtmp_live
Specify that the media is a live stream. No resuming or seeking in
live streams is possible. The default value is @code{any}, which means the
subscriber first tries to play the live stream specified in the
playpath. If a live stream of that name is not found, it plays the
recorded stream. The other possible values are @code{live} and
@code{recorded}.

@item rtmp_pageurl
URL of the web page in which the media was embedded. By default no
value will be sent.

@item rtmp_playpath
Stream identifier to play or to publish. This option overrides the
parameter specified in the URI.

@item rtmp_subscribe
Name of live stream to subscribe to. By default no value will be sent.
It is only sent if the option is specified or if rtmp_live
is set to live.

@item rtmp_swfhash
SHA256 hash of the decompressed SWF file (32 bytes).

@item rtmp_swfsize
Size of the decompressed SWF file, required for SWFVerification.

@item rtmp_swfurl
URL of the SWF player for the media. By default no value will be sent.

@item rtmp_swfverify
URL to player swf file, compute hash/size automatically.

@item rtmp_tcurl
URL of the target stream. Defaults to proto://host[:port]/app.

@item tcp_nodelay=@var{1|0}
Set TCP_NODELAY to disable Nagle's algorithm. Default value is 0.

@emph{Remark: Writing to the socket is currently not optimized to minimize system calls and reduces the efficiency / effect of TCP_NODELAY.}

@end table

For example to read with @command{ffplay} a multimedia resource named
"sample" from the application "vod" from an RTMP server "myserver":
@example
ffplay rtmp://myserver/vod/sample
@end example

To publish to a password protected server, passing the playpath and
app names separately:
@example
ffmpeg -re -i <input> -f flv -rtmp_playpath some/long/path -rtmp_app long/app/name rtmp://username:password@@myserver/
@end example

@section rtmpe

Encrypted Real-Time Messaging Protocol.

The Encrypted Real-Time Messaging Protocol (RTMPE) is used for
streaming multimedia content within standard cryptographic primitives,
consisting of Diffie-Hellman key exchange and HMACSHA256, generating
a pair of RC4 keys.

@section rtmps

Real-Time Messaging Protocol over a secure SSL connection.

The Real-Time Messaging Protocol (RTMPS) is used for streaming
multimedia content across an encrypted connection.

@section rtmpt

Real-Time Messaging Protocol tunneled through HTTP.

The Real-Time Messaging Protocol tunneled through HTTP (RTMPT) is used
for streaming multimedia content within HTTP requests to traverse
firewalls.

@section rtmpte

Encrypted Real-Time Messaging Protocol tunneled through HTTP.

The Encrypted Real-Time Messaging Protocol tunneled through HTTP (RTMPTE)
is used for streaming multimedia content within HTTP requests to traverse
firewalls.

@section rtmpts

Real-Time Messaging Protocol tunneled through HTTPS.

The Real-Time Messaging Protocol tunneled through HTTPS (RTMPTS) is used
for streaming multimedia content within HTTPS requests to traverse
firewalls.

@section libsmbclient

libsmbclient permits one to manipulate CIFS/SMB network resources.

Following syntax is required.

@example
smb://[[domain:]user[:password@@]]server[/share[/path[/file]]]
@end example

This protocol accepts the following options.

@table @option
@item timeout
Set timeout in milliseconds of socket I/O operations used by the underlying
low level operation. By default it is set to -1, which means that the timeout
is not specified.

@item truncate
Truncate existing files on write, if set to 1. A value of 0 prevents
truncating. Default value is 1.

@item workgroup
Set the workgroup used for making connections. By default workgroup is not specified.

@end table

For more information see: @url{http://www.samba.org/}.

@section libssh

Secure File Transfer Protocol via libssh

Read from or write to remote resources using SFTP protocol.

Following syntax is required.

@example
sftp://[user[:password]@@]server[:port]/path/to/remote/resource.mpeg
@end example

This protocol accepts the following options.

@table @option
@item timeout
Set timeout of socket I/O operations used by the underlying low level
operation. By default it is set to -1, which means that the timeout
is not specified.

@item truncate
Truncate existing files on write, if set to 1. A value of 0 prevents
truncating. Default value is 1.

@item private_key
Specify the path of the file containing private key to use during authorization.
By default libssh searches for keys in the @file{~/.ssh/} directory.

@end table

Example: Play a file stored on remote server.

@example
ffplay sftp://user:password@@server_address:22/home/<USER>/resource.mpeg
@end example

@section librtmp rtmp, rtmpe, rtmps, rtmpt, rtmpte

Real-Time Messaging Protocol and its variants supported through
librtmp.

Requires the presence of the librtmp headers and library during
configuration. You need to explicitly configure the build with
"--enable-librtmp". If enabled this will replace the native RTMP
protocol.

This protocol provides most client functions and a few server
functions needed to support RTMP, RTMP tunneled in HTTP (RTMPT),
encrypted RTMP (RTMPE), RTMP over SSL/TLS (RTMPS) and tunneled
variants of these encrypted types (RTMPTE, RTMPTS).

The required syntax is:
@example
@var{rtmp_proto}://@var{server}[:@var{port}][/@var{app}][/@var{playpath}] @var{options}
@end example

where @var{rtmp_proto} is one of the strings "rtmp", "rtmpt", "rtmpe",
"rtmps", "rtmpte", "rtmpts" corresponding to each RTMP variant, and
@var{server}, @var{port}, @var{app} and @var{playpath} have the same
meaning as specified for the RTMP native protocol.
@var{options} contains a list of space-separated options of the form
@var{key}=@var{val}.

See the librtmp manual page (man 3 librtmp) for more information.

For example, to stream a file in real-time to an RTMP server using
@command{ffmpeg}:
@example
ffmpeg -re -i myfile -f flv rtmp://myserver/live/mystream
@end example

To play the same stream using @command{ffplay}:
@example
ffplay "rtmp://myserver/live/mystream live=1"
@end example

@section rtp

Real-time Transport Protocol.

The required syntax for an RTP URL is:
@example
rtp://@var{hostname}[:@var{port}][?@var{options}]
@end example

@var{port} specifies the RTP port to use.

@var{options} contains a list of &-separated options of the form
@var{key}=@var{val}.

The following URL options are supported:

@table @option

@item ttl=@var{n}
Set the TTL (Time-To-Live) value (for multicast only).

@item rtcpport=@var{n}
Set the remote RTCP port to @var{n}.

@item localrtpport=@var{n}
Set the local RTP port to @var{n}.

@item localrtcpport=@var{n}'
Set the local RTCP port to @var{n}.

@item pkt_size=@var{n}
Set max packet size (in bytes) to @var{n}.

@item buffer_size=@var{size}
Set the maximum UDP socket buffer size in bytes.

@item connect=0|1
Do a @code{connect()} on the UDP socket (if set to 1) or not (if set
to 0).

@item sources=@var{ip}[,@var{ip}]
List allowed source IP addresses.

@item block=@var{ip}[,@var{ip}]
List disallowed (blocked) source IP addresses.

@item write_to_source=0|1
Send packets to the source address of the latest received packet (if
set to 1) or to a default remote address (if set to 0).

@item localport=@var{n}
Set the local RTP port to @var{n}.

This is a deprecated option. Instead, @option{localrtpport} should be
used.

@item localaddr=@var{addr}
Local IP address of a network interface used for sending packets or joining
multicast groups.

@item timeout=@var{n}
Set timeout (in microseconds) of socket I/O operations to @var{n}.
@end table

Important notes:

@enumerate

@item
If @option{rtcpport} is not set the RTCP port will be set to the RTP
port value plus 1.

@item
If @option{localrtpport} (the local RTP port) is not set any available
port will be used for the local RTP and RTCP ports.

@item
If @option{localrtcpport} (the local RTCP port) is not set it will be
set to the local RTP port value plus 1.
@end enumerate

@section rtsp

Real-Time Streaming Protocol.

RTSP is not technically a protocol handler in libavformat, it is a demuxer
and muxer. The demuxer supports both normal RTSP (with data transferred
over RTP; this is used by e.g. Apple and Microsoft) and Real-RTSP (with
data transferred over RDT).

The muxer can be used to send a stream using RTSP ANNOUNCE to a server
supporting it (currently Darwin Streaming Server and Mischa Spiegelmock's
@uref{https://github.com/revmischa/rtsp-server, RTSP server}).

The required syntax for a RTSP url is:
@example
rtsp://@var{hostname}[:@var{port}]/@var{path}
@end example

Options can be set on the @command{ffmpeg}/@command{ffplay} command
line, or set in code via @code{AVOption}s or in
@code{avformat_open_input}.

@subsection Muxer
The following options are supported.

@table @option
@item rtsp_transport
Set RTSP transport protocols.

It accepts the following values:
@table @samp
@item udp
Use UDP as lower transport protocol.

@item tcp
Use TCP (interleaving within the RTSP control channel) as lower
transport protocol.
@end table

Default value is @samp{0}.

@item rtsp_flags
Set RTSP flags.

The following values are accepted:
@table @samp
@item latm
Use MP4A-LATM packetization instead of MPEG4-GENERIC for AAC.
@item rfc2190
Use RFC 2190 packetization instead of RFC 4629 for H.263.
@item skip_rtcp
Don't send RTCP sender reports.
@item h264_mode0
Use mode 0 for H.264 in RTP.
@item send_bye
Send RTCP BYE packets when finishing.
@end table

Default value is @samp{0}.


@item min_port
Set minimum local UDP port. Default value is 5000.

@item max_port
Set maximum local UDP port. Default value is 65000.

@item buffer_size
Set the maximum socket buffer size in bytes.

@item pkt_size
Set max send packet size (in bytes). Default value is 1472.
@end table

@subsection Demuxer
The following options are supported.

@table @option
@item initial_pause
Do not start playing the stream immediately if set to 1. Default value
is 0.

@item rtsp_transport
Set RTSP transport protocols.

It accepts the following values:
@table @samp
@item udp
Use UDP as lower transport protocol.

@item tcp
Use TCP (interleaving within the RTSP control channel) as lower
transport protocol.

@item udp_multicast
Use UDP multicast as lower transport protocol.

@item http
Use HTTP tunneling as lower transport protocol, which is useful for
passing proxies.

@item https
Use HTTPs tunneling as lower transport protocol, which is useful for
passing proxies and widely used for security consideration.
@end table

Multiple lower transport protocols may be specified, in that case they are
tried one at a time (if the setup of one fails, the next one is tried).
For the muxer, only the @samp{tcp} and @samp{udp} options are supported.

@item rtsp_flags
Set RTSP flags.

The following values are accepted:
@table @samp
@item filter_src
Accept packets only from negotiated peer address and port.
@item listen
Act as a server, listening for an incoming connection.
@item prefer_tcp
Try TCP for RTP transport first, if TCP is available as RTSP RTP transport.
@item satip_raw
Export raw MPEG-TS stream instead of demuxing. The flag will simply write out
the raw stream, with the original PAT/PMT/PIDs intact.
@end table

Default value is @samp{none}.

@item allowed_media_types
Set media types to accept from the server.

The following flags are accepted:
@table @samp
@item video
@item audio
@item data
@item subtitle
@end table

By default it accepts all media types.

@item min_port
Set minimum local UDP port. Default value is 5000.

@item max_port
Set maximum local UDP port. Default value is 65000.

@item listen_timeout
Set maximum timeout (in seconds) to establish an initial connection. Setting
@option{listen_timeout} > 0 sets @option{rtsp_flags} to @samp{listen}. Default is -1
which means an infinite timeout when @samp{listen} mode is set.

@item reorder_queue_size
Set number of packets to buffer for handling of reordered packets.

@item timeout
Set socket TCP I/O timeout in microseconds.

@item user_agent
Override User-Agent header. If not specified, it defaults to the
libavformat identifier string.

@item buffer_size
Set the maximum socket buffer size in bytes.
@end table

When receiving data over UDP, the demuxer tries to reorder received packets
(since they may arrive out of order, or packets may get lost totally). This
can be disabled by setting the maximum demuxing delay to zero (via
the @code{max_delay} field of AVFormatContext).

When watching multi-bitrate Real-RTSP streams with @command{ffplay}, the
streams to display can be chosen with @code{-vst} @var{n} and
@code{-ast} @var{n} for video and audio respectively, and can be switched
on the fly by pressing @code{v} and @code{a}.

@subsection Examples

The following examples all make use of the @command{ffplay} and
@command{ffmpeg} tools.

@itemize
@item
Watch a stream over UDP, with a max reordering delay of 0.5 seconds:
@example
ffplay -max_delay 500000 -rtsp_transport udp rtsp://server/video.mp4
@end example

@item
Watch a stream tunneled over HTTP:
@example
ffplay -rtsp_transport http rtsp://server/video.mp4
@end example

@item
Send a stream in realtime to a RTSP server, for others to watch:
@example
ffmpeg -re -i @var{input} -f rtsp -muxdelay 0.1 rtsp://server/live.sdp
@end example

@item
Receive a stream in realtime:
@example
ffmpeg -rtsp_flags listen -i rtsp://ownaddress/live.sdp @var{output}
@end example
@end itemize

@section sap

Session Announcement Protocol (RFC 2974). This is not technically a
protocol handler in libavformat, it is a muxer and demuxer.
It is used for signalling of RTP streams, by announcing the SDP for the
streams regularly on a separate port.

@subsection Muxer

The syntax for a SAP url given to the muxer is:
@example
sap://@var{destination}[:@var{port}][?@var{options}]
@end example

The RTP packets are sent to @var{destination} on port @var{port},
or to port 5004 if no port is specified.
@var{options} is a @code{&}-separated list. The following options
are supported:

@table @option

@item announce_addr=@var{address}
Specify the destination IP address for sending the announcements to.
If omitted, the announcements are sent to the commonly used SAP
announcement multicast address ************* (sap.mcast.net), or
ff0e::2:7ffe if @var{destination} is an IPv6 address.

@item announce_port=@var{port}
Specify the port to send the announcements on, defaults to
9875 if not specified.

@item ttl=@var{ttl}
Specify the time to live value for the announcements and RTP packets,
defaults to 255.

@item same_port=@var{0|1}
If set to 1, send all RTP streams on the same port pair. If zero (the
default), all streams are sent on unique ports, with each stream on a
port 2 numbers higher than the previous.
VLC/Live555 requires this to be set to 1, to be able to receive the stream.
The RTP stack in libavformat for receiving requires all streams to be sent
on unique ports.
@end table

Example command lines follow.

To broadcast a stream on the local subnet, for watching in VLC:

@example
ffmpeg -re -i @var{input} -f sap sap://***********?same_port=1
@end example

Similarly, for watching in @command{ffplay}:

@example
ffmpeg -re -i @var{input} -f sap sap://***********
@end example

And for watching in @command{ffplay}, over IPv6:

@example
ffmpeg -re -i @var{input} -f sap sap://[ff0e::1:2:3:4]
@end example

@subsection Demuxer

The syntax for a SAP url given to the demuxer is:
@example
sap://[@var{address}][:@var{port}]
@end example

@var{address} is the multicast address to listen for announcements on,
if omitted, the default ************* (sap.mcast.net) is used. @var{port}
is the port that is listened on, 9875 if omitted.

The demuxers listens for announcements on the given address and port.
Once an announcement is received, it tries to receive that particular stream.

Example command lines follow.

To play back the first stream announced on the normal SAP multicast address:

@example
ffplay sap://
@end example

To play back the first stream announced on one the default IPv6 SAP multicast address:

@example
ffplay sap://[ff0e::2:7ffe]
@end example

@section sctp

Stream Control Transmission Protocol.

The accepted URL syntax is:
@example
sctp://@var{host}:@var{port}[?@var{options}]
@end example

The protocol accepts the following options:
@table @option
@item listen
If set to any value, listen for an incoming connection. Outgoing connection is done by default.

@item max_streams
Set the maximum number of streams. By default no limit is set.
@end table

@section srt

Haivision Secure Reliable Transport Protocol via libsrt.

The supported syntax for a SRT URL is:
@example
srt://@var{hostname}:@var{port}[?@var{options}]
@end example

@var{options} contains a list of &-separated options of the form
@var{key}=@var{val}.

or

@example
@var{options} srt://@var{hostname}:@var{port}
@end example

@var{options} contains a list of '-@var{key} @var{val}'
options.

This protocol accepts the following options.

@table @option
@item connect_timeout=@var{milliseconds}
Connection timeout; SRT cannot connect for RTT > 1500 msec
(2 handshake exchanges) with the default connect timeout of
3 seconds. This option applies to the caller and rendezvous
connection modes. The connect timeout is 10 times the value
set for the rendezvous mode (which can be used as a
workaround for this connection problem with earlier versions).

@item ffs=@var{bytes}
Flight Flag Size (Window Size), in bytes. FFS is actually an
internal parameter and you should set it to not less than
@option{recv_buffer_size} and @option{mss}. The default value
is relatively large, therefore unless you set a very large receiver buffer,
you do not need to change this option. Default value is 25600.

@item inputbw=@var{bytes/seconds}
Sender nominal input rate, in bytes per seconds. Used along with
@option{oheadbw}, when @option{maxbw} is set to relative (0), to
calculate maximum sending rate when recovery packets are sent
along with the main media stream:
@option{inputbw} * (100 + @option{oheadbw}) / 100
if @option{inputbw} is not set while @option{maxbw} is set to
relative (0), the actual input rate is evaluated inside
the library. Default value is 0.

@item iptos=@var{tos}
IP Type of Service. Applies to sender only. Default value is 0xB8.

@item ipttl=@var{ttl}
IP Time To Live. Applies to sender only. Default value is 64.

@item latency=@var{microseconds}
Timestamp-based Packet Delivery Delay.
Used to absorb bursts of missed packet retransmissions.
This flag sets both @option{rcvlatency} and @option{peerlatency}
to the same value. Note that prior to version 1.3.0
this is the only flag to set the latency, however
this is effectively equivalent to setting @option{peerlatency},
when side is sender and @option{rcvlatency}
when side is receiver, and the bidirectional stream
sending is not supported.

@item listen_timeout=@var{microseconds}
Set socket listen timeout.

@item maxbw=@var{bytes/seconds}
Maximum sending bandwidth, in bytes per seconds.
-1 infinite (CSRTCC limit is 30mbps)
0 relative to input rate (see @option{inputbw})
>0 absolute limit value
Default value is 0 (relative)

@item mode=@var{caller|listener|rendezvous}
Connection mode.
@option{caller} opens client connection.
@option{listener} starts server to listen for incoming connections.
@option{rendezvous} use Rendez-Vous connection mode.
Default value is caller.

@item mss=@var{bytes}
Maximum Segment Size, in bytes. Used for buffer allocation
and rate calculation using a packet counter assuming fully
filled packets. The smallest MSS between the peers is
used. This is 1500 by default in the overall internet.
This is the maximum size of the UDP packet and can be
only decreased, unless you have some unusual dedicated
network settings. Default value is 1500.

@item nakreport=@var{1|0}
If set to 1, Receiver will send `UMSG_LOSSREPORT` messages
periodically until a lost packet is retransmitted or
intentionally dropped. Default value is 1.

@item oheadbw=@var{percents}
Recovery bandwidth overhead above input rate, in percents.
See @option{inputbw}. Default value is 25%.

@item passphrase=@var{string}
HaiCrypt Encryption/Decryption Passphrase string, length
from 10 to 79 characters. The passphrase is the shared
secret between the sender and the receiver. It is used
to generate the Key Encrypting Key using PBKDF2
(Password-Based Key Derivation Function). It is used
only if @option{pbkeylen} is non-zero. It is used on
the receiver only if the received data is encrypted.
The configured passphrase cannot be recovered (write-only).

@item enforced_encryption=@var{1|0}
If true, both connection parties must have the same password
set (including empty, that is, with no encryption). If the
password doesn't match or only one side is unencrypted,
the connection is rejected. Default is true.

@item kmrefreshrate=@var{packets}
The number of packets to be transmitted after which the
encryption key is switched to a new key. Default is -1.
-1 means auto (0x1000000 in srt library). The range for
this option is integers in the 0 - @code{INT_MAX}.

@item kmpreannounce=@var{packets}
The interval between when a new encryption key is sent and
when switchover occurs. This value also applies to the
subsequent interval between when switchover occurs and
when the old encryption key is decommissioned. Default is -1.
-1 means auto (0x1000 in srt library). The range for
this option is integers in the 0 - @code{INT_MAX}.

@item snddropdelay=@var{microseconds}
The sender's extra delay before dropping packets. This delay is
added to the default drop delay time interval value.

Special value -1: Do not drop packets on the sender at all.

@item payload_size=@var{bytes}
Sets the maximum declared size of a packet transferred
during the single call to the sending function in Live
mode. Use 0 if this value isn't used (which is default in
file mode).
Default is -1 (automatic), which typically means MPEG-TS;
if you are going to use SRT
to send any different kind of payload, such as, for example,
wrapping a live stream in very small frames, then you can
use a bigger maximum frame size, though not greater than
1456 bytes.

@item pkt_size=@var{bytes}
Alias for @samp{payload_size}.

@item peerlatency=@var{microseconds}
The latency value (as described in @option{rcvlatency}) that is
set by the sender side as a minimum value for the receiver.

@item pbkeylen=@var{bytes}
Sender encryption key length, in bytes.
Only can be set to 0, 16, 24 and 32.
Enable sender encryption if not 0.
Not required on receiver (set to 0),
key size obtained from sender in HaiCrypt handshake.
Default value is 0.

@item rcvlatency=@var{microseconds}
The time that should elapse since the moment when the
packet was sent and the moment when it's delivered to
the receiver application in the receiving function.
This time should be a buffer time large enough to cover
the time spent for sending, unexpectedly extended RTT
time, and the time needed to retransmit the lost UDP
packet. The effective latency value will be the maximum
of this options' value and the value of @option{peerlatency}
set by the peer side. Before version 1.3.0 this option
is only available as @option{latency}.

@item recv_buffer_size=@var{bytes}
Set UDP receive buffer size, expressed in bytes.

@item send_buffer_size=@var{bytes}
Set UDP send buffer size, expressed in bytes.

@item timeout=@var{microseconds}
Set raise error timeouts for read, write and connect operations. Note that the
SRT library has internal timeouts which can be controlled separately, the
value set here is only a cap on those.

@item tlpktdrop=@var{1|0}
Too-late Packet Drop. When enabled on receiver, it skips
missing packets that have not been delivered in time and
delivers the following packets to the application when
their time-to-play has come. It also sends a fake ACK to
the sender. When enabled on sender and enabled on the
receiving peer, the sender drops the older packets that
have no chance of being delivered in time. It was
automatically enabled in the sender if the receiver
supports it.

@item sndbuf=@var{bytes}
Set send buffer size, expressed in bytes.

@item rcvbuf=@var{bytes}
Set receive buffer size, expressed in bytes.

Receive buffer must not be greater than @option{ffs}.

@item lossmaxttl=@var{packets}
The value up to which the Reorder Tolerance may grow. When
Reorder Tolerance is > 0, then packet loss report is delayed
until that number of packets come in. Reorder Tolerance
increases every time a "belated" packet has come, but it
wasn't due to retransmission (that is, when UDP packets tend
to come out of order), with the difference between the latest
sequence and this packet's sequence, and not more than the
value of this option. By default it's 0, which means that this
mechanism is turned off, and the loss report is always sent
immediately upon experiencing a "gap" in sequences.

@item minversion
The minimum SRT version that is required from the peer. A connection
to a peer that does not satisfy the minimum version requirement
will be rejected.

The version format in hex is 0xXXYYZZ for x.y.z in human readable
form.

@item streamid=@var{string}
A string limited to 512 characters that can be set on the socket prior
to connecting. This stream ID will be able to be retrieved by the
listener side from the socket that is returned from srt_accept and
was connected by a socket with that set stream ID. SRT does not enforce
any special interpretation of the contents of this string.
This option doesn’t make sense in Rendezvous connection; the result
might be that simply one side will override the value from the other
side and it’s the matter of luck which one would win

@item srt_streamid=@var{string}
Alias for @samp{streamid} to avoid conflict with ffmpeg command line option.

@item smoother=@var{live|file}
The type of Smoother used for the transmission for that socket, which
is responsible for the transmission and congestion control. The Smoother
type must be exactly the same on both connecting parties, otherwise
the connection is rejected.

@item messageapi=@var{1|0}
When set, this socket uses the Message API, otherwise it uses Buffer
API. Note that in live mode (see @option{transtype}) there’s only
message API available. In File mode you can chose to use one of two modes:

Stream API (default, when this option is false). In this mode you may
send as many data as you wish with one sending instruction, or even use
dedicated functions that read directly from a file. The internal facility
will take care of any speed and congestion control. When receiving, you
can also receive as many data as desired, the data not extracted will be
waiting for the next call. There is no boundary between data portions in
the Stream mode.

Message API. In this mode your single sending instruction passes exactly
one piece of data that has boundaries (a message). Contrary to Live mode,
this message may span across multiple UDP packets and the only size
limitation is that it shall fit as a whole in the sending buffer. The
receiver shall use as large buffer as necessary to receive the message,
otherwise the message will not be given up. When the message is not
complete (not all packets received or there was a packet loss) it will
not be given up.

@item transtype=@var{live|file}
Sets the transmission type for the socket, in particular, setting this
option sets multiple other parameters to their default values as required
for a particular transmission type.

live: Set options as for live transmission. In this mode, you should
send by one sending instruction only so many data that fit in one UDP packet,
and limited to the value defined first in @option{payload_size} (1316 is
default in this mode). There is no speed control in this mode, only the
bandwidth control, if configured, in order to not exceed the bandwidth with
the overhead transmission (retransmitted and control packets).

file: Set options as for non-live transmission. See @option{messageapi}
for further explanations

@item linger=@var{seconds}
The number of seconds that the socket waits for unsent data when closing.
Default is -1. -1 means auto (off with 0 seconds in live mode, on with 180
seconds in file mode). The range for this option is integers in the
0 - @code{INT_MAX}.

@item tsbpd=@var{1|0}
When true, use Timestamp-based Packet Delivery mode. The default behavior
depends on the transmission type: enabled in live mode, disabled in file
mode.

@end table

For more information see: @url{https://github.com/Haivision/srt}.

@section srtp

Secure Real-time Transport Protocol.

The accepted options are:
@table @option
@item srtp_in_suite
@item srtp_out_suite
Select input and output encoding suites.

Supported values:
@table @samp
@item AES_CM_128_HMAC_SHA1_80
@item SRTP_AES128_CM_HMAC_SHA1_80
@item AES_CM_128_HMAC_SHA1_32
@item SRTP_AES128_CM_HMAC_SHA1_32
@end table

@item srtp_in_params
@item srtp_out_params
Set input and output encoding parameters, which are expressed by a
base64-encoded representation of a binary block. The first 16 bytes of
this binary block are used as master key, the following 14 bytes are
used as master salt.
@end table

@section subfile

Virtually extract a segment of a file or another stream.
The underlying stream must be seekable.

Accepted options:
@table @option
@item start
Start offset of the extracted segment, in bytes.
@item end
End offset of the extracted segment, in bytes.
If set to 0, extract till end of file.
@end table

Examples:

Extract a chapter from a DVD VOB file (start and end sectors obtained
externally and multiplied by 2048):
@example
subfile,,start,153391104,end,268142592,,:/media/dvd/VIDEO_TS/VTS_08_1.VOB
@end example

Play an AVI file directly from a TAR archive:
@example
subfile,,start,*********,end,*********,,:archive.tar
@end example

Play a MPEG-TS file from start offset till end:
@example
subfile,,start,32815239,end,0,,:video.ts
@end example

@section tee

Writes the output to multiple protocols. The individual outputs are separated
by |

@example
tee:file://path/to/local/this.avi|file://path/to/local/that.avi
@end example

@section tcp

Transmission Control Protocol.

The required syntax for a TCP url is:
@example
tcp://@var{hostname}:@var{port}[?@var{options}]
@end example

@var{options} contains a list of &-separated options of the form
@var{key}=@var{val}.

The list of supported options follows.

@table @option
@item listen=@var{2|1|0}
Listen for an incoming connection. 0 disables listen, 1 enables listen in
single client mode, 2 enables listen in multi-client mode. Default value is 0.

@item local_addr=@var{addr}
Local IP address of a network interface used for tcp socket connect.

@item local_port=@var{port}
Local port used for tcp socket connect.

@item timeout=@var{microseconds}
Set raise error timeout, expressed in microseconds.

This option is only relevant in read mode: if no data arrived in more
than this time interval, raise error.

@item listen_timeout=@var{milliseconds}
Set listen timeout, expressed in milliseconds.

@item recv_buffer_size=@var{bytes}
Set receive buffer size, expressed bytes.

@item send_buffer_size=@var{bytes}
Set send buffer size, expressed bytes.

@item tcp_nodelay=@var{1|0}
Set TCP_NODELAY to disable Nagle's algorithm. Default value is 0.

@emph{Remark: Writing to the socket is currently not optimized to minimize system calls and reduces the efficiency / effect of TCP_NODELAY.}

@item tcp_mss=@var{bytes}
Set maximum segment size for outgoing TCP packets, expressed in bytes.
@end table

The following example shows how to setup a listening TCP connection
with @command{ffmpeg}, which is then accessed with @command{ffplay}:
@example
ffmpeg -i @var{input} -f @var{format} tcp://@var{hostname}:@var{port}?listen
ffplay tcp://@var{hostname}:@var{port}
@end example

@section tls

Transport Layer Security (TLS) / Secure Sockets Layer (SSL)

The required syntax for a TLS/SSL url is:
@example
tls://@var{hostname}:@var{port}[?@var{options}]
@end example

The following parameters can be set via command line options
(or in code via @code{AVOption}s):

@table @option

@item ca_file, cafile=@var{filename}
A file containing certificate authority (CA) root certificates to treat
as trusted. If the linked TLS library contains a default this might not
need to be specified for verification to work, but not all libraries and
setups have defaults built in.
The file must be in OpenSSL PEM format.

@item tls_verify=@var{1|0}
If enabled, try to verify the peer that we are communicating with.
Note, if using OpenSSL, this currently only makes sure that the
peer certificate is signed by one of the root certificates in the CA
database, but it does not validate that the certificate actually
matches the host name we are trying to connect to. (With other backends,
the host name is validated as well.)

This is disabled by default since it requires a CA database to be
provided by the caller in many cases.

@item cert_file, cert=@var{filename}
A file containing a certificate to use in the handshake with the peer.
(When operating as server, in listen mode, this is more often required
by the peer, while client certificates only are mandated in certain
setups.)

@item key_file, key=@var{filename}
A file containing the private key for the certificate.

@item listen=@var{1|0}
If enabled, listen for connections on the provided port, and assume
the server role in the handshake instead of the client role.

@item http_proxy
The HTTP proxy to tunnel through, e.g. @code{http://example.com:1234}.
The proxy must support the CONNECT method.

@end table

Example command lines:

To create a TLS/SSL server that serves an input stream.

@example
ffmpeg -i @var{input} -f @var{format} tls://@var{hostname}:@var{port}?listen&cert=@var{server.crt}&key=@var{server.key}
@end example

To play back a stream from the TLS/SSL server using @command{ffplay}:

@example
ffplay tls://@var{hostname}:@var{port}
@end example

@section udp

User Datagram Protocol.

The required syntax for an UDP URL is:
@example
udp://@var{hostname}:@var{port}[?@var{options}]
@end example

@var{options} contains a list of &-separated options of the form @var{key}=@var{val}.

In case threading is enabled on the system, a circular buffer is used
to store the incoming data, which allows one to reduce loss of data due to
UDP socket buffer overruns. The @var{fifo_size} and
@var{overrun_nonfatal} options are related to this buffer.

The list of supported options follows.

@table @option
@item buffer_size=@var{size}
Set the UDP maximum socket buffer size in bytes. This is used to set either
the receive or send buffer size, depending on what the socket is used for.
Default is 32 KB for output, 384 KB for input.  See also @var{fifo_size}.

@item bitrate=@var{bitrate}
If set to nonzero, the output will have the specified constant bitrate if the
input has enough packets to sustain it.

@item burst_bits=@var{bits}
When using @var{bitrate} this specifies the maximum number of bits in
packet bursts.

@item localport=@var{port}
Override the local UDP port to bind with.

@item localaddr=@var{addr}
Local IP address of a network interface used for sending packets or joining
multicast groups.

@item pkt_size=@var{size}
Set the size in bytes of UDP packets.

@item reuse=@var{1|0}
Explicitly allow or disallow reusing UDP sockets.

@item ttl=@var{ttl}
Set the time to live value (for multicast only).

@item connect=@var{1|0}
Initialize the UDP socket with @code{connect()}. In this case, the
destination address can't be changed with ff_udp_set_remote_url later.
If the destination address isn't known at the start, this option can
be specified in ff_udp_set_remote_url, too.
This allows finding out the source address for the packets with getsockname,
and makes writes return with AVERROR(ECONNREFUSED) if "destination
unreachable" is received.
For receiving, this gives the benefit of only receiving packets from
the specified peer address/port.

@item sources=@var{address}[,@var{address}]
Only receive packets sent from the specified addresses. In case of multicast,
also subscribe to multicast traffic coming from these addresses only.

@item block=@var{address}[,@var{address}]
Ignore packets sent from the specified addresses. In case of multicast, also
exclude the source addresses in the multicast subscription.

@item fifo_size=@var{units}
Set the UDP receiving circular buffer size, expressed as a number of
packets with size of 188 bytes. If not specified defaults to 7*4096.

@item overrun_nonfatal=@var{1|0}
Survive in case of UDP receiving circular buffer overrun. Default
value is 0.

@item timeout=@var{microseconds}
Set raise error timeout, expressed in microseconds.

This option is only relevant in read mode: if no data arrived in more
than this time interval, raise error.

@item broadcast=@var{1|0}
Explicitly allow or disallow UDP broadcasting.

Note that broadcasting may not work properly on networks having
a broadcast storm protection.
@end table

@subsection Examples

@itemize
@item
Use @command{ffmpeg} to stream over UDP to a remote endpoint:
@example
ffmpeg -i @var{input} -f @var{format} udp://@var{hostname}:@var{port}
@end example

@item
Use @command{ffmpeg} to stream in mpegts format over UDP using 188
sized UDP packets, using a large input buffer:
@example
ffmpeg -i @var{input} -f mpegts udp://@var{hostname}:@var{port}?pkt_size=188&buffer_size=65535
@end example

@item
Use @command{ffmpeg} to receive over UDP from a remote endpoint:
@example
ffmpeg -i udp://[@var{multicast-address}]:@var{port} ...
@end example
@end itemize

@section unix

Unix local socket

The required syntax for a Unix socket URL is:

@example
unix://@var{filepath}
@end example

The following parameters can be set via command line options
(or in code via @code{AVOption}s):

@table @option
@item timeout
Timeout in ms.
@item listen
Create the Unix socket in listening mode.
@end table

@section zmq

ZeroMQ asynchronous messaging using the libzmq library.

This library supports unicast streaming to multiple clients without relying on
an external server.

The required syntax for streaming or connecting to a stream is:
@example
zmq:tcp://ip-address:port
@end example

Example:
Create a localhost stream on port 5555:
@example
ffmpeg -re -i input -f mpegts zmq:tcp://127.0.0.1:5555
@end example

Multiple clients may connect to the stream using:
@example
ffplay zmq:tcp://127.0.0.1:5555
@end example

Streaming to multiple clients is implemented using a ZeroMQ Pub-Sub pattern.
The server side binds to a port and publishes data. Clients connect to the
server (via IP address/port) and subscribe to the stream. The order in which
the server and client start generally does not matter.

ffmpeg must be compiled with the --enable-libzmq option to support
this protocol.

Options can be set on the @command{ffmpeg}/@command{ffplay} command
line. The following options are supported:

@table @option

@item pkt_size
Forces the maximum packet size for sending/receiving data. The default value is
131,072 bytes. On the server side, this sets the maximum size of sent packets
via ZeroMQ. On the clients, it sets an internal buffer size for receiving
packets. Note that pkt_size on the clients should be equal to or greater than
pkt_size on the server. Otherwise the received message may be truncated causing
decoding errors.

@end table

@c man end PROTOCOLS
