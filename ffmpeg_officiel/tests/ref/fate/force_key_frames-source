#tb 0: 1/25
#media_type 0: video
#codec_id 0: mpeg2video
#dimensions 0: 2x2
#sar 0: 0/1
0,         -1,          0,        1,       57, 0x7db00eb7, S=1, Quality stats,        8, 0x05ec00be
0,          0,          1,        1,       24, 0x4f1c0660, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,          1,          2,        1,       24, 0x53dc06a0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,          2,          3,        1,       24, 0x589c06e0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,          3,          4,        1,       24, 0x4a700621, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,          4,          5,        1,       24, 0x4f300661, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,          5,          6,        1,       24, 0x53f006a1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,          6,          7,        1,       24, 0x58b006e1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,          7,          8,        1,       24, 0x4a840622, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,          8,          9,        1,       24, 0x4f440662, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,          9,         10,        1,       24, 0x540406a2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         10,         11,        1,       24, 0x58c406e2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         11,         12,        1,       24, 0x4a980623, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         12,         13,        1,       24, 0x4f580663, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         13,         14,        1,       24, 0x541806a3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         14,         15,        1,       24, 0x58d806e3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         15,         16,        1,       24, 0x4aac0624, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         16,         17,        1,       24, 0x4f6c0664, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         17,         18,        1,       24, 0x542c06a4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         18,         19,        1,       24, 0x58ec06e4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         19,         20,        1,       24, 0x4ac00625, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         20,         21,        1,       24, 0x4f800665, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         21,         22,        1,       24, 0x544006a5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         22,         23,        1,       24, 0x590006e5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         23,         24,        1,       24, 0x4ad40626, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         24,         25,        1,       24, 0x4f940666, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         25,         26,        1,       24, 0x545406a6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         26,         27,        1,       24, 0x591406e6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         27,         28,        1,       24, 0x4ae80627, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         28,         29,        1,       24, 0x4fa80667, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         29,         30,        1,       24, 0x546806a7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         30,         31,        1,       24, 0x592806e7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         31,         32,        1,       24, 0x4afc0628, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         32,         33,        1,       24, 0x4fbc0668, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         33,         34,        1,       24, 0x547c06a8, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         34,         35,        1,       24, 0x593c06e8, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         35,         36,        1,       24, 0x4b100629, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         36,         37,        1,       24, 0x4fd00669, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         37,         38,        1,       24, 0x549006a9, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         38,         39,        1,       24, 0x595006e9, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         39,         40,        1,       24, 0x4b24062a, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         40,         41,        1,       24, 0x4fe4066a, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         41,         42,        1,       24, 0x54a406aa, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         42,         43,        1,       24, 0x596406ea, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         43,         44,        1,       24, 0x4b38062b, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         44,         45,        1,       24, 0x4ff8066b, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         45,         46,        1,       24, 0x54b806ab, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         46,         47,        1,       24, 0x597806eb, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         47,         48,        1,       24, 0x4b4c062c, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         48,         49,        1,       24, 0x500c066c, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         49,         50,        1,       24, 0x54cc06ac, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         50,         51,        1,       24, 0x598c06ec, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         51,         52,        1,       24, 0x4b60062d, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         52,         53,        1,       24, 0x5020066d, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         53,         54,        1,       24, 0x54e006ad, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         54,         55,        1,       24, 0x59a006ed, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         55,         56,        1,       24, 0x4b74062e, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         56,         57,        1,       24, 0x5034066e, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         57,         58,        1,       24, 0x54f406ae, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         58,         59,        1,       24, 0x59b406ee, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         59,         60,        1,       24, 0x4b88062f, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         60,         61,        1,       24, 0x5048066f, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         61,         62,        1,       24, 0x550806af, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         62,         63,        1,       24, 0x59c806ef, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         63,         64,        1,       24, 0x4b9c0630, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         64,         65,        1,       24, 0x505c0670, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         65,         66,        1,       24, 0x551c06b0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         66,         67,        1,       24, 0x59dc06f0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         67,         68,        1,       24, 0x4bb00631, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         68,         69,        1,       24, 0x50700671, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         69,         70,        1,       24, 0x553006b1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         70,         71,        1,       24, 0x59f006f1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         71,         72,        1,       24, 0x4bc40632, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         72,         73,        1,       24, 0x50840672, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         73,         74,        1,       24, 0x554406b2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         74,         75,        1,       24, 0x5a0406f2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         75,         76,        1,       24, 0x4bd80633, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         76,         77,        1,       24, 0x50980673, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         77,         78,        1,       24, 0x555806b3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         78,         79,        1,       24, 0x5a1806f3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         79,         80,        1,       24, 0x4bec0634, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         80,         81,        1,       24, 0x50ac0674, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         81,         82,        1,       24, 0x556c06b4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         82,         83,        1,       24, 0x5a2c06f4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         83,         84,        1,       24, 0x4c000635, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         84,         85,        1,       24, 0x50c00675, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         85,         86,        1,       24, 0x558006b5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         86,         87,        1,       24, 0x5a4006f5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         87,         88,        1,       24, 0x4c140636, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         88,         89,        1,       24, 0x50d40676, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         89,         90,        1,       24, 0x559406b6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         90,         91,        1,       24, 0x5a5406f6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         91,         92,        1,       24, 0x4c280637, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         92,         93,        1,       24, 0x50e80677, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         93,         94,        1,       24, 0x55a806b7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         94,         95,        1,       24, 0x5a6806f7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         95,         96,        1,       24, 0x4c3c0638, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         96,         97,        1,       24, 0x50fc0678, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         97,         98,        1,       24, 0x55bc06b8, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         98,         99,        1,       24, 0x5a7c06f8, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,         99,        100,        1,       24, 0x4c500639, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        100,        101,        1,       24, 0x51100679, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        101,        102,        1,       24, 0x55d006b9, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        102,        103,        1,       24, 0x5a9006f9, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        103,        104,        1,       24, 0x4c64063a, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        104,        105,        1,       24, 0x5124067a, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        105,        106,        1,       24, 0x55e406ba, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        106,        107,        1,       24, 0x5aa406fa, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        107,        108,        1,       57, 0x85a40efb, S=1, Quality stats,        8, 0x05ec00be
0,        108,        109,        1,       24, 0x4f1c0660, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        109,        110,        1,       24, 0x53dc06a0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        110,        111,        1,       24, 0x589c06e0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        111,        112,        1,       24, 0x4a700621, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        112,        113,        1,       24, 0x4f300661, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        113,        114,        1,       24, 0x53f006a1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        114,        115,        1,       24, 0x58b006e1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        115,        116,        1,       24, 0x4a840622, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        116,        117,        1,       24, 0x4f440662, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        117,        118,        1,       24, 0x540406a2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        118,        119,        1,       24, 0x58c406e2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        119,        120,        1,       24, 0x4a980623, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        120,        121,        1,       24, 0x4f580663, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        121,        122,        1,       24, 0x541806a3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        122,        123,        1,       24, 0x58d806e3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        123,        124,        1,       24, 0x4aac0624, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        124,        125,        1,       24, 0x4f6c0664, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        125,        126,        1,       24, 0x542c06a4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        126,        127,        1,       24, 0x58ec06e4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        127,        128,        1,       24, 0x4ac00625, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        128,        129,        1,       24, 0x4f800665, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        129,        130,        1,       24, 0x544006a5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        130,        131,        1,       24, 0x590006e5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        131,        132,        1,       24, 0x4ad40626, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        132,        133,        1,       24, 0x4f940666, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        133,        134,        1,       24, 0x545406a6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        134,        135,        1,       24, 0x591406e6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        135,        136,        1,       24, 0x4ae80627, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        136,        137,        1,       24, 0x4fa80667, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        137,        138,        1,       24, 0x546806a7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        138,        139,        1,       24, 0x592806e7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        139,        140,        1,       24, 0x4afc0628, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        140,        141,        1,       24, 0x4fbc0668, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        141,        142,        1,       24, 0x547c06a8, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        142,        143,        1,       24, 0x593c06e8, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        143,        144,        1,       24, 0x4b100629, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        144,        145,        1,       24, 0x4fd00669, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        145,        146,        1,       24, 0x549006a9, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        146,        147,        1,       24, 0x595006e9, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        147,        148,        1,       24, 0x4b24062a, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        148,        149,        1,       24, 0x4fe4066a, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        149,        150,        1,       24, 0x54a406aa, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        150,        151,        1,       24, 0x596406ea, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        151,        152,        1,       24, 0x4b38062b, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        152,        153,        1,       24, 0x4ff8066b, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        153,        154,        1,       24, 0x54b806ab, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        154,        155,        1,       24, 0x597806eb, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        155,        156,        1,       24, 0x4b4c062c, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        156,        157,        1,       24, 0x500c066c, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        157,        158,        1,       24, 0x54cc06ac, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        158,        159,        1,       24, 0x598c06ec, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        159,        160,        1,       24, 0x4b60062d, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        160,        161,        1,       24, 0x5020066d, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        161,        162,        1,       24, 0x54e006ad, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        162,        163,        1,       24, 0x59a006ed, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        163,        164,        1,       24, 0x4b74062e, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        164,        165,        1,       24, 0x5034066e, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        165,        166,        1,       24, 0x54f406ae, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        166,        167,        1,       24, 0x59b406ee, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        167,        168,        1,       24, 0x4b88062f, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        168,        169,        1,       24, 0x5048066f, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        169,        170,        1,       24, 0x550806af, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        170,        171,        1,       24, 0x59c806ef, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        171,        172,        1,       24, 0x4b9c0630, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        172,        173,        1,       24, 0x505c0670, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        173,        174,        1,       24, 0x551c06b0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        174,        175,        1,       24, 0x59dc06f0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        175,        176,        1,       24, 0x4bb00631, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        176,        177,        1,       24, 0x50700671, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        177,        178,        1,       24, 0x553006b1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        178,        179,        1,       24, 0x59f006f1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        179,        180,        1,       24, 0x4bc40632, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        180,        181,        1,       24, 0x50840672, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        181,        182,        1,       24, 0x554406b2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        182,        183,        1,       24, 0x5a0406f2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        183,        184,        1,       24, 0x4bd80633, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        184,        185,        1,       24, 0x50980673, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        185,        186,        1,       24, 0x555806b3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        186,        187,        1,       24, 0x5a1806f3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        187,        188,        1,       24, 0x4bec0634, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        188,        189,        1,       24, 0x50ac0674, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        189,        190,        1,       24, 0x556c06b4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        190,        191,        1,       24, 0x5a2c06f4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        191,        192,        1,       24, 0x4c000635, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        192,        193,        1,       24, 0x50c00675, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        193,        194,        1,       24, 0x558006b5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        194,        195,        1,       24, 0x5a4006f5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        195,        196,        1,       24, 0x4c140636, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        196,        197,        1,       24, 0x50d40676, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        197,        198,        1,       24, 0x559406b6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        198,        199,        1,       24, 0x5a5406f6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        199,        200,        1,       24, 0x4c280637, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        200,        201,        1,       24, 0x50e80677, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        201,        202,        1,       24, 0x55a806b7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        202,        203,        1,       24, 0x5a6806f7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        203,        204,        1,       24, 0x4c3c0638, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        204,        205,        1,       24, 0x50fc0678, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        205,        206,        1,       24, 0x55bc06b8, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        206,        207,        1,       24, 0x5a7c06f8, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        207,        208,        1,       24, 0x4c500639, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        208,        209,        1,       24, 0x51100679, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        209,        210,        1,       24, 0x55d006b9, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        210,        211,        1,       24, 0x5a9006f9, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        211,        212,        1,       24, 0x4c64063a, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        212,        213,        1,       24, 0x5124067a, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        213,        214,        1,       24, 0x55e406ba, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        214,        215,        1,       24, 0x5aa406fa, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        215,        216,        1,       24, 0x4c78063b, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        216,        217,        1,       24, 0x5138067b, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        217,        218,        1,       24, 0x55f806bb, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        218,        219,        1,       24, 0x5ab806fb, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        219,        220,        1,       24, 0x4c8c063c, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        220,        221,        1,       24, 0x514c067c, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        221,        222,        1,       24, 0x560c06bc, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        222,        223,        1,       24, 0x5acc06fc, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        223,        224,        1,       24, 0x4ca0063d, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        224,        225,        1,       24, 0x5160067d, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        225,        226,        1,       24, 0x562006bd, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        226,        227,        1,       24, 0x5ae006fd, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        227,        228,        1,       24, 0x4cb4063e, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        228,        229,        1,       24, 0x5174067e, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        229,        230,        1,       24, 0x563406be, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        230,        231,        1,       24, 0x5af406fe, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        231,        232,        1,       24, 0x4cc8063f, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        232,        233,        1,       24, 0x5188067f, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        233,        234,        1,       24, 0x564806bf, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        234,        235,        1,       24, 0x5b0806ff, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        235,        236,        1,       24, 0x4cdc0640, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        236,        237,        1,       24, 0x519c0680, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        237,        238,        1,       24, 0x565c06c0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        238,        239,        1,       24, 0x5b1c0700, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        239,        240,        1,       24, 0x4cf00641, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        240,        241,        1,       24, 0x51b00681, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        241,        242,        1,       24, 0x567006c1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        242,        243,        1,       24, 0x5b300701, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        243,        244,        1,       24, 0x4d040642, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        244,        245,        1,       24, 0x51c40682, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        245,        246,        1,       24, 0x568406c2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        246,        247,        1,       24, 0x5b440702, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        247,        248,        1,       24, 0x4d180643, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        248,        249,        1,       24, 0x51d80683, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        249,        250,        1,       24, 0x569806c3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        250,        251,        1,       24, 0x5b580703, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        251,        252,        1,       24, 0x4d2c0644, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        252,        253,        1,       24, 0x51ec0684, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        253,        254,        1,       24, 0x56ac06c4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        254,        255,        1,       24, 0x5b6c0704, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        255,        256,        1,       24, 0x4d400645, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        256,        257,        1,       24, 0x52000685, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        257,        258,        1,       24, 0x56c006c5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        258,        259,        1,       24, 0x5b800705, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        259,        260,        1,       24, 0x4d540646, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        260,        261,        1,       24, 0x52140686, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        261,        262,        1,       24, 0x56d406c6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        262,        263,        1,       24, 0x5b940706, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        263,        264,        1,       24, 0x4d680647, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        264,        265,        1,       24, 0x52280687, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        265,        266,        1,       24, 0x56e806c7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        266,        267,        1,       24, 0x5ba80707, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        267,        268,        1,       24, 0x4d7c0648, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        268,        269,        1,       24, 0x523c0688, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        269,        270,        1,       24, 0x56fc06c8, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        270,        271,        1,       24, 0x5bbc0708, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        271,        272,        1,       24, 0x4d900649, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        272,        273,        1,       24, 0x52500689, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        273,        274,        1,       24, 0x571006c9, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        274,        275,        1,       24, 0x5bd00709, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        275,        276,        1,       24, 0x4da4064a, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        276,        277,        1,       24, 0x5264068a, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        277,        278,        1,       24, 0x572406ca, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        278,        279,        1,       24, 0x5be4070a, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        279,        280,        1,       24, 0x4db8064b, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        280,        281,        1,       24, 0x5278068b, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        281,        282,        1,       24, 0x573806cb, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        282,        283,        1,       24, 0x5bf8070b, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        283,        284,        1,       24, 0x4dcc064c, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        284,        285,        1,       24, 0x528c068c, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        285,        286,        1,       24, 0x574c06cc, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        286,        287,        1,       24, 0x5c0c070c, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        287,        288,        1,       24, 0x4de0064d, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        288,        289,        1,       24, 0x52a0068d, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        289,        290,        1,       24, 0x576006cd, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        290,        291,        1,       24, 0x5c20070d, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        291,        292,        1,       24, 0x4df4064e, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        292,        293,        1,       24, 0x52b4068e, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        293,        294,        1,       24, 0x577406ce, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        294,        295,        1,       24, 0x5c34070e, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        295,        296,        1,       24, 0x4e08064f, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        296,        297,        1,       24, 0x52c8068f, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        297,        298,        1,       24, 0x578806cf, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        298,        299,        1,       24, 0x5c48070f, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        299,        300,        1,       24, 0x4e1c0650, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        300,        301,        1,       24, 0x52dc0690, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        301,        302,        1,       24, 0x579c06d0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        302,        303,        1,       24, 0x5c5c0710, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        303,        304,        1,       24, 0x4e300651, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        304,        305,        1,       24, 0x52f00691, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        305,        306,        1,       24, 0x57b006d1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        306,        307,        1,       24, 0x5c700711, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        307,        308,        1,       24, 0x4e440652, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        308,        309,        1,       24, 0x53040692, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        309,        310,        1,       24, 0x57c406d2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        310,        311,        1,       24, 0x5c840712, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        311,        312,        1,       24, 0x4e580653, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        312,        313,        1,       24, 0x53180693, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        313,        314,        1,       24, 0x57d806d3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        314,        315,        1,       24, 0x5c980713, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        315,        316,        1,       24, 0x4e6c0654, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        316,        317,        1,       24, 0x532c0694, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        317,        318,        1,       24, 0x57ec06d4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        318,        319,        1,       24, 0x5cac0714, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        319,        320,        1,       24, 0x4e800655, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        320,        321,        1,       24, 0x53400695, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        321,        322,        1,       24, 0x580006d5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        322,        323,        1,       24, 0x5cc00715, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        323,        324,        1,       24, 0x4e940656, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        324,        325,        1,       24, 0x53540696, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        325,        326,        1,       24, 0x581406d6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        326,        327,        1,       24, 0x5cd40716, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        327,        328,        1,       24, 0x4ea80657, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        328,        329,        1,       24, 0x53680697, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        329,        330,        1,       24, 0x582806d7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        330,        331,        1,       24, 0x5ce80717, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        331,        332,        1,       24, 0x4ebc0658, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        332,        333,        1,       24, 0x537c0698, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        333,        334,        1,       24, 0x583c06d8, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        334,        335,        1,       24, 0x5cfc0718, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        335,        336,        1,       24, 0x4ed00659, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        336,        337,        1,       57, 0x899c0f1e, S=1, Quality stats,        8, 0x05ec00be
0,        337,        338,        1,       24, 0x4f1c0660, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        338,        339,        1,       24, 0x53dc06a0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        339,        340,        1,       24, 0x589c06e0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        340,        341,        1,       24, 0x4a700621, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        341,        342,        1,       24, 0x4f300661, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        342,        343,        1,       24, 0x53f006a1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        343,        344,        1,       24, 0x58b006e1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        344,        345,        1,       24, 0x4a840622, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        345,        346,        1,       24, 0x4f440662, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        346,        347,        1,       24, 0x540406a2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        347,        348,        1,       24, 0x58c406e2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        348,        349,        1,       24, 0x4a980623, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        349,        350,        1,       24, 0x4f580663, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        350,        351,        1,       24, 0x541806a3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        351,        352,        1,       24, 0x58d806e3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        352,        353,        1,       24, 0x4aac0624, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        353,        354,        1,       24, 0x4f6c0664, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        354,        355,        1,       24, 0x542c06a4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        355,        356,        1,       24, 0x58ec06e4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        356,        357,        1,       24, 0x4ac00625, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        357,        358,        1,       24, 0x4f800665, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        358,        359,        1,       24, 0x544006a5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        359,        360,        1,       24, 0x590006e5, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        360,        361,        1,       24, 0x4ad40626, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        361,        362,        1,       24, 0x4f940666, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        362,        363,        1,       24, 0x545406a6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        363,        364,        1,       24, 0x591406e6, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        364,        365,        1,       24, 0x4ae80627, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        365,        366,        1,       24, 0x4fa80667, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        366,        367,        1,       24, 0x546806a7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        367,        368,        1,       24, 0x592806e7, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        368,        369,        1,       57, 0x9b930fc1, S=1, Quality stats,        8, 0x05ec00be
0,        369,        370,        1,       24, 0x4f1c0660, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        370,        371,        1,       24, 0x53dc06a0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        371,        372,        1,       24, 0x589c06e0, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        372,        373,        1,       24, 0x4a700621, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        373,        374,        1,       24, 0x4f300661, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        374,        375,        1,       24, 0x53f006a1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        375,        376,        1,       24, 0x58b006e1, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        376,        377,        1,       24, 0x4a840622, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        377,        378,        1,       24, 0x4f440662, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        378,        379,        1,       24, 0x540406a2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        379,        380,        1,       24, 0x58c406e2, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        380,        381,        1,       24, 0x4a980623, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        381,        382,        1,       24, 0x4f580663, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        382,        383,        1,       24, 0x541806a3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        383,        384,        1,       24, 0x58d806e3, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        384,        385,        1,       24, 0x4aac0624, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        385,        386,        1,       24, 0x4f6c0664, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        386,        387,        1,       24, 0x542c06a4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        387,        388,        1,       24, 0x58ec06e4, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        388,        389,        1,       24, 0x4ac00625, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        389,        390,        1,       24, 0x4f800665, F=0x0, S=1, Quality stats,        8, 0x076800ee
0,        390,        391,        1,       24, 0x544006a5, F=0x0, S=1, Quality stats,        8, 0x076800ee
