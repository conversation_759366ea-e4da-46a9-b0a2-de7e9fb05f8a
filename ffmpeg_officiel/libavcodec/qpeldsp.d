libavcodec/qpeldsp.o: libavcodec/qpeldsp.c config.h config_components.h \
 libavutil/attributes.h libavcodec/copy_block.h libavutil/intreadwrite.h \
 libavutil/avconfig.h libavutil/attributes.h libavutil/bswap.h \
 libavutil/x86/bswap.h libavutil/x86/intreadwrite.h libavcodec/qpeldsp.h \
 libavcodec/diracdsp.h libavcodec/hpel_template.c libavcodec/pixels.h \
 libavcodec/bit_depth_template.c libavcodec/mathops.h \
 libavutil/attributes_internal.h libavutil/common.h libavutil/error.h \
 libavutil/macros.h libavutil/version.h libavutil/intmath.h \
 libavutil/x86/intmath.h libavutil/internal.h libavutil/libm.h \
 libavutil/mathematics.h libavutil/rational.h libavutil/intfloat.h \
 libavcodec/x86/mathops.h libavutil/x86/asm.h libavcodec/rnd_avg.h \
 libavcodec/pel_template.c libavcodec/qpel_template.c
