libavcodec/vp8.o: libavcodec/vp8.c config_components.h libavutil/mem.h \
 libavutil/attributes.h libavutil/mem_internal.h config.h \
 libavutil/macros.h libavutil/avconfig.h libavcodec/avcodec.h \
 libavutil/samplefmt.h libavutil/attributes.h libavutil/avutil.h \
 libavutil/common.h libavutil/error.h libavutil/version.h \
 libavutil/intmath.h libavutil/x86/intmath.h libavutil/internal.h \
 libavutil/libm.h libavutil/mathematics.h libavutil/rational.h \
 libavutil/intfloat.h libavutil/log.h libavutil/pixfmt.h \
 libavutil/buffer.h libavutil/channel_layout.h libavutil/dict.h \
 libavutil/frame.h libavutil/avutil.h libavutil/buffer.h \
 libavutil/channel_layout.h libavutil/dict.h libavutil/samplefmt.h \
 libavutil/log.h libavutil/pixfmt.h libavutil/rational.h \
 libavcodec/codec.h libavutil/hwcontext.h libavutil/frame.h \
 libavcodec/codec_id.h libavcodec/version_major.h \
 libavcodec/version_major.h libavcodec/codec_id.h libavcodec/defs.h \
 libavcodec/packet.h libavutil/version.h libavcodec/version_major.h \
 libavcodec/codec_internal.h libavcodec/decode.h \
 libavcodec/hwaccel_internal.h libavutil/refstruct.h \
 libavcodec/hwconfig.h libavcodec/hwaccels.h libavcodec/mathops.h \
 libavutil/attributes_internal.h libavutil/common.h \
 libavcodec/x86/mathops.h libavutil/x86/asm.h libavcodec/progressframe.h \
 libavcodec/thread.h libavcodec/vp8.h libavutil/thread.h \
 libavcodec/h264pred.h libavcodec/videodsp.h libavcodec/vp8dsp.h \
 libavcodec/vpx_rac.h libavcodec/bytestream.h libavutil/avassert.h \
 libavutil/intreadwrite.h libavutil/bswap.h libavutil/x86/bswap.h \
 libavutil/x86/intreadwrite.h libavcodec/x86/vpx_arith.h \
 libavcodec/vp89_rac.h libavcodec/vp8data.h
