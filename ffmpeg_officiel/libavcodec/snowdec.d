libavcodec/snowdec.o: libavcodec/snowdec.c libavutil/emms.h config.h \
 libavutil/attributes.h libavutil/intmath.h libavutil/attributes.h \
 libavutil/x86/intmath.h libavutil/log.h libavutil/version.h \
 libavutil/macros.h libavutil/avconfig.h libavutil/mem.h \
 libavcodec/avcodec.h libavutil/samplefmt.h libavutil/avutil.h \
 libavutil/common.h libavutil/error.h libavutil/intmath.h \
 libavutil/internal.h libavutil/libm.h libavutil/mathematics.h \
 libavutil/rational.h libavutil/intfloat.h libavutil/log.h \
 libavutil/pixfmt.h libavutil/buffer.h libavutil/channel_layout.h \
 libavutil/dict.h libavutil/frame.h libavutil/avutil.h libavutil/buffer.h \
 libavutil/channel_layout.h libavutil/dict.h libavutil/samplefmt.h \
 libavutil/pixfmt.h libavutil/rational.h libavcodec/codec.h \
 libavutil/hwcontext.h libavutil/frame.h libavcodec/codec_id.h \
 libavcodec/version_major.h libavcodec/version_major.h \
 libavcodec/codec_id.h libavcodec/defs.h libavcodec/packet.h \
 libavutil/version.h libavcodec/version_major.h \
 libavcodec/codec_internal.h libavcodec/decode.h libavcodec/snow_dwt.h \
 libavcodec/snow.h libavutil/motion_vector.h libavcodec/hpeldsp.h \
 libavcodec/rangecoder.h libavutil/avassert.h libavcodec/mathops.h \
 libavutil/attributes_internal.h libavutil/common.h \
 libavcodec/x86/mathops.h libavutil/x86/asm.h libavcodec/h264qpel.h \
 libavcodec/qpeldsp.h libavcodec/videodsp.h
