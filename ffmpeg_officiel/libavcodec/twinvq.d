libavcodec/twinvq.o: libavcodec/twinvq.c libavutil/channel_layout.h \
 libavutil/version.h libavutil/macros.h libavutil/avconfig.h \
 libavutil/attributes.h libavutil/float_dsp.h libavutil/mem.h \
 libavcodec/avcodec.h libavutil/samplefmt.h libavutil/attributes.h \
 libavutil/avutil.h libavutil/common.h libavutil/error.h config.h \
 libavutil/intmath.h libavutil/x86/intmath.h libavutil/internal.h \
 libavutil/libm.h libavutil/mathematics.h libavutil/rational.h \
 libavutil/intfloat.h libavutil/log.h libavutil/pixfmt.h \
 libavutil/buffer.h libavutil/dict.h libavutil/frame.h libavutil/avutil.h \
 libavutil/buffer.h libavutil/channel_layout.h libavutil/dict.h \
 libavutil/samplefmt.h libavutil/log.h libavutil/pixfmt.h \
 libavutil/rational.h libavcodec/codec.h libavutil/hwcontext.h \
 libavutil/frame.h libavcodec/codec_id.h libavcodec/version_major.h \
 libavcodec/version_major.h libavcodec/codec_id.h libavcodec/defs.h \
 libavcodec/packet.h libavutil/version.h libavcodec/version_major.h \
 libavcodec/decode.h libavcodec/lsp.h libavcodec/metasound_twinvq_data.h \
 libavcodec/twinvq.h libavutil/attributes_internal.h libavutil/tx.h \
 libavutil/common.h libavcodec/sinewin.h libavutil/mem_internal.h
