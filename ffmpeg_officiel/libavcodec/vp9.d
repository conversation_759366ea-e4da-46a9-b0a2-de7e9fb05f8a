libavcodec/vp9.o: libavcodec/vp9.c config_components.h \
 libavcodec/avcodec.h libavutil/samplefmt.h libavutil/attributes.h \
 libavutil/avutil.h libavutil/common.h libavutil/attributes.h \
 libavutil/error.h libavutil/macros.h libavutil/avconfig.h \
 libavutil/version.h config.h libavutil/intmath.h libavutil/x86/intmath.h \
 libavutil/internal.h libavutil/libm.h libavutil/mathematics.h \
 libavutil/rational.h libavutil/intfloat.h libavutil/log.h \
 libavutil/pixfmt.h libavutil/buffer.h libavutil/channel_layout.h \
 libavutil/dict.h libavutil/frame.h libavutil/avutil.h libavutil/buffer.h \
 libavutil/channel_layout.h libavutil/dict.h libavutil/samplefmt.h \
 libavutil/log.h libavutil/pixfmt.h libavutil/rational.h \
 libavcodec/codec.h libavutil/hwcontext.h libavutil/frame.h \
 libavcodec/codec_id.h libavcodec/version_major.h \
 libavcodec/version_major.h libavcodec/codec_id.h libavcodec/defs.h \
 libavcodec/packet.h libavutil/version.h libavcodec/version_major.h \
 libavcodec/codec_internal.h libavcodec/decode.h libavcodec/get_bits.h \
 libavutil/common.h libavutil/intreadwrite.h libavutil/bswap.h \
 libavutil/x86/bswap.h libavutil/x86/intreadwrite.h libavutil/avassert.h \
 libavcodec/mathops.h libavutil/attributes_internal.h \
 libavcodec/x86/mathops.h libavutil/x86/asm.h libavcodec/vlc.h \
 libavutil/macros.h libavcodec/hwaccel_internal.h libavutil/refstruct.h \
 libavcodec/hwconfig.h libavcodec/hwaccels.h libavcodec/profiles.h \
 libavutil/opt.h libavcodec/progressframe.h libavcodec/thread.h \
 libavcodec/pthread_internal.h libavcodec/videodsp.h \
 libavcodec/vp89_rac.h libavcodec/vpx_rac.h libavcodec/bytestream.h \
 libavcodec/x86/vpx_arith.h libavcodec/vp9.h libavcodec/vp9data.h \
 libavcodec/vp9dec.h libavutil/mem_internal.h libavutil/thread.h \
 libavcodec/vp9dsp.h libavcodec/vp9.h libavcodec/vp9shared.h \
 libavcodec/cbs_vp9.h libavcodec/cbs.h libavcodec/codec_par.h \
 libavutil/mem.h libavutil/pixdesc.h libavutil/video_enc_params.h
