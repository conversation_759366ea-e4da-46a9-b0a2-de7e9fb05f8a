fftools/thread_queue.o: fftools/thread_queue.c libavutil/avassert.h \
 libavutil/attributes.h libavutil/log.h libavutil/version.h \
 libavutil/macros.h libavutil/avconfig.h libavutil/container_fifo.h \
 libavutil/error.h libavutil/fifo.h libavutil/frame.h libavutil/avutil.h \
 libavutil/common.h libavutil/error.h libavutil/mem.h \
 libavutil/rational.h libavutil/mathematics.h libavutil/intfloat.h \
 libavutil/pixfmt.h libavutil/buffer.h libavutil/channel_layout.h \
 libavutil/dict.h libavutil/samplefmt.h libavutil/intreadwrite.h \
 libavutil/bswap.h libavutil/mem.h libavutil/thread.h config.h \
 libavcodec/packet.h libavutil/attributes.h libavutil/buffer.h \
 libavutil/dict.h libavutil/rational.h libavutil/version.h \
 libavcodec/version_major.h fftools/thread_queue.h
