fftools/ffmpeg.o: fftools/ffmpeg.c config.h libavutil/bprint.h \
 libavutil/attributes.h libavutil/avstring.h libavutil/dict.h \
 libavutil/mem.h libavutil/time.h libavformat/avformat.h \
 libavcodec/codec_par.h libavutil/avutil.h libavutil/common.h \
 libavutil/error.h libavutil/macros.h libavutil/avconfig.h \
 libavutil/version.h libavutil/mem.h libavutil/rational.h \
 libavutil/mathematics.h libavutil/intfloat.h libavutil/log.h \
 libavutil/pixfmt.h libavutil/channel_layout.h libavutil/rational.h \
 libavutil/pixfmt.h libavcodec/codec_id.h libavutil/samplefmt.h \
 libavcodec/version_major.h libavcodec/defs.h libavcodec/packet.h \
 libavutil/attributes.h libavutil/buffer.h libavutil/version.h \
 libavcodec/version_major.h libavcodec/defs.h libavcodec/packet.h \
 libavutil/log.h libavformat/avio.h libavformat/version_major.h \
 libavformat/version.h libavformat/version_major.h libavutil/frame.h \
 libavutil/avutil.h libavutil/buffer.h libavutil/channel_layout.h \
 libavutil/dict.h libavutil/samplefmt.h libavcodec/codec.h \
 libavutil/hwcontext.h libavutil/frame.h libavcodec/codec_id.h \
 libavdevice/avdevice.h libavdevice/version_major.h libavdevice/version.h \
 libavutil/opt.h fftools/cmdutils.h libavcodec/avcodec.h \
 libavcodec/codec.h libavcodec/version.h libavcodec/codec_desc.h \
 libavcodec/codec_par.h libavfilter/avfilter.h \
 libavfilter/version_major.h libavfilter/version.h \
 libavfilter/version_major.h libswscale/swscale.h \
 libswscale/version_major.h libswscale/version.h fftools/ffmpeg.h \
 fftools/ffmpeg_sched.h fftools/ffmpeg_utils.h libavutil/common.h \
 fftools/sync_queue.h libavformat/avio.h libavcodec/bsf.h \
 libavutil/eval.h libavutil/fifo.h libavutil/thread.h \
 libavutil/threadmessage.h libswresample/swresample.h \
 libswresample/version_major.h libswresample/version.h \
 libswresample/version_major.h fftools/graph/graphprint.h \
 fftools/ffmpeg.h
