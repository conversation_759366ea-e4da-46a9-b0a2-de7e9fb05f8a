fftools/ffprobe.o: fftools/ffprobe.c config.h libavutil/ffversion.h \
 libavformat/avformat.h libavcodec/codec_par.h libavutil/avutil.h \
 libavutil/common.h libavutil/attributes.h libavutil/error.h \
 libavutil/macros.h libavutil/avconfig.h libavutil/version.h \
 libavutil/mem.h libavutil/rational.h libavutil/mathematics.h \
 libavutil/intfloat.h libavutil/log.h libavutil/pixfmt.h \
 libavutil/channel_layout.h libavutil/rational.h libavutil/pixfmt.h \
 libavcodec/codec_id.h libavutil/samplefmt.h libavcodec/version_major.h \
 libavcodec/defs.h libavcodec/packet.h libavutil/attributes.h \
 libavutil/buffer.h libavutil/dict.h libavutil/version.h \
 libavcodec/version_major.h libavcodec/defs.h libavcodec/packet.h \
 libavutil/log.h libavformat/avio.h libavformat/version_major.h \
 libavformat/version.h libavformat/version_major.h libavutil/frame.h \
 libavutil/avutil.h libavutil/buffer.h libavutil/channel_layout.h \
 libavutil/dict.h libavutil/samplefmt.h libavcodec/codec.h \
 libavutil/hwcontext.h libavutil/frame.h libavcodec/codec_id.h \
 libavcodec/avcodec.h libavcodec/codec.h libavcodec/version.h \
 libavcodec/codec_desc.h libavcodec/codec_par.h libavcodec/version.h \
 libavutil/ambient_viewing_environment.h libavutil/avassert.h \
 libavutil/avstring.h libavutil/bprint.h libavutil/avstring.h \
 libavutil/display.h libavutil/film_grain_params.h \
 libavutil/hdr_dynamic_metadata.h libavutil/iamf.h libavutil/avassert.h \
 libavutil/mastering_display_metadata.h \
 libavutil/hdr_dynamic_vivid_metadata.h libavutil/dovi_meta.h \
 libavutil/csp.h libavutil/mem.h libavutil/opt.h libavutil/pixdesc.h \
 libavutil/spherical.h libavutil/stereo3d.h libavutil/intreadwrite.h \
 libavutil/bswap.h libavutil/libm.h libavutil/parseutils.h \
 libavutil/timecode.h libavutil/timestamp.h libavdevice/avdevice.h \
 libavdevice/version_major.h libavdevice/version.h libavdevice/version.h \
 libswscale/swscale.h libswscale/version_major.h libswscale/version.h \
 libswscale/version.h libswresample/swresample.h \
 libswresample/version_major.h libswresample/version.h \
 libswresample/version_major.h libavfilter/version.h \
 libavfilter/version_major.h fftools/textformat/avtextformat.h \
 libavformat/avio.h libavutil/hash.h fftools/textformat/avtextwriters.h \
 fftools/cmdutils.h libavfilter/avfilter.h libavfilter/version_major.h \
 fftools/opt_common.h libavutil/thread.h
