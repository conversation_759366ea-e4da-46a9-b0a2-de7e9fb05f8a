fftools/cmdutils.o: fftools/cmdutils.c config.h compat/va_copy.h \
 libavformat/avformat.h libavcodec/codec_par.h libavutil/avutil.h \
 libavutil/common.h libavutil/attributes.h libavutil/error.h \
 libavutil/macros.h libavutil/avconfig.h libavutil/version.h \
 libavutil/mem.h libavutil/rational.h libavutil/mathematics.h \
 libavutil/intfloat.h libavutil/log.h libavutil/pixfmt.h \
 libavutil/channel_layout.h libavutil/rational.h libavutil/pixfmt.h \
 libavcodec/codec_id.h libavutil/samplefmt.h libavcodec/version_major.h \
 libavcodec/defs.h libavcodec/packet.h libavutil/attributes.h \
 libavutil/buffer.h libavutil/dict.h libavutil/version.h \
 libavcodec/version_major.h libavcodec/defs.h libavcodec/packet.h \
 libavutil/log.h libavformat/avio.h libavformat/version_major.h \
 libavformat/version.h libavformat/version_major.h libavutil/frame.h \
 libavutil/avutil.h libavutil/buffer.h libavutil/channel_layout.h \
 libavutil/dict.h libavutil/samplefmt.h libavcodec/codec.h \
 libavutil/hwcontext.h libavutil/frame.h libavcodec/codec_id.h \
 libswscale/swscale.h libswscale/version_major.h libswscale/version.h \
 libswresample/swresample.h libswresample/version_major.h \
 libswresample/version.h libswresample/version_major.h \
 libavutil/avassert.h libavutil/avstring.h libavutil/bprint.h \
 libavutil/avstring.h libavutil/display.h libavutil/getenv_utf8.h \
 libavutil/libm.h libavutil/mem.h libavutil/parseutils.h libavutil/eval.h \
 libavutil/opt.h fftools/cmdutils.h libavcodec/avcodec.h \
 libavcodec/codec.h libavcodec/version.h libavcodec/codec_desc.h \
 libavcodec/codec_par.h libavfilter/avfilter.h \
 libavfilter/version_major.h libavfilter/version.h \
 libavfilter/version_major.h fftools/fopen_utf8.h fftools/opt_common.h
