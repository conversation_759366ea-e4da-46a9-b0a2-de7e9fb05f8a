#!/bin/sh
#
# Copyright (c) 2012 <PERSON>
# Copyright (c) 2013 T<PERSON><PERSON> "Timothy" Gu
#
# This file is part of FFmpeg.
#
# FFmpeg is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 2.1 of the License, or (at your option) any later version.
#
# FFmpeg is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
# See the GNU Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with FFmpeg; if not, write to the Free Software Foundation, Inc.,
# 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA

## Help
die() {
    cat <<EOF >&2
This script is used to generate Windows resources file for the FFmpeg libraries.
The output .rc file is to be compiled by windres(1). It is mainly useful for
FFmpeg developers to tweak and regenerate all resources files at once.

Usage: $0 <libname> <comment>

The script will output the file to '<libname>/<libname-without-lib>res.rc'.

Example: $0 libavcodec 'FFmpeg codecs library'
EOF
    exit 1
}

# Script to generate all:
# (to remove prefix '# ' and add 'tools/' as prefix: sed -r 's/^.{2}/tools\//')
# gen-rc libavutil     "FFmpeg utility library"
# gen-rc libavcodec    "FFmpeg codec library"
# gen-rc libavformat   "FFmpeg container format library"
# gen-rc libavdevice   "FFmpeg device handling library"
# gen-rc libavfilter   "FFmpeg audio/video filtering library"
# gen-rc libswscale    "FFmpeg image rescaling library"
# gen-rc libswresample "FFmpeg audio resampling library"

## Sanity checks and argument parsing
if test $# -lt 2 || test $# -gt 3; then
    die
fi

name=$1
shortname=${name#lib}
comment=$2
capname=`echo $name | awk '{print toupper($0)}'`
version=${capname}_VERSION

mkdir -p "$name"
output="$name/${shortname}res.rc"

## REAL magic
cat <<EOF > $output
/*
 * Windows resource file for $name
 *
 * Copyright (C) 2012 James Almer
 * Copyright (C) 2013 Tiancheng "Timothy" Gu
 *
 * This file is part of FFmpeg.
 *
 * FFmpeg is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * FFmpeg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with FFmpeg; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#include <windows.h>
#include "$name/version.h"
#include "libavutil/ffversion.h"
#include "config.h"

1 VERSIONINFO
FILEVERSION     ${version}_MAJOR, ${version}_MINOR, ${version}_MICRO, 0
PRODUCTVERSION  ${version}_MAJOR, ${version}_MINOR, ${version}_MICRO, 0
FILEFLAGSMASK   VS_FFI_FILEFLAGSMASK
FILEOS          VOS_NT_WINDOWS32
FILETYPE        VFT_DLL
{
    BLOCK "StringFileInfo"
    {
        BLOCK "040904B0"
        {
            VALUE "CompanyName",      "FFmpeg Project"
            VALUE "FileDescription",  "$comment"
            VALUE "FileVersion",      AV_STRINGIFY($version)
            VALUE "InternalName",     "$name"
            VALUE "LegalCopyright",   "Copyright (C) 2000-" AV_STRINGIFY(CONFIG_THIS_YEAR) " FFmpeg Project"
            VALUE "OriginalFilename", "$shortname" BUILDSUF "-" AV_STRINGIFY(${version}_MAJOR) SLIBSUF
            VALUE "ProductName",      "FFmpeg"
            VALUE "ProductVersion",   FFMPEG_VERSION
        }
    }

    BLOCK "VarFileInfo"
    {
        VALUE "Translation", 0x0409, 0x04B0
    }
}
EOF
