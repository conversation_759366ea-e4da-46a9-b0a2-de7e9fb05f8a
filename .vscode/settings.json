{"files.associations": {"chrono": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "cstdint": "cpp", "string_view": "cpp", "array": "cpp", "string": "cpp", "ranges": "cpp", "span": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "any": "cpp", "atomic": "cpp", "strstream": "cpp", "bit": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cfenv": "cpp", "charconv": "cpp", "cinttypes": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "source_location": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "format": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "semaphore": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stdfloat": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "valarray": "cpp", "variant": "cpp", "*.ipp": "cpp", "libvega_common.h": "c", "vegaff.h": "c", "vega_bqb_encoder.h": "c", "apivdec.h": "c", "vega_bqb_types.h": "c", "version_major.h": "c", "hevc_parse.h": "c", "hevc_sei.h": "c", "h2645_parse.h": "c", "hevc.h": "c", "ps.h": "c", "nal.h": "c", "avcodec.h": "c", "profiles.h": "c", "avcodec_internal.h": "c", "codec_internal.h": "c", "attributes.h": "c"}, "C_Cpp.errorSquiggles": "enabled"}